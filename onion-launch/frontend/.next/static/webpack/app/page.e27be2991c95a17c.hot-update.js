"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/../wrappers/UserPurchase.ts":
/*!***********************************!*\
  !*** ../wrappers/UserPurchase.ts ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   UserPurchase: () => (/* binding */ UserPurchase),\n/* harmony export */   userPurchaseConfigToCell: () => (/* binding */ userPurchaseConfigToCell)\n/* harmony export */ });\n/* harmony import */ var _ton_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @ton/core */ \"(app-pages-browser)/../node_modules/@ton/core/dist/index.js\");\n/* harmony import */ var _ton_core__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_ton_core__WEBPACK_IMPORTED_MODULE_0__);\n\nfunction userPurchaseConfigToCell(config) {\n    return (0,_ton_core__WEBPACK_IMPORTED_MODULE_0__.beginCell)().endCell();\n}\nclass UserPurchase {\n    static createFromAddress(address) {\n        return new UserPurchase(address);\n    }\n    static createFromConfig(config, code) {\n        let workchain = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 0;\n        const data = userPurchaseConfigToCell(config);\n        const init = {\n            code,\n            data\n        };\n        return new UserPurchase((0,_ton_core__WEBPACK_IMPORTED_MODULE_0__.contractAddress)(workchain, init), init);\n    }\n    async sendDeploy(provider, via, value) {\n        await provider.internal(via, {\n            value,\n            sendMode: _ton_core__WEBPACK_IMPORTED_MODULE_0__.SendMode.PAY_GAS_SEPARATELY,\n            body: (0,_ton_core__WEBPACK_IMPORTED_MODULE_0__.beginCell)().endCell()\n        });\n    }\n    // Send refund request\n    async sendRefund(provider, via, value, purchaseId) {\n        const body = (0,_ton_core__WEBPACK_IMPORTED_MODULE_0__.beginCell)().storeUint(0x31d5b5ac, 32) // Refund op code\n        .storeUint(purchaseId, 32) // purchase_id\n        .endCell();\n        await provider.internal(via, {\n            value,\n            sendMode: _ton_core__WEBPACK_IMPORTED_MODULE_0__.SendMode.PAY_GAS_SEPARATELY,\n            body\n        });\n    }\n    // Getter methods\n    async getTotalPurchased(provider) {\n        const result = await provider.get('total_purchased', []);\n        return result.stack.readBigNumber();\n    }\n    async getTotalPaid(provider) {\n        const result = await provider.get('total_paid', []);\n        return result.stack.readBigNumber();\n    }\n    async getPurchaseIdCounter(provider) {\n        const result = await provider.get('purchase_id_counter', []);\n        return result.stack.readNumber();\n    }\n    async getPurchaseDetails(provider, purchaseId) {\n        try {\n            const args = new _ton_core__WEBPACK_IMPORTED_MODULE_0__.TupleBuilder();\n            args.writeNumber(purchaseId);\n            args.writeCell((0,_ton_core__WEBPACK_IMPORTED_MODULE_0__.beginCell)().storeInt(0).endCell()); // Placeholder for additional args if needed\n            const result = await provider.get('purchase_details', args.build());\n            if (result.stack.remaining === 0) {\n                return null;\n            }\n            // Read the PurchaseRecord struct\n            const record = result.stack.readTuple();\n            return {\n                id: record.readNumber(),\n                user: record.readAddress(),\n                amount: record.readBigNumber(),\n                tokens: record.readBigNumber(),\n                timestamp: record.readNumber(),\n                currency: record.readNumber(),\n                purchase_method: record.readNumber(),\n                nonce: record.readBigNumber()\n            };\n        } catch (error) {\n            console.error('Error reading purchase details:', error);\n            return null;\n        }\n    }\n    async isRefunded(provider, purchaseId) {\n        try {\n            const args = new _ton_core__WEBPACK_IMPORTED_MODULE_0__.TupleBuilder();\n            args.writeNumber(purchaseId);\n            const result = await provider.get('is_refunded', args.build());\n            return result.stack.readBoolean();\n        } catch (error) {\n            console.error('Error checking refund status:', error);\n            return false;\n        }\n    }\n    async getSignatureVerifiedPurchases(provider) {\n        try {\n            const result = await provider.get('signature_verified_purchases', []);\n            return result.stack.readNumber();\n        } catch (error) {\n            console.error('Error reading signature verified purchases:', error);\n            return 0;\n        }\n    }\n    constructor(address, init){\n        this.address = address;\n        this.init = init;\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../wrappers/UserPurchase.ts\n"));

/***/ })

});