"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/../wrappers/UserPurchase.ts":
/*!***********************************!*\
  !*** ../wrappers/UserPurchase.ts ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   UserPurchase: () => (/* binding */ UserPurchase),\n/* harmony export */   userPurchaseConfigToCell: () => (/* binding */ userPurchaseConfigToCell)\n/* harmony export */ });\n/* harmony import */ var _ton_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @ton/core */ \"(app-pages-browser)/../node_modules/@ton/core/dist/index.js\");\n/* harmony import */ var _ton_core__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_ton_core__WEBPACK_IMPORTED_MODULE_0__);\n\nfunction userPurchaseConfigToCell(config) {\n    return (0,_ton_core__WEBPACK_IMPORTED_MODULE_0__.beginCell)().endCell();\n}\nclass UserPurchase {\n    static createFromAddress(address) {\n        return new UserPurchase(address);\n    }\n    static createFromConfig(config, code) {\n        let workchain = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 0;\n        const data = userPurchaseConfigToCell(config);\n        const init = {\n            code,\n            data\n        };\n        return new UserPurchase((0,_ton_core__WEBPACK_IMPORTED_MODULE_0__.contractAddress)(workchain, init), init);\n    }\n    async sendDeploy(provider, via, value) {\n        await provider.internal(via, {\n            value,\n            sendMode: _ton_core__WEBPACK_IMPORTED_MODULE_0__.SendMode.PAY_GAS_SEPARATELY,\n            body: (0,_ton_core__WEBPACK_IMPORTED_MODULE_0__.beginCell)().endCell()\n        });\n    }\n    // Send refund request\n    async sendRefund(provider, via, value, purchaseId) {\n        const body = (0,_ton_core__WEBPACK_IMPORTED_MODULE_0__.beginCell)().storeUint(0x31d5b5ac, 32) // Refund op code\n        .storeUint(purchaseId, 32) // purchase_id\n        .endCell();\n        await provider.internal(via, {\n            value,\n            sendMode: _ton_core__WEBPACK_IMPORTED_MODULE_0__.SendMode.PAY_GAS_SEPARATELY,\n            body\n        });\n    }\n    // Getter methods\n    async getTotalPurchased(provider) {\n        const result = await provider.get('total_purchased', []);\n        return result.stack.readBigNumber();\n    }\n    async getTotalPaid(provider) {\n        const result = await provider.get('total_paid', []);\n        return result.stack.readBigNumber();\n    }\n    async getPurchaseIdCounter(provider) {\n        const result = await provider.get('purchase_id_counter', []);\n        return result.stack.readNumber();\n    }\n    async getPurchaseDetails(provider, purchaseId) {\n        try {\n            const args = new _ton_core__WEBPACK_IMPORTED_MODULE_0__.TupleBuilder();\n            // args.writeNumber(purchaseId);\n            args.writeCell((0,_ton_core__WEBPACK_IMPORTED_MODULE_0__.beginCell)().storeInt(purchaseId, 10).endCell()); // Placeholder for additional args if needed\n            const result = await provider.get('purchase_details', args.build());\n            if (result.stack.remaining === 0) {\n                return null;\n            }\n            // Read the PurchaseRecord struct\n            const record = result.stack.readTuple();\n            return {\n                id: record.readNumber(),\n                user: record.readAddress(),\n                amount: record.readBigNumber(),\n                tokens: record.readBigNumber(),\n                timestamp: record.readNumber(),\n                currency: record.readNumber(),\n                purchase_method: record.readNumber(),\n                nonce: record.readBigNumber()\n            };\n        } catch (error) {\n            console.error('Error reading purchase details:', error);\n            return null;\n        }\n    }\n    async isRefunded(provider, purchaseId) {\n        try {\n            const args = new _ton_core__WEBPACK_IMPORTED_MODULE_0__.TupleBuilder();\n            args.writeNumber(purchaseId);\n            const result = await provider.get('is_refunded', args.build());\n            return result.stack.readBoolean();\n        } catch (error) {\n            console.error('Error checking refund status:', error);\n            return false;\n        }\n    }\n    async getSignatureVerifiedPurchases(provider) {\n        try {\n            const result = await provider.get('signature_verified_purchases', []);\n            return result.stack.readNumber();\n        } catch (error) {\n            console.error('Error reading signature verified purchases:', error);\n            return 0;\n        }\n    }\n    constructor(address, init){\n        this.address = address;\n        this.init = init;\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../wrappers/UserPurchase.ts\n"));

/***/ })

});