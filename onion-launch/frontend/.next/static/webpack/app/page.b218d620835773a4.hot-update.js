"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/hooks/useUserPurchases.ts":
/*!***************************************!*\
  !*** ./src/hooks/useUserPurchases.ts ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useUserPurchases: () => (/* binding */ useUserPurchases)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _tonconnect_ui_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @tonconnect/ui-react */ \"(app-pages-browser)/./node_modules/@tonconnect/ui-react/lib/index.mjs\");\n/* harmony import */ var _ton_ton__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @ton/ton */ \"(app-pages-browser)/./node_modules/@ton/ton/dist/index.js\");\n/* harmony import */ var _ton_ton__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_ton_ton__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _wrappers_UserPurchase__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../wrappers/UserPurchase */ \"(app-pages-browser)/../wrappers/UserPurchase.ts\");\n/* provided dependency */ var Buffer = __webpack_require__(/*! buffer */ \"(app-pages-browser)/./node_modules/buffer/index.js\")[\"Buffer\"];\n/* __next_internal_client_entry_do_not_use__ useUserPurchases auto */ \n\n\n\n// Contract configuration\nconst AUCTION_CONTRACT_ADDRESS = \"EQCCjQ9e39-9eoBJdt_mXn2jftAWgNDhYaytVg4tFzEFmKTX\" || 0;\n// TON client configuration\nconst getTonClient = ()=>{\n    return new _ton_ton__WEBPACK_IMPORTED_MODULE_2__.TonClient({\n        endpoint: 'https://testnet.toncenter.com/api/v2/jsonRPC',\n        apiKey: \"8b797132a14f8e75806ad0774e509b04088514045a8a539eb6bd747bf25d9493\"\n    });\n};\nfunction useUserPurchases() {\n    var _wallet_account, _wallet_account1, _wallet_account2, _wallet_account3;\n    const wallet = (0,_tonconnect_ui_react__WEBPACK_IMPORTED_MODULE_1__.useTonWallet)();\n    const [tonConnectUI] = (0,_tonconnect_ui_react__WEBPACK_IMPORTED_MODULE_1__.useTonConnectUI)();\n    const [state, setState] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({\n        purchases: [],\n        totalPurchased: 0,\n        totalInvested: 0,\n        isLoading: false,\n        error: null,\n        isRefunding: null\n    });\n    // Calculate user purchase contract address\n    const getUserPurchaseAddress = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useUserPurchases.useCallback[getUserPurchaseAddress]\": async (userAddress)=>{\n            try {\n                const client = getTonClient();\n                // Import the auction contract to get the user purchase address\n                const { OnionAuction } = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_src_lib_onionAuction_ts\").then(__webpack_require__.bind(__webpack_require__, /*! ../lib/onionAuction */ \"(app-pages-browser)/./src/lib/onionAuction.ts\"));\n                const auction = client.open(OnionAuction.createFromAddress(_ton_ton__WEBPACK_IMPORTED_MODULE_2__.Address.parse(AUCTION_CONTRACT_ADDRESS)));\n                const userPurchaseAddress = await auction.getUserPurchaseAddress(_ton_ton__WEBPACK_IMPORTED_MODULE_2__.Address.parse(userAddress));\n                return userPurchaseAddress;\n            } catch (error) {\n                console.error('Error getting user purchase address:', error);\n                return null;\n            }\n        }\n    }[\"useUserPurchases.useCallback[getUserPurchaseAddress]\"], []);\n    // Check if contract is deployed\n    const isContractDeployed = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useUserPurchases.useCallback[isContractDeployed]\": async (address)=>{\n            try {\n                const client = getTonClient();\n                const account = await client.getContractState(address);\n                return account.state === 'active';\n            } catch (error) {\n                console.error('Error checking contract deployment:', error);\n                return false;\n            }\n        }\n    }[\"useUserPurchases.useCallback[isContractDeployed]\"], []);\n    // Fetch purchases from the blockchain\n    const fetchPurchases = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useUserPurchases.useCallback[fetchPurchases]\": async ()=>{\n            var _wallet_account;\n            if (!(wallet === null || wallet === void 0 ? void 0 : (_wallet_account = wallet.account) === null || _wallet_account === void 0 ? void 0 : _wallet_account.address)) {\n                setState({\n                    \"useUserPurchases.useCallback[fetchPurchases]\": (prev)=>({\n                            ...prev,\n                            purchases: [],\n                            totalPurchased: 0,\n                            totalInvested: 0,\n                            isLoading: false,\n                            error: null\n                        })\n                }[\"useUserPurchases.useCallback[fetchPurchases]\"]);\n                return;\n            }\n            setState({\n                \"useUserPurchases.useCallback[fetchPurchases]\": (prev)=>({\n                        ...prev,\n                        isLoading: true,\n                        error: null\n                    })\n            }[\"useUserPurchases.useCallback[fetchPurchases]\"]);\n            try {\n                const userPurchaseAddress = await getUserPurchaseAddress(wallet.account.address);\n                if (!userPurchaseAddress) {\n                    throw new Error('Failed to get user purchase contract address');\n                }\n                // Check if the contract is deployed\n                const isDeployed = await isContractDeployed(userPurchaseAddress);\n                if (!isDeployed) {\n                    // Contract not deployed means no purchases yet\n                    setState({\n                        \"useUserPurchases.useCallback[fetchPurchases]\": (prev)=>({\n                                ...prev,\n                                purchases: [],\n                                totalPurchased: 0,\n                                totalInvested: 0,\n                                isLoading: false,\n                                error: null\n                            })\n                    }[\"useUserPurchases.useCallback[fetchPurchases]\"]);\n                    return;\n                }\n                const client = getTonClient();\n                const userPurchase = client.open(_wrappers_UserPurchase__WEBPACK_IMPORTED_MODULE_3__.UserPurchase.createFromAddress(userPurchaseAddress));\n                client.callGetMethod;\n                // Get purchase count\n                const purchaseCount = await userPurchase.getPurchaseIdCounter();\n                if (purchaseCount === 0) {\n                    setState({\n                        \"useUserPurchases.useCallback[fetchPurchases]\": (prev)=>({\n                                ...prev,\n                                purchases: [],\n                                totalPurchased: 0,\n                                totalInvested: 0,\n                                isLoading: false,\n                                error: null\n                            })\n                    }[\"useUserPurchases.useCallback[fetchPurchases]\"]);\n                    return;\n                }\n                // Fetch all purchase records\n                const purchases = [];\n                let totalTokens = 0;\n                let totalInvestedUSD = 0;\n                for(let i = 1; i <= purchaseCount; i++){\n                    try {\n                        const purchaseRecord = await userPurchase.getPurchaseDetails(i);\n                        const isRefunded = await userPurchase.isRefunded(i);\n                        if (purchaseRecord) {\n                            const amountInUnits = Number(purchaseRecord.amount) / ********** // Convert from nanotons\n                            ;\n                            const tokensInUnits = Number(purchaseRecord.tokens) / ********** // Convert from nanotons\n                            ;\n                            const price = amountInUnits / tokensInUnits // Price per token\n                            ;\n                            // Convert timestamp to readable format\n                            const timestamp = new Date(purchaseRecord.timestamp * 1000).toLocaleString('en-US', {\n                                year: 'numeric',\n                                month: '2-digit',\n                                day: '2-digit',\n                                hour: '2-digit',\n                                minute: '2-digit',\n                                timeZone: 'UTC',\n                                timeZoneName: 'short'\n                            });\n                            const purchase = {\n                                id: purchaseRecord.id,\n                                amount: amountInUnits,\n                                currency: purchaseRecord.currency === 0 ? 'TON' : 'USDT',\n                                tokens: tokensInUnits,\n                                price: price,\n                                timestamp: timestamp,\n                                status: isRefunded ? 'refunded' : 'completed',\n                                canRefund: !isRefunded,\n                                nonce: purchaseRecord.nonce.toString(),\n                                purchase_method: purchaseRecord.purchase_method === 0 ? 'direct' : 'signature_verified'\n                            };\n                            purchases.push(purchase);\n                            // Calculate totals (excluding refunded purchases)\n                            if (!isRefunded) {\n                                totalTokens += tokensInUnits;\n                                // Convert to USD for total invested calculation\n                                const usdValue = purchaseRecord.currency === 0 ? amountInUnits * 5.5 : amountInUnits // Assume TON = $5.5\n                                ;\n                                totalInvestedUSD += usdValue;\n                            }\n                        }\n                    } catch (error) {\n                        console.error(\"Error fetching purchase \".concat(i, \":\"), error);\n                    // Continue with other purchases\n                    }\n                }\n                setState({\n                    \"useUserPurchases.useCallback[fetchPurchases]\": (prev)=>({\n                            ...prev,\n                            purchases: purchases.reverse(),\n                            totalPurchased: totalTokens,\n                            totalInvested: totalInvestedUSD,\n                            isLoading: false,\n                            error: null\n                        })\n                }[\"useUserPurchases.useCallback[fetchPurchases]\"]);\n            } catch (error) {\n                console.error('Error fetching purchases:', error);\n                setState({\n                    \"useUserPurchases.useCallback[fetchPurchases]\": (prev)=>({\n                            ...prev,\n                            isLoading: false,\n                            error: error instanceof Error ? error.message : 'Failed to fetch purchases'\n                        })\n                }[\"useUserPurchases.useCallback[fetchPurchases]\"]);\n            }\n        }\n    }[\"useUserPurchases.useCallback[fetchPurchases]\"], [\n        wallet === null || wallet === void 0 ? void 0 : (_wallet_account = wallet.account) === null || _wallet_account === void 0 ? void 0 : _wallet_account.address,\n        getUserPurchaseAddress,\n        isContractDeployed\n    ]);\n    // Refresh purchases\n    const refreshPurchases = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useUserPurchases.useCallback[refreshPurchases]\": ()=>{\n            fetchPurchases();\n        }\n    }[\"useUserPurchases.useCallback[refreshPurchases]\"], [\n        fetchPurchases\n    ]);\n    // Request refund\n    const requestRefund = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useUserPurchases.useCallback[requestRefund]\": async (purchaseId)=>{\n            var _wallet_account;\n            if (!(wallet === null || wallet === void 0 ? void 0 : (_wallet_account = wallet.account) === null || _wallet_account === void 0 ? void 0 : _wallet_account.address) || !tonConnectUI) {\n                throw new Error('Wallet not connected');\n            }\n            setState({\n                \"useUserPurchases.useCallback[requestRefund]\": (prev)=>({\n                        ...prev,\n                        isRefunding: purchaseId\n                    })\n            }[\"useUserPurchases.useCallback[requestRefund]\"]);\n            try {\n                const userPurchaseAddress = await getUserPurchaseAddress(wallet.account.address);\n                if (!userPurchaseAddress) {\n                    throw new Error('Failed to get user purchase contract address');\n                }\n                // Create refund transaction\n                const refundBody = (0,_ton_ton__WEBPACK_IMPORTED_MODULE_2__.beginCell)().storeUint(0x31d5b5ac, 32) // Refund op code\n                .storeUint(purchaseId, 32) // purchase_id\n                .endCell();\n                const transactionRequest = {\n                    validUntil: Math.floor(Date.now() / 1000) + 600,\n                    messages: [\n                        {\n                            address: userPurchaseAddress.toString(),\n                            amount: (0.1 * **********).toString(),\n                            payload: Buffer.from(refundBody.toBoc()).toString('base64')\n                        }\n                    ]\n                };\n                const result = await tonConnectUI.sendTransaction(transactionRequest);\n                console.log('Refund transaction sent:', result);\n                // Refresh purchases after successful refund\n                setTimeout({\n                    \"useUserPurchases.useCallback[requestRefund]\": ()=>{\n                        refreshPurchases();\n                    }\n                }[\"useUserPurchases.useCallback[requestRefund]\"], 3000); // Wait 3 seconds for transaction to be processed\n                return true;\n            } catch (error) {\n                console.error('Refund failed:', error);\n                throw error;\n            } finally{\n                setState({\n                    \"useUserPurchases.useCallback[requestRefund]\": (prev)=>({\n                            ...prev,\n                            isRefunding: null\n                        })\n                }[\"useUserPurchases.useCallback[requestRefund]\"]);\n            }\n        }\n    }[\"useUserPurchases.useCallback[requestRefund]\"], [\n        wallet === null || wallet === void 0 ? void 0 : (_wallet_account1 = wallet.account) === null || _wallet_account1 === void 0 ? void 0 : _wallet_account1.address,\n        tonConnectUI,\n        getUserPurchaseAddress,\n        refreshPurchases\n    ]);\n    // Auto-fetch purchases when wallet connects\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useUserPurchases.useEffect\": ()=>{\n            var _wallet_account;\n            if (wallet === null || wallet === void 0 ? void 0 : (_wallet_account = wallet.account) === null || _wallet_account === void 0 ? void 0 : _wallet_account.address) {\n                fetchPurchases();\n            }\n        }\n    }[\"useUserPurchases.useEffect\"], [\n        wallet === null || wallet === void 0 ? void 0 : (_wallet_account2 = wallet.account) === null || _wallet_account2 === void 0 ? void 0 : _wallet_account2.address,\n        fetchPurchases\n    ]);\n    return {\n        ...state,\n        refreshPurchases,\n        requestRefund,\n        isConnected: !!(wallet === null || wallet === void 0 ? void 0 : (_wallet_account3 = wallet.account) === null || _wallet_account3 === void 0 ? void 0 : _wallet_account3.address)\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useUserPurchases.ts\n"));

/***/ })

});