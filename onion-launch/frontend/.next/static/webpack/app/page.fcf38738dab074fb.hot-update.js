"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/hooks/useUserPurchases.ts":
/*!***************************************!*\
  !*** ./src/hooks/useUserPurchases.ts ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useUserPurchases: () => (/* binding */ useUserPurchases)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _tonconnect_ui_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @tonconnect/ui-react */ \"(app-pages-browser)/./node_modules/@tonconnect/ui-react/lib/index.mjs\");\n/* harmony import */ var _ton_ton__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @ton/ton */ \"(app-pages-browser)/./node_modules/@ton/ton/dist/index.js\");\n/* harmony import */ var _ton_ton__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_ton_ton__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _wrappers_UserPurchase__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../wrappers/UserPurchase */ \"(app-pages-browser)/../wrappers/UserPurchase.ts\");\n/* provided dependency */ var Buffer = __webpack_require__(/*! buffer */ \"(app-pages-browser)/./node_modules/buffer/index.js\")[\"Buffer\"];\n/* __next_internal_client_entry_do_not_use__ useUserPurchases auto */ \n\n\n\n// Contract configuration\nconst AUCTION_CONTRACT_ADDRESS = \"EQCCjQ9e39-9eoBJdt_mXn2jftAWgNDhYaytVg4tFzEFmKTX\" || 0;\n// TON client configuration\nconst getTonClient = ()=>{\n    return new _ton_ton__WEBPACK_IMPORTED_MODULE_2__.TonClient({\n        endpoint: 'https://testnet.toncenter.com/api/v2/jsonRPC',\n        apiKey: \"8b797132a14f8e75806ad0774e509b04088514045a8a539eb6bd747bf25d9493\"\n    });\n};\nfunction useUserPurchases() {\n    var _wallet_account, _wallet_account1, _wallet_account2, _wallet_account3;\n    const wallet = (0,_tonconnect_ui_react__WEBPACK_IMPORTED_MODULE_1__.useTonWallet)();\n    const [tonConnectUI] = (0,_tonconnect_ui_react__WEBPACK_IMPORTED_MODULE_1__.useTonConnectUI)();\n    const [state, setState] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({\n        purchases: [],\n        totalPurchased: 0,\n        totalInvested: 0,\n        isLoading: false,\n        error: null,\n        isRefunding: null\n    });\n    // Calculate user purchase contract address\n    const getUserPurchaseAddress = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useUserPurchases.useCallback[getUserPurchaseAddress]\": async (userAddress)=>{\n            try {\n                const client = getTonClient();\n                // Import the auction contract to get the user purchase address\n                const { OnionAuction } = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_src_lib_onionAuction_ts\").then(__webpack_require__.bind(__webpack_require__, /*! ../lib/onionAuction */ \"(app-pages-browser)/./src/lib/onionAuction.ts\"));\n                const auction = client.open(OnionAuction.createFromAddress(_ton_ton__WEBPACK_IMPORTED_MODULE_2__.Address.parse(AUCTION_CONTRACT_ADDRESS)));\n                const userPurchaseAddress = await auction.getUserPurchaseAddress(_ton_ton__WEBPACK_IMPORTED_MODULE_2__.Address.parse(userAddress));\n                return userPurchaseAddress;\n            } catch (error) {\n                console.error('Error getting user purchase address:', error);\n                return null;\n            }\n        }\n    }[\"useUserPurchases.useCallback[getUserPurchaseAddress]\"], []);\n    // Check if contract is deployed\n    const isContractDeployed = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useUserPurchases.useCallback[isContractDeployed]\": async (address)=>{\n            try {\n                const client = getTonClient();\n                const account = await client.getContractState(address);\n                return account.state === 'active';\n            } catch (error) {\n                console.error('Error checking contract deployment:', error);\n                return false;\n            }\n        }\n    }[\"useUserPurchases.useCallback[isContractDeployed]\"], []);\n    // Fetch purchases from the blockchain\n    const fetchPurchases = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useUserPurchases.useCallback[fetchPurchases]\": async ()=>{\n            var _wallet_account;\n            if (!(wallet === null || wallet === void 0 ? void 0 : (_wallet_account = wallet.account) === null || _wallet_account === void 0 ? void 0 : _wallet_account.address)) {\n                setState({\n                    \"useUserPurchases.useCallback[fetchPurchases]\": (prev)=>({\n                            ...prev,\n                            purchases: [],\n                            totalPurchased: 0,\n                            totalInvested: 0,\n                            isLoading: false,\n                            error: null\n                        })\n                }[\"useUserPurchases.useCallback[fetchPurchases]\"]);\n                return;\n            }\n            setState({\n                \"useUserPurchases.useCallback[fetchPurchases]\": (prev)=>({\n                        ...prev,\n                        isLoading: true,\n                        error: null\n                    })\n            }[\"useUserPurchases.useCallback[fetchPurchases]\"]);\n            try {\n                const userPurchaseAddress = await getUserPurchaseAddress(wallet.account.address);\n                if (!userPurchaseAddress) {\n                    throw new Error('Failed to get user purchase contract address');\n                }\n                // Check if the contract is deployed\n                const isDeployed = await isContractDeployed(userPurchaseAddress);\n                if (!isDeployed) {\n                    // Contract not deployed means no purchases yet\n                    setState({\n                        \"useUserPurchases.useCallback[fetchPurchases]\": (prev)=>({\n                                ...prev,\n                                purchases: [],\n                                totalPurchased: 0,\n                                totalInvested: 0,\n                                isLoading: false,\n                                error: null\n                            })\n                    }[\"useUserPurchases.useCallback[fetchPurchases]\"]);\n                    return;\n                }\n                const client = getTonClient();\n                const userPurchase = client.open(_wrappers_UserPurchase__WEBPACK_IMPORTED_MODULE_3__.UserPurchase.createFromAddress(userPurchaseAddress));\n                // Get purchase count\n                const purchaseCount = await userPurchase.getPurchaseIdCounter();\n                if (purchaseCount === 0) {\n                    setState({\n                        \"useUserPurchases.useCallback[fetchPurchases]\": (prev)=>({\n                                ...prev,\n                                purchases: [],\n                                totalPurchased: 0,\n                                totalInvested: 0,\n                                isLoading: false,\n                                error: null\n                            })\n                    }[\"useUserPurchases.useCallback[fetchPurchases]\"]);\n                    return;\n                }\n                // Fetch all purchase records\n                const purchases = [];\n                let totalTokens = 0;\n                let totalInvestedUSD = 0;\n                for(let i = 1; i <= purchaseCount; i++){\n                    try {\n                        const args = new _ton_ton__WEBPACK_IMPORTED_MODULE_2__.TupleBuilder();\n                        args.writeNumber(i);\n                        const result = await client.runMethod(userPurchase.address, 'purchase_details', args.build());\n                        //const purchaseRecord = result.stack.readTuple().readTuple<PurchaseRecord | null>()\n                        const isRefunded = await userPurchase.isRefunded(i);\n                        if (purchaseRecord) {\n                            const amountInUnits = Number(purchaseRecord.amount) / ********** // Convert from nanotons\n                            ;\n                            const tokensInUnits = Number(purchaseRecord.tokens) / ********** // Convert from nanotons\n                            ;\n                            const price = amountInUnits / tokensInUnits // Price per token\n                            ;\n                            // Convert timestamp to readable format\n                            const timestamp = new Date(purchaseRecord.timestamp * 1000).toLocaleString('en-US', {\n                                year: 'numeric',\n                                month: '2-digit',\n                                day: '2-digit',\n                                hour: '2-digit',\n                                minute: '2-digit',\n                                timeZone: 'UTC',\n                                timeZoneName: 'short'\n                            });\n                            const purchase = {\n                                id: purchaseRecord.id,\n                                amount: amountInUnits,\n                                currency: purchaseRecord.currency === 0 ? 'TON' : 'USDT',\n                                tokens: tokensInUnits,\n                                price: price,\n                                timestamp: timestamp,\n                                status: isRefunded ? 'refunded' : 'completed',\n                                canRefund: !isRefunded,\n                                nonce: purchaseRecord.nonce.toString(),\n                                purchase_method: purchaseRecord.purchase_method === 0 ? 'direct' : 'signature_verified'\n                            };\n                            purchases.push(purchase);\n                            // Calculate totals (excluding refunded purchases)\n                            if (!isRefunded) {\n                                totalTokens += tokensInUnits;\n                                // Convert to USD for total invested calculation\n                                const usdValue = purchaseRecord.currency === 0 ? amountInUnits * 5.5 : amountInUnits // Assume TON = $5.5\n                                ;\n                                totalInvestedUSD += usdValue;\n                            }\n                        }\n                    } catch (error) {\n                        console.error(\"Error fetching purchase \".concat(i, \":\"), error);\n                    // Continue with other purchases\n                    }\n                }\n                setState({\n                    \"useUserPurchases.useCallback[fetchPurchases]\": (prev)=>({\n                            ...prev,\n                            purchases: purchases.reverse(),\n                            totalPurchased: totalTokens,\n                            totalInvested: totalInvestedUSD,\n                            isLoading: false,\n                            error: null\n                        })\n                }[\"useUserPurchases.useCallback[fetchPurchases]\"]);\n            } catch (error) {\n                console.error('Error fetching purchases:', error);\n                setState({\n                    \"useUserPurchases.useCallback[fetchPurchases]\": (prev)=>({\n                            ...prev,\n                            isLoading: false,\n                            error: error instanceof Error ? error.message : 'Failed to fetch purchases'\n                        })\n                }[\"useUserPurchases.useCallback[fetchPurchases]\"]);\n            }\n        }\n    }[\"useUserPurchases.useCallback[fetchPurchases]\"], [\n        wallet === null || wallet === void 0 ? void 0 : (_wallet_account = wallet.account) === null || _wallet_account === void 0 ? void 0 : _wallet_account.address,\n        getUserPurchaseAddress,\n        isContractDeployed\n    ]);\n    // Refresh purchases\n    const refreshPurchases = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useUserPurchases.useCallback[refreshPurchases]\": ()=>{\n            fetchPurchases();\n        }\n    }[\"useUserPurchases.useCallback[refreshPurchases]\"], [\n        fetchPurchases\n    ]);\n    // Request refund\n    const requestRefund = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useUserPurchases.useCallback[requestRefund]\": async (purchaseId)=>{\n            var _wallet_account;\n            if (!(wallet === null || wallet === void 0 ? void 0 : (_wallet_account = wallet.account) === null || _wallet_account === void 0 ? void 0 : _wallet_account.address) || !tonConnectUI) {\n                throw new Error('Wallet not connected');\n            }\n            setState({\n                \"useUserPurchases.useCallback[requestRefund]\": (prev)=>({\n                        ...prev,\n                        isRefunding: purchaseId\n                    })\n            }[\"useUserPurchases.useCallback[requestRefund]\"]);\n            try {\n                const userPurchaseAddress = await getUserPurchaseAddress(wallet.account.address);\n                if (!userPurchaseAddress) {\n                    throw new Error('Failed to get user purchase contract address');\n                }\n                // Create refund transaction\n                const refundBody = (0,_ton_ton__WEBPACK_IMPORTED_MODULE_2__.beginCell)().storeUint(0x31d5b5ac, 32) // Refund op code\n                .storeUint(purchaseId, 32) // purchase_id\n                .endCell();\n                const transactionRequest = {\n                    validUntil: Math.floor(Date.now() / 1000) + 600,\n                    messages: [\n                        {\n                            address: userPurchaseAddress.toString(),\n                            amount: (0.1 * **********).toString(),\n                            payload: Buffer.from(refundBody.toBoc()).toString('base64')\n                        }\n                    ]\n                };\n                const result = await tonConnectUI.sendTransaction(transactionRequest);\n                console.log('Refund transaction sent:', result);\n                // Refresh purchases after successful refund\n                setTimeout({\n                    \"useUserPurchases.useCallback[requestRefund]\": ()=>{\n                        refreshPurchases();\n                    }\n                }[\"useUserPurchases.useCallback[requestRefund]\"], 3000); // Wait 3 seconds for transaction to be processed\n                return true;\n            } catch (error) {\n                console.error('Refund failed:', error);\n                throw error;\n            } finally{\n                setState({\n                    \"useUserPurchases.useCallback[requestRefund]\": (prev)=>({\n                            ...prev,\n                            isRefunding: null\n                        })\n                }[\"useUserPurchases.useCallback[requestRefund]\"]);\n            }\n        }\n    }[\"useUserPurchases.useCallback[requestRefund]\"], [\n        wallet === null || wallet === void 0 ? void 0 : (_wallet_account1 = wallet.account) === null || _wallet_account1 === void 0 ? void 0 : _wallet_account1.address,\n        tonConnectUI,\n        getUserPurchaseAddress,\n        refreshPurchases\n    ]);\n    // Auto-fetch purchases when wallet connects\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useUserPurchases.useEffect\": ()=>{\n            var _wallet_account;\n            if (wallet === null || wallet === void 0 ? void 0 : (_wallet_account = wallet.account) === null || _wallet_account === void 0 ? void 0 : _wallet_account.address) {\n                fetchPurchases();\n            }\n        }\n    }[\"useUserPurchases.useEffect\"], [\n        wallet === null || wallet === void 0 ? void 0 : (_wallet_account2 = wallet.account) === null || _wallet_account2 === void 0 ? void 0 : _wallet_account2.address,\n        fetchPurchases\n    ]);\n    return {\n        ...state,\n        refreshPurchases,\n        requestRefund,\n        isConnected: !!(wallet === null || wallet === void 0 ? void 0 : (_wallet_account3 = wallet.account) === null || _wallet_account3 === void 0 ? void 0 : _wallet_account3.address)\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useUserPurchases.ts\n"));

/***/ })

});