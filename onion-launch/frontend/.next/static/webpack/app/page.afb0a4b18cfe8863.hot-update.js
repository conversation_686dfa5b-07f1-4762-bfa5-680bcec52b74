"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/hooks/useUserPurchases.ts":
/*!***************************************!*\
  !*** ./src/hooks/useUserPurchases.ts ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useUserPurchases: () => (/* binding */ useUserPurchases)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _tonconnect_ui_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @tonconnect/ui-react */ \"(app-pages-browser)/./node_modules/@tonconnect/ui-react/lib/index.mjs\");\n/* harmony import */ var _ton_ton__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @ton/ton */ \"(app-pages-browser)/./node_modules/@ton/ton/dist/index.js\");\n/* harmony import */ var _ton_ton__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_ton_ton__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _wrappers_UserPurchase__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../wrappers/UserPurchase */ \"(app-pages-browser)/../wrappers/UserPurchase.ts\");\n/* provided dependency */ var Buffer = __webpack_require__(/*! buffer */ \"(app-pages-browser)/./node_modules/buffer/index.js\")[\"Buffer\"];\n/* __next_internal_client_entry_do_not_use__ useUserPurchases auto */ \n\n\n\n// Contract configuration\nconst AUCTION_CONTRACT_ADDRESS = \"EQCCjQ9e39-9eoBJdt_mXn2jftAWgNDhYaytVg4tFzEFmKTX\" || 0;\n// TON client configuration\nconst getTonClient = ()=>{\n    return new _ton_ton__WEBPACK_IMPORTED_MODULE_2__.TonClient({\n        endpoint: 'https://testnet.toncenter.com/api/v2/jsonRPC',\n        apiKey: \"8b797132a14f8e75806ad0774e509b04088514045a8a539eb6bd747bf25d9493\"\n    });\n};\nfunction useUserPurchases() {\n    var _wallet_account, _wallet_account1, _wallet_account2, _wallet_account3;\n    const wallet = (0,_tonconnect_ui_react__WEBPACK_IMPORTED_MODULE_1__.useTonWallet)();\n    const [tonConnectUI] = (0,_tonconnect_ui_react__WEBPACK_IMPORTED_MODULE_1__.useTonConnectUI)();\n    const [state, setState] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({\n        purchases: [],\n        totalPurchased: 0,\n        totalInvested: 0,\n        isLoading: false,\n        error: null,\n        isRefunding: null\n    });\n    // Calculate user purchase contract address\n    const getUserPurchaseAddress = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useUserPurchases.useCallback[getUserPurchaseAddress]\": async (userAddress)=>{\n            try {\n                const client = getTonClient();\n                // Import the auction contract to get the user purchase address\n                const { OnionAuction } = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_src_lib_onionAuction_ts\").then(__webpack_require__.bind(__webpack_require__, /*! ../lib/onionAuction */ \"(app-pages-browser)/./src/lib/onionAuction.ts\"));\n                const auction = client.open(OnionAuction.createFromAddress(_ton_ton__WEBPACK_IMPORTED_MODULE_2__.Address.parse(AUCTION_CONTRACT_ADDRESS)));\n                const userPurchaseAddress = await auction.getUserPurchaseAddress(_ton_ton__WEBPACK_IMPORTED_MODULE_2__.Address.parse(userAddress));\n                return userPurchaseAddress;\n            } catch (error) {\n                console.error('Error getting user purchase address:', error);\n                return null;\n            }\n        }\n    }[\"useUserPurchases.useCallback[getUserPurchaseAddress]\"], []);\n    // Check if contract is deployed\n    const isContractDeployed = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useUserPurchases.useCallback[isContractDeployed]\": async (address)=>{\n            try {\n                const client = getTonClient();\n                const account = await client.getContractState(address);\n                return account.state === 'active';\n            } catch (error) {\n                console.error('Error checking contract deployment:', error);\n                return false;\n            }\n        }\n    }[\"useUserPurchases.useCallback[isContractDeployed]\"], []);\n    // Fetch purchases from the blockchain\n    const fetchPurchases = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useUserPurchases.useCallback[fetchPurchases]\": async ()=>{\n            var _wallet_account;\n            if (!(wallet === null || wallet === void 0 ? void 0 : (_wallet_account = wallet.account) === null || _wallet_account === void 0 ? void 0 : _wallet_account.address)) {\n                setState({\n                    \"useUserPurchases.useCallback[fetchPurchases]\": (prev)=>({\n                            ...prev,\n                            purchases: [],\n                            totalPurchased: 0,\n                            totalInvested: 0,\n                            isLoading: false,\n                            error: null\n                        })\n                }[\"useUserPurchases.useCallback[fetchPurchases]\"]);\n                return;\n            }\n            setState({\n                \"useUserPurchases.useCallback[fetchPurchases]\": (prev)=>({\n                        ...prev,\n                        isLoading: true,\n                        error: null\n                    })\n            }[\"useUserPurchases.useCallback[fetchPurchases]\"]);\n            try {\n                const userPurchaseAddress = await getUserPurchaseAddress(wallet.account.address);\n                if (!userPurchaseAddress) {\n                    throw new Error('Failed to get user purchase contract address');\n                }\n                // Check if the contract is deployed\n                const isDeployed = await isContractDeployed(userPurchaseAddress);\n                if (!isDeployed) {\n                    // Contract not deployed means no purchases yet\n                    setState({\n                        \"useUserPurchases.useCallback[fetchPurchases]\": (prev)=>({\n                                ...prev,\n                                purchases: [],\n                                totalPurchased: 0,\n                                totalInvested: 0,\n                                isLoading: false,\n                                error: null\n                            })\n                    }[\"useUserPurchases.useCallback[fetchPurchases]\"]);\n                    return;\n                }\n                const client = getTonClient();\n                const userPurchase = client.open(_wrappers_UserPurchase__WEBPACK_IMPORTED_MODULE_3__.UserPurchase.createFromAddress(userPurchaseAddress));\n                // Get purchase count\n                const purchaseCount = await userPurchase.getPurchaseIdCounter();\n                if (purchaseCount === 0) {\n                    setState({\n                        \"useUserPurchases.useCallback[fetchPurchases]\": (prev)=>({\n                                ...prev,\n                                purchases: [],\n                                totalPurchased: 0,\n                                totalInvested: 0,\n                                isLoading: false,\n                                error: null\n                            })\n                    }[\"useUserPurchases.useCallback[fetchPurchases]\"]);\n                    return;\n                }\n                // Fetch all purchase records\n                const purchases = [];\n                let totalTokens = 0;\n                let totalInvestedUSD = 0;\n                for(let i = 1; i <= purchaseCount; i++){\n                    try {\n                        const purchaseRecord = await userPurchase.getPurchaseDetails(i);\n                        const isRefunded = await userPurchase.isRefunded(i);\n                        if (purchaseRecord) {\n                            const amountInUnits = Number(purchaseRecord.amount) / ********** // Convert from nanotons\n                            ;\n                            const tokensInUnits = Number(purchaseRecord.tokens) / ********** // Convert from nanotons\n                            ;\n                            const price = amountInUnits / tokensInUnits // Price per token\n                            ;\n                            // Convert timestamp to readable format\n                            const timestamp = new Date(purchaseRecord.timestamp * 1000).toLocaleString('en-US', {\n                                year: 'numeric',\n                                month: '2-digit',\n                                day: '2-digit',\n                                hour: '2-digit',\n                                minute: '2-digit',\n                                timeZone: 'UTC',\n                                timeZoneName: 'short'\n                            });\n                            const purchase = {\n                                id: purchaseRecord.id,\n                                amount: amountInUnits,\n                                currency: purchaseRecord.currency === 0 ? 'TON' : 'USDT',\n                                tokens: tokensInUnits,\n                                price: price,\n                                timestamp: timestamp,\n                                status: isRefunded ? 'refunded' : 'completed',\n                                canRefund: !isRefunded,\n                                nonce: purchaseRecord.nonce.toString(),\n                                purchase_method: purchaseRecord.purchase_method === 0 ? 'direct' : 'signature_verified'\n                            };\n                            purchases.push(purchase);\n                            // Calculate totals (excluding refunded purchases)\n                            if (!isRefunded) {\n                                totalTokens += tokensInUnits;\n                                // Convert to USD for total invested calculation\n                                const usdValue = purchaseRecord.currency === 0 ? amountInUnits * 5.5 : amountInUnits // Assume TON = $5.5\n                                ;\n                                totalInvestedUSD += usdValue;\n                            }\n                        }\n                    } catch (error) {\n                        console.error(\"Error fetching purchase \".concat(i, \":\"), error);\n                    // Continue with other purchases\n                    }\n                }\n                setState({\n                    \"useUserPurchases.useCallback[fetchPurchases]\": (prev)=>({\n                            ...prev,\n                            purchases: purchases.reverse(),\n                            totalPurchased: totalTokens,\n                            totalInvested: totalInvestedUSD,\n                            isLoading: false,\n                            error: null\n                        })\n                }[\"useUserPurchases.useCallback[fetchPurchases]\"]);\n            } catch (error) {\n                console.error('Error fetching purchases:', error);\n                setState({\n                    \"useUserPurchases.useCallback[fetchPurchases]\": (prev)=>({\n                            ...prev,\n                            isLoading: false,\n                            error: error instanceof Error ? error.message : 'Failed to fetch purchases'\n                        })\n                }[\"useUserPurchases.useCallback[fetchPurchases]\"]);\n            }\n        }\n    }[\"useUserPurchases.useCallback[fetchPurchases]\"], [\n        wallet === null || wallet === void 0 ? void 0 : (_wallet_account = wallet.account) === null || _wallet_account === void 0 ? void 0 : _wallet_account.address,\n        getUserPurchaseAddress,\n        isContractDeployed\n    ]);\n    // Refresh purchases\n    const refreshPurchases = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useUserPurchases.useCallback[refreshPurchases]\": ()=>{\n            fetchPurchases();\n        }\n    }[\"useUserPurchases.useCallback[refreshPurchases]\"], [\n        fetchPurchases\n    ]);\n    // Request refund\n    const requestRefund = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useUserPurchases.useCallback[requestRefund]\": async (purchaseId)=>{\n            var _wallet_account;\n            if (!(wallet === null || wallet === void 0 ? void 0 : (_wallet_account = wallet.account) === null || _wallet_account === void 0 ? void 0 : _wallet_account.address) || !tonConnectUI) {\n                throw new Error('Wallet not connected');\n            }\n            setState({\n                \"useUserPurchases.useCallback[requestRefund]\": (prev)=>({\n                        ...prev,\n                        isRefunding: purchaseId\n                    })\n            }[\"useUserPurchases.useCallback[requestRefund]\"]);\n            try {\n                const userPurchaseAddress = await getUserPurchaseAddress(wallet.account.address);\n                if (!userPurchaseAddress) {\n                    throw new Error('Failed to get user purchase contract address');\n                }\n                // Create refund transaction\n                const refundBody = (0,_ton_ton__WEBPACK_IMPORTED_MODULE_2__.beginCell)().storeUint(0x31d5b5ac, 32) // Refund op code\n                .storeUint(purchaseId, 32) // purchase_id\n                .endCell();\n                const transactionRequest = {\n                    validUntil: Math.floor(Date.now() / 1000) + 600,\n                    messages: [\n                        {\n                            address: userPurchaseAddress.toString(),\n                            amount: (0.1 * **********).toString(),\n                            payload: Buffer.from(refundBody.toBoc()).toString('base64')\n                        }\n                    ]\n                };\n                const result = await tonConnectUI.sendTransaction(transactionRequest);\n                console.log('Refund transaction sent:', result);\n                // Refresh purchases after successful refund\n                setTimeout({\n                    \"useUserPurchases.useCallback[requestRefund]\": ()=>{\n                        refreshPurchases();\n                    }\n                }[\"useUserPurchases.useCallback[requestRefund]\"], 3000); // Wait 3 seconds for transaction to be processed\n                return true;\n            } catch (error) {\n                console.error('Refund failed:', error);\n                throw error;\n            } finally{\n                setState({\n                    \"useUserPurchases.useCallback[requestRefund]\": (prev)=>({\n                            ...prev,\n                            isRefunding: null\n                        })\n                }[\"useUserPurchases.useCallback[requestRefund]\"]);\n            }\n        }\n    }[\"useUserPurchases.useCallback[requestRefund]\"], [\n        wallet === null || wallet === void 0 ? void 0 : (_wallet_account1 = wallet.account) === null || _wallet_account1 === void 0 ? void 0 : _wallet_account1.address,\n        tonConnectUI,\n        getUserPurchaseAddress,\n        refreshPurchases\n    ]);\n    // Auto-fetch purchases when wallet connects\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useUserPurchases.useEffect\": ()=>{\n            var _wallet_account;\n            if (wallet === null || wallet === void 0 ? void 0 : (_wallet_account = wallet.account) === null || _wallet_account === void 0 ? void 0 : _wallet_account.address) {\n                fetchPurchases();\n            }\n        }\n    }[\"useUserPurchases.useEffect\"], [\n        wallet === null || wallet === void 0 ? void 0 : (_wallet_account2 = wallet.account) === null || _wallet_account2 === void 0 ? void 0 : _wallet_account2.address,\n        fetchPurchases\n    ]);\n    return {\n        ...state,\n        refreshPurchases,\n        requestRefund,\n        isConnected: !!(wallet === null || wallet === void 0 ? void 0 : (_wallet_account3 = wallet.account) === null || _wallet_account3 === void 0 ? void 0 : _wallet_account3.address)\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useUserPurchases.ts\n"));

/***/ })

});