"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/hooks/useUserPurchases.ts":
/*!***************************************!*\
  !*** ./src/hooks/useUserPurchases.ts ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useUserPurchases: () => (/* binding */ useUserPurchases)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _tonconnect_ui_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @tonconnect/ui-react */ \"(app-pages-browser)/./node_modules/@tonconnect/ui-react/lib/index.mjs\");\n/* harmony import */ var _ton_ton__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @ton/ton */ \"(app-pages-browser)/./node_modules/@ton/ton/dist/index.js\");\n/* harmony import */ var _ton_ton__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_ton_ton__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _wrappers_UserPurchase__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../wrappers/UserPurchase */ \"(app-pages-browser)/../wrappers/UserPurchase.ts\");\n/* provided dependency */ var Buffer = __webpack_require__(/*! buffer */ \"(app-pages-browser)/./node_modules/buffer/index.js\")[\"Buffer\"];\n/* __next_internal_client_entry_do_not_use__ useUserPurchases auto */ \n\n\n\n// Contract configuration\nconst AUCTION_CONTRACT_ADDRESS = \"EQCCjQ9e39-9eoBJdt_mXn2jftAWgNDhYaytVg4tFzEFmKTX\" || 0;\n// TON client configuration\nconst getTonClient = ()=>{\n    return new _ton_ton__WEBPACK_IMPORTED_MODULE_2__.TonClient({\n        endpoint: 'https://testnet.toncenter.com/api/v2/jsonRPC',\n        apiKey: \"8b797132a14f8e75806ad0774e509b04088514045a8a539eb6bd747bf25d9493\"\n    });\n};\nfunction useUserPurchases() {\n    var _wallet_account, _wallet_account1, _wallet_account2, _wallet_account3;\n    const wallet = (0,_tonconnect_ui_react__WEBPACK_IMPORTED_MODULE_1__.useTonWallet)();\n    const [tonConnectUI] = (0,_tonconnect_ui_react__WEBPACK_IMPORTED_MODULE_1__.useTonConnectUI)();\n    const [state, setState] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({\n        purchases: [],\n        totalPurchased: 0,\n        totalInvested: 0,\n        isLoading: false,\n        error: null,\n        isRefunding: null\n    });\n    // Calculate user purchase contract address\n    const getUserPurchaseAddress = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useUserPurchases.useCallback[getUserPurchaseAddress]\": async (userAddress)=>{\n            try {\n                const client = getTonClient();\n                // Import the auction contract to get the user purchase address\n                const { OnionAuction } = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_src_lib_onionAuction_ts\").then(__webpack_require__.bind(__webpack_require__, /*! ../lib/onionAuction */ \"(app-pages-browser)/./src/lib/onionAuction.ts\"));\n                const auction = client.open(OnionAuction.createFromAddress(_ton_ton__WEBPACK_IMPORTED_MODULE_2__.Address.parse(AUCTION_CONTRACT_ADDRESS)));\n                const userPurchaseAddress = await auction.getUserPurchaseAddress(_ton_ton__WEBPACK_IMPORTED_MODULE_2__.Address.parse(userAddress));\n                return userPurchaseAddress;\n            } catch (error) {\n                console.error('Error getting user purchase address:', error);\n                return null;\n            }\n        }\n    }[\"useUserPurchases.useCallback[getUserPurchaseAddress]\"], []);\n    // Check if contract is deployed\n    const isContractDeployed = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useUserPurchases.useCallback[isContractDeployed]\": async (address)=>{\n            try {\n                const client = getTonClient();\n                const account = await client.getContractState(address);\n                return account.state === 'active';\n            } catch (error) {\n                console.error('Error checking contract deployment:', error);\n                return false;\n            }\n        }\n    }[\"useUserPurchases.useCallback[isContractDeployed]\"], []);\n    // Fetch purchases from the blockchain\n    const fetchPurchases = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useUserPurchases.useCallback[fetchPurchases]\": async ()=>{\n            var _wallet_account;\n            if (!(wallet === null || wallet === void 0 ? void 0 : (_wallet_account = wallet.account) === null || _wallet_account === void 0 ? void 0 : _wallet_account.address)) {\n                setState({\n                    \"useUserPurchases.useCallback[fetchPurchases]\": (prev)=>({\n                            ...prev,\n                            purchases: [],\n                            totalPurchased: 0,\n                            totalInvested: 0,\n                            isLoading: false,\n                            error: null\n                        })\n                }[\"useUserPurchases.useCallback[fetchPurchases]\"]);\n                return;\n            }\n            setState({\n                \"useUserPurchases.useCallback[fetchPurchases]\": (prev)=>({\n                        ...prev,\n                        isLoading: true,\n                        error: null\n                    })\n            }[\"useUserPurchases.useCallback[fetchPurchases]\"]);\n            try {\n                const userPurchaseAddress = await getUserPurchaseAddress(wallet.account.address);\n                if (!userPurchaseAddress) {\n                    throw new Error('Failed to get user purchase contract address');\n                }\n                // Check if the contract is deployed\n                const isDeployed = await isContractDeployed(userPurchaseAddress);\n                if (!isDeployed) {\n                    // Contract not deployed means no purchases yet\n                    setState({\n                        \"useUserPurchases.useCallback[fetchPurchases]\": (prev)=>({\n                                ...prev,\n                                purchases: [],\n                                totalPurchased: 0,\n                                totalInvested: 0,\n                                isLoading: false,\n                                error: null\n                            })\n                    }[\"useUserPurchases.useCallback[fetchPurchases]\"]);\n                    return;\n                }\n                const client = getTonClient();\n                const userPurchase = client.open(_wrappers_UserPurchase__WEBPACK_IMPORTED_MODULE_3__.UserPurchase.createFromAddress(userPurchaseAddress));\n                // Get purchase count\n                const purchaseCount = await userPurchase.getPurchaseIdCounter();\n                if (purchaseCount === 0) {\n                    setState({\n                        \"useUserPurchases.useCallback[fetchPurchases]\": (prev)=>({\n                                ...prev,\n                                purchases: [],\n                                totalPurchased: 0,\n                                totalInvested: 0,\n                                isLoading: false,\n                                error: null\n                            })\n                    }[\"useUserPurchases.useCallback[fetchPurchases]\"]);\n                    return;\n                }\n                // Fetch all purchase records\n                const purchases = [];\n                let totalTokens = 0;\n                let totalInvestedUSD = 0;\n                for(let i = 1; i <= purchaseCount; i++){\n                    try {\n                        const args = new _ton_ton__WEBPACK_IMPORTED_MODULE_2__.TupleBuilder();\n                        args.writeNumber(i);\n                        const result = await client.runMethod(userPurchase.address, 'purchase_details', args.build());\n                        const purchaseRecord = result.stack.readTuple().readTuple();\n                        const isRefunded = await userPurchase.isRefunded(i);\n                        if (purchaseRecord) {\n                            const amountInUnits = Number(purchaseRecord.amount) / ********** // Convert from nanotons\n                            ;\n                            const tokensInUnits = Number(purchaseRecord.tokens) / ********** // Convert from nanotons\n                            ;\n                            const price = amountInUnits / tokensInUnits // Price per token\n                            ;\n                            // Convert timestamp to readable format\n                            const timestamp = new Date(purchaseRecord.timestamp * 1000).toLocaleString('en-US', {\n                                year: 'numeric',\n                                month: '2-digit',\n                                day: '2-digit',\n                                hour: '2-digit',\n                                minute: '2-digit',\n                                timeZone: 'UTC',\n                                timeZoneName: 'short'\n                            });\n                            const purchase = {\n                                id: purchaseRecord.id,\n                                amount: amountInUnits,\n                                currency: purchaseRecord.currency === 0 ? 'TON' : 'USDT',\n                                tokens: tokensInUnits,\n                                price: price,\n                                timestamp: timestamp,\n                                status: isRefunded ? 'refunded' : 'completed',\n                                canRefund: !isRefunded,\n                                nonce: purchaseRecord.nonce.toString(),\n                                purchase_method: purchaseRecord.purchase_method === 0 ? 'direct' : 'signature_verified'\n                            };\n                            purchases.push(purchase);\n                            // Calculate totals (excluding refunded purchases)\n                            if (!isRefunded) {\n                                totalTokens += tokensInUnits;\n                                // Convert to USD for total invested calculation\n                                const usdValue = purchaseRecord.currency === 0 ? amountInUnits * 5.5 : amountInUnits // Assume TON = $5.5\n                                ;\n                                totalInvestedUSD += usdValue;\n                            }\n                        }\n                    } catch (error) {\n                        console.error(\"Error fetching purchase \".concat(i, \":\"), error);\n                    // Continue with other purchases\n                    }\n                }\n                setState({\n                    \"useUserPurchases.useCallback[fetchPurchases]\": (prev)=>({\n                            ...prev,\n                            purchases: purchases.reverse(),\n                            totalPurchased: totalTokens,\n                            totalInvested: totalInvestedUSD,\n                            isLoading: false,\n                            error: null\n                        })\n                }[\"useUserPurchases.useCallback[fetchPurchases]\"]);\n            } catch (error) {\n                console.error('Error fetching purchases:', error);\n                setState({\n                    \"useUserPurchases.useCallback[fetchPurchases]\": (prev)=>({\n                            ...prev,\n                            isLoading: false,\n                            error: error instanceof Error ? error.message : 'Failed to fetch purchases'\n                        })\n                }[\"useUserPurchases.useCallback[fetchPurchases]\"]);\n            }\n        }\n    }[\"useUserPurchases.useCallback[fetchPurchases]\"], [\n        wallet === null || wallet === void 0 ? void 0 : (_wallet_account = wallet.account) === null || _wallet_account === void 0 ? void 0 : _wallet_account.address,\n        getUserPurchaseAddress,\n        isContractDeployed\n    ]);\n    // Refresh purchases\n    const refreshPurchases = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useUserPurchases.useCallback[refreshPurchases]\": ()=>{\n            fetchPurchases();\n        }\n    }[\"useUserPurchases.useCallback[refreshPurchases]\"], [\n        fetchPurchases\n    ]);\n    // Request refund\n    const requestRefund = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useUserPurchases.useCallback[requestRefund]\": async (purchaseId)=>{\n            var _wallet_account;\n            if (!(wallet === null || wallet === void 0 ? void 0 : (_wallet_account = wallet.account) === null || _wallet_account === void 0 ? void 0 : _wallet_account.address) || !tonConnectUI) {\n                throw new Error('Wallet not connected');\n            }\n            setState({\n                \"useUserPurchases.useCallback[requestRefund]\": (prev)=>({\n                        ...prev,\n                        isRefunding: purchaseId\n                    })\n            }[\"useUserPurchases.useCallback[requestRefund]\"]);\n            try {\n                const userPurchaseAddress = await getUserPurchaseAddress(wallet.account.address);\n                if (!userPurchaseAddress) {\n                    throw new Error('Failed to get user purchase contract address');\n                }\n                // Create refund transaction\n                const refundBody = (0,_ton_ton__WEBPACK_IMPORTED_MODULE_2__.beginCell)().storeUint(0x31d5b5ac, 32) // Refund op code\n                .storeUint(purchaseId, 32) // purchase_id\n                .endCell();\n                const transactionRequest = {\n                    validUntil: Math.floor(Date.now() / 1000) + 600,\n                    messages: [\n                        {\n                            address: userPurchaseAddress.toString(),\n                            amount: (0.1 * **********).toString(),\n                            payload: Buffer.from(refundBody.toBoc()).toString('base64')\n                        }\n                    ]\n                };\n                const result = await tonConnectUI.sendTransaction(transactionRequest);\n                console.log('Refund transaction sent:', result);\n                // Refresh purchases after successful refund\n                setTimeout({\n                    \"useUserPurchases.useCallback[requestRefund]\": ()=>{\n                        refreshPurchases();\n                    }\n                }[\"useUserPurchases.useCallback[requestRefund]\"], 3000); // Wait 3 seconds for transaction to be processed\n                return true;\n            } catch (error) {\n                console.error('Refund failed:', error);\n                throw error;\n            } finally{\n                setState({\n                    \"useUserPurchases.useCallback[requestRefund]\": (prev)=>({\n                            ...prev,\n                            isRefunding: null\n                        })\n                }[\"useUserPurchases.useCallback[requestRefund]\"]);\n            }\n        }\n    }[\"useUserPurchases.useCallback[requestRefund]\"], [\n        wallet === null || wallet === void 0 ? void 0 : (_wallet_account1 = wallet.account) === null || _wallet_account1 === void 0 ? void 0 : _wallet_account1.address,\n        tonConnectUI,\n        getUserPurchaseAddress,\n        refreshPurchases\n    ]);\n    // Auto-fetch purchases when wallet connects\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useUserPurchases.useEffect\": ()=>{\n            var _wallet_account;\n            if (wallet === null || wallet === void 0 ? void 0 : (_wallet_account = wallet.account) === null || _wallet_account === void 0 ? void 0 : _wallet_account.address) {\n                fetchPurchases();\n            }\n        }\n    }[\"useUserPurchases.useEffect\"], [\n        wallet === null || wallet === void 0 ? void 0 : (_wallet_account2 = wallet.account) === null || _wallet_account2 === void 0 ? void 0 : _wallet_account2.address,\n        fetchPurchases\n    ]);\n    return {\n        ...state,\n        refreshPurchases,\n        requestRefund,\n        isConnected: !!(wallet === null || wallet === void 0 ? void 0 : (_wallet_account3 = wallet.account) === null || _wallet_account3 === void 0 ? void 0 : _wallet_account3.address)\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useUserPurchases.ts\n"));

/***/ })

});