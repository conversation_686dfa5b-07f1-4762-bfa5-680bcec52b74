"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_src_lib_onionAuction_ts"],{

/***/ "(app-pages-browser)/./src/lib/onionAuction.ts":
/*!*********************************!*\
  !*** ./src/lib/onionAuction.ts ***!
  \*********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OnionAuction: () => (/* binding */ OnionAuction),\n/* harmony export */   onionAuctionConfigToCell: () => (/* binding */ onionAuctionConfigToCell)\n/* harmony export */ });\n/* harmony import */ var _ton_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @ton/core */ \"(app-pages-browser)/./node_modules/@ton/core/dist/index.js\");\n/* harmony import */ var _ton_core__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_ton_core__WEBPACK_IMPORTED_MODULE_0__);\n\nfunction onionAuctionConfigToCell(config) {\n    return (0,_ton_core__WEBPACK_IMPORTED_MODULE_0__.beginCell)().storeAddress(config.owner).storeUint(config.startTime, 64).storeUint(config.endTime, 64).storeCoins(config.softCap).storeCoins(config.hardCap).storeCoins(config.totalSupply).endCell();\n}\nclass OnionAuction {\n    static createFromAddress(address) {\n        return new OnionAuction(address);\n    }\n    static createFromConfig(config, code) {\n        let workchain = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 0;\n        const data = onionAuctionConfigToCell(config);\n        const init = {\n            code,\n            data\n        };\n        return new OnionAuction((0,_ton_core__WEBPACK_IMPORTED_MODULE_0__.contractAddress)(workchain, init), init);\n    }\n    async sendDeploy(provider, via, value) {\n        await provider.internal(via, {\n            value,\n            sendMode: _ton_core__WEBPACK_IMPORTED_MODULE_0__.SendMode.PAY_GAS_SEPARATELY,\n            body: (0,_ton_core__WEBPACK_IMPORTED_MODULE_0__.beginCell)().endCell()\n        });\n    }\n    async sendPurchase(provider, via, opts) {\n        await provider.internal(via, {\n            value: opts.value,\n            sendMode: _ton_core__WEBPACK_IMPORTED_MODULE_0__.SendMode.PAY_GAS_SEPARATELY,\n            body: (0,_ton_core__WEBPACK_IMPORTED_MODULE_0__.beginCell)().storeUint(0x1234, 32) // op code for Purchase\n            .storeUint(0, 64) // query_id\n            .storeCoins(opts.amount).storeUint(opts.currency, 8).endCell()\n        });\n    }\n    async sendRefund(provider, via, opts) {\n        await provider.internal(via, {\n            value: opts.value,\n            sendMode: _ton_core__WEBPACK_IMPORTED_MODULE_0__.SendMode.PAY_GAS_SEPARATELY,\n            body: (0,_ton_core__WEBPACK_IMPORTED_MODULE_0__.beginCell)().storeUint(0x5678, 32) // op code for Refund\n            .storeUint(0, 64) // query_id\n            .storeUint(opts.purchaseId, 32).endCell()\n        });\n    }\n    async getAuctionInfo(provider) {\n        const result = await provider.get('auction_info', []);\n        return {\n            startTime: result.stack.readNumber(),\n            endTime: result.stack.readNumber(),\n            softCap: result.stack.readBigNumber(),\n            hardCap: result.stack.readBigNumber(),\n            totalSupply: result.stack.readBigNumber(),\n            refundFeePercent: result.stack.readNumber()\n        };\n    }\n    async getCurrentRound(provider) {\n        const result = await provider.get('current_round', []);\n        return result.stack.readNumber();\n    }\n    async getCurrentPrice(provider) {\n        const result = await provider.get('current_price', []);\n        return result.stack.readBigNumber();\n    }\n    async getTotalRaised(provider) {\n        const result = await provider.get('total_raised', []);\n        return result.stack.readBigNumber();\n    }\n    async getTotalTokensSold(provider) {\n        const result = await provider.get('total_tokens_sold', []);\n        return result.stack.readBigNumber();\n    }\n    async getAuctionStatus(provider) {\n        const result = await provider.get('auction_status', []);\n        return result.stack.readNumber();\n    }\n    async getRemainingTokens(provider) {\n        const result = await provider.get('remaining_tokens', []);\n        return result.stack.readBigNumber();\n    }\n    async getUserPurchaseAddress(provider, user) {\n        const result = await provider.get('user_purchase_address', [\n            {\n                type: 'slice',\n                cell: (0,_ton_core__WEBPACK_IMPORTED_MODULE_0__.beginCell)().storeAddress(user).endCell()\n            }\n        ]);\n        return result.stack.readAddressOpt();\n    }\n    async isAuctionActive(provider) {\n        const result = await provider.get('is_auction_active', []);\n        return result.stack.readBoolean();\n    }\n    constructor(address, init){\n        this.address = address;\n        this.init = init;\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/onionAuction.ts\n"));

/***/ })

}]);