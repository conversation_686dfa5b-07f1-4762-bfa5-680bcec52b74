"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/dataloader";
exports.ids = ["vendor-chunks/dataloader"];
exports.modules = {

/***/ "(ssr)/./node_modules/dataloader/index.js":
/*!******************************************!*\
  !*** ./node_modules/dataloader/index.js ***!
  \******************************************/
/***/ ((module) => {

eval("\n\n/**\n * Copyright (c) 2019-present, GraphQL Foundation\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n// A Function, which when given an Array of keys, returns a Promise of an Array\n// of values or Errors.\n// Optionally turn off batching or caching or provide a cache key function or a\n// custom cache instance.\n// If a custom cache is provided, it must be of this type (a subset of ES6 Map).\n\n/**\n * A `DataLoader` creates a public API for loading data from a particular\n * data back-end with unique keys such as the `id` column of a SQL table or\n * document name in a MongoDB database, given a batch loading function.\n *\n * Each `DataLoader` instance contains a unique memoized cache. Use caution when\n * used in long-lived applications or those which serve many users with\n * different access permissions and consider creating a new instance per\n * web request.\n */\nvar DataLoader =\n/*#__PURE__*/\nfunction () {\n  function DataLoader(batchLoadFn, options) {\n    if (typeof batchLoadFn !== 'function') {\n      throw new TypeError('DataLoader must be constructed with a function which accepts ' + (\"Array<key> and returns Promise<Array<value>>, but got: \" + batchLoadFn + \".\"));\n    }\n\n    this._batchLoadFn = batchLoadFn;\n    this._maxBatchSize = getValidMaxBatchSize(options);\n    this._batchScheduleFn = getValidBatchScheduleFn(options);\n    this._cacheKeyFn = getValidCacheKeyFn(options);\n    this._cacheMap = getValidCacheMap(options);\n    this._batch = null;\n    this.name = getValidName(options);\n  } // Private\n\n\n  var _proto = DataLoader.prototype;\n\n  /**\n   * Loads a key, returning a `Promise` for the value represented by that key.\n   */\n  _proto.load = function load(key) {\n    if (key === null || key === undefined) {\n      throw new TypeError('The loader.load() function must be called with a value, ' + (\"but got: \" + String(key) + \".\"));\n    }\n\n    var batch = getCurrentBatch(this);\n    var cacheMap = this._cacheMap;\n    var cacheKey; // If caching and there is a cache-hit, return cached Promise.\n\n    if (cacheMap) {\n      cacheKey = this._cacheKeyFn(key);\n      var cachedPromise = cacheMap.get(cacheKey);\n\n      if (cachedPromise) {\n        var cacheHits = batch.cacheHits || (batch.cacheHits = []);\n        return new Promise(function (resolve) {\n          cacheHits.push(function () {\n            resolve(cachedPromise);\n          });\n        });\n      }\n    } // Otherwise, produce a new Promise for this key, and enqueue it to be\n    // dispatched along with the current batch.\n\n\n    batch.keys.push(key);\n    var promise = new Promise(function (resolve, reject) {\n      batch.callbacks.push({\n        resolve: resolve,\n        reject: reject\n      });\n    }); // If caching, cache this promise.\n\n    if (cacheMap) {\n      cacheMap.set(cacheKey, promise);\n    }\n\n    return promise;\n  }\n  /**\n   * Loads multiple keys, promising an array of values:\n   *\n   *     var [ a, b ] = await myLoader.loadMany([ 'a', 'b' ]);\n   *\n   * This is similar to the more verbose:\n   *\n   *     var [ a, b ] = await Promise.all([\n   *       myLoader.load('a'),\n   *       myLoader.load('b')\n   *     ]);\n   *\n   * However it is different in the case where any load fails. Where\n   * Promise.all() would reject, loadMany() always resolves, however each result\n   * is either a value or an Error instance.\n   *\n   *     var [ a, b, c ] = await myLoader.loadMany([ 'a', 'b', 'badkey' ]);\n   *     // c instanceof Error\n   *\n   */\n  ;\n\n  _proto.loadMany = function loadMany(keys) {\n    if (!isArrayLike(keys)) {\n      throw new TypeError('The loader.loadMany() function must be called with Array<key> ' + (\"but got: \" + keys + \".\"));\n    } // Support ArrayLike by using only minimal property access\n\n\n    var loadPromises = [];\n\n    for (var i = 0; i < keys.length; i++) {\n      loadPromises.push(this.load(keys[i])[\"catch\"](function (error) {\n        return error;\n      }));\n    }\n\n    return Promise.all(loadPromises);\n  }\n  /**\n   * Clears the value at `key` from the cache, if it exists. Returns itself for\n   * method chaining.\n   */\n  ;\n\n  _proto.clear = function clear(key) {\n    var cacheMap = this._cacheMap;\n\n    if (cacheMap) {\n      var cacheKey = this._cacheKeyFn(key);\n\n      cacheMap[\"delete\"](cacheKey);\n    }\n\n    return this;\n  }\n  /**\n   * Clears the entire cache. To be used when some event results in unknown\n   * invalidations across this particular `DataLoader`. Returns itself for\n   * method chaining.\n   */\n  ;\n\n  _proto.clearAll = function clearAll() {\n    var cacheMap = this._cacheMap;\n\n    if (cacheMap) {\n      cacheMap.clear();\n    }\n\n    return this;\n  }\n  /**\n   * Adds the provided key and value to the cache. If the key already\n   * exists, no change is made. Returns itself for method chaining.\n   *\n   * To prime the cache with an error at a key, provide an Error instance.\n   */\n  ;\n\n  _proto.prime = function prime(key, value) {\n    var cacheMap = this._cacheMap;\n\n    if (cacheMap) {\n      var cacheKey = this._cacheKeyFn(key); // Only add the key if it does not already exist.\n\n\n      if (cacheMap.get(cacheKey) === undefined) {\n        // Cache a rejected promise if the value is an Error, in order to match\n        // the behavior of load(key).\n        var promise;\n\n        if (value instanceof Error) {\n          promise = Promise.reject(value); // Since this is a case where an Error is intentionally being primed\n          // for a given key, we want to disable unhandled promise rejection.\n\n          promise[\"catch\"](function () {});\n        } else {\n          promise = Promise.resolve(value);\n        }\n\n        cacheMap.set(cacheKey, promise);\n      }\n    }\n\n    return this;\n  }\n  /**\n   * The name given to this `DataLoader` instance. Useful for APM tools.\n   *\n   * Is `null` if not set in the constructor.\n   */\n  ;\n\n  return DataLoader;\n}(); // Private: Enqueue a Job to be executed after all \"PromiseJobs\" Jobs.\n//\n// ES6 JavaScript uses the concepts Job and JobQueue to schedule work to occur\n// after the current execution context has completed:\n// http://www.ecma-international.org/ecma-262/6.0/#sec-jobs-and-job-queues\n//\n// Node.js uses the `process.nextTick` mechanism to implement the concept of a\n// Job, maintaining a global FIFO JobQueue for all Jobs, which is flushed after\n// the current call stack ends.\n//\n// When calling `then` on a Promise, it enqueues a Job on a specific\n// \"PromiseJobs\" JobQueue which is flushed in Node as a single Job on the\n// global JobQueue.\n//\n// DataLoader batches all loads which occur in a single frame of execution, but\n// should include in the batch all loads which occur during the flushing of the\n// \"PromiseJobs\" JobQueue after that same execution frame.\n//\n// In order to avoid the DataLoader dispatch Job occuring before \"PromiseJobs\",\n// A Promise Job is created with the sole purpose of enqueuing a global Job,\n// ensuring that it always occurs after \"PromiseJobs\" ends.\n//\n// Node.js's job queue is unique. Browsers do not have an equivalent mechanism\n// for enqueuing a job to be performed after promise microtasks and before the\n// next macrotask. For browser environments, a macrotask is used (via\n// setImmediate or setTimeout) at a potential performance penalty.\n\n\nvar enqueuePostPromiseJob = typeof process === 'object' && typeof process.nextTick === 'function' ? function (fn) {\n  if (!resolvedPromise) {\n    resolvedPromise = Promise.resolve();\n  }\n\n  resolvedPromise.then(function () {\n    process.nextTick(fn);\n  });\n} : typeof setImmediate === 'function' ? function (fn) {\n  setImmediate(fn);\n} : function (fn) {\n  setTimeout(fn);\n}; // Private: cached resolved Promise instance\n\nvar resolvedPromise; // Private: Describes a batch of requests\n\n// Private: Either returns the current batch, or creates and schedules a\n// dispatch of a new batch for the given loader.\nfunction getCurrentBatch(loader) {\n  // If there is an existing batch which has not yet dispatched and is within\n  // the limit of the batch size, then return it.\n  var existingBatch = loader._batch;\n\n  if (existingBatch !== null && !existingBatch.hasDispatched && existingBatch.keys.length < loader._maxBatchSize) {\n    return existingBatch;\n  } // Otherwise, create a new batch for this loader.\n\n\n  var newBatch = {\n    hasDispatched: false,\n    keys: [],\n    callbacks: []\n  }; // Store it on the loader so it may be reused.\n\n  loader._batch = newBatch; // Then schedule a task to dispatch this batch of requests.\n\n  loader._batchScheduleFn(function () {\n    dispatchBatch(loader, newBatch);\n  });\n\n  return newBatch;\n}\n\nfunction dispatchBatch(loader, batch) {\n  // Mark this batch as having been dispatched.\n  batch.hasDispatched = true; // If there's nothing to load, resolve any cache hits and return early.\n\n  if (batch.keys.length === 0) {\n    resolveCacheHits(batch);\n    return;\n  } // Call the provided batchLoadFn for this loader with the batch's keys and\n  // with the loader as the `this` context.\n\n\n  var batchPromise;\n\n  try {\n    batchPromise = loader._batchLoadFn(batch.keys);\n  } catch (e) {\n    return failedDispatch(loader, batch, new TypeError('DataLoader must be constructed with a function which accepts ' + 'Array<key> and returns Promise<Array<value>>, but the function ' + (\"errored synchronously: \" + String(e) + \".\")));\n  } // Assert the expected response from batchLoadFn\n\n\n  if (!batchPromise || typeof batchPromise.then !== 'function') {\n    return failedDispatch(loader, batch, new TypeError('DataLoader must be constructed with a function which accepts ' + 'Array<key> and returns Promise<Array<value>>, but the function did ' + (\"not return a Promise: \" + String(batchPromise) + \".\")));\n  } // Await the resolution of the call to batchLoadFn.\n\n\n  batchPromise.then(function (values) {\n    // Assert the expected resolution from batchLoadFn.\n    if (!isArrayLike(values)) {\n      throw new TypeError('DataLoader must be constructed with a function which accepts ' + 'Array<key> and returns Promise<Array<value>>, but the function did ' + (\"not return a Promise of an Array: \" + String(values) + \".\"));\n    }\n\n    if (values.length !== batch.keys.length) {\n      throw new TypeError('DataLoader must be constructed with a function which accepts ' + 'Array<key> and returns Promise<Array<value>>, but the function did ' + 'not return a Promise of an Array of the same length as the Array ' + 'of keys.' + (\"\\n\\nKeys:\\n\" + String(batch.keys)) + (\"\\n\\nValues:\\n\" + String(values)));\n    } // Resolve all cache hits in the same micro-task as freshly loaded values.\n\n\n    resolveCacheHits(batch); // Step through values, resolving or rejecting each Promise in the batch.\n\n    for (var i = 0; i < batch.callbacks.length; i++) {\n      var _value = values[i];\n\n      if (_value instanceof Error) {\n        batch.callbacks[i].reject(_value);\n      } else {\n        batch.callbacks[i].resolve(_value);\n      }\n    }\n  })[\"catch\"](function (error) {\n    failedDispatch(loader, batch, error);\n  });\n} // Private: do not cache individual loads if the entire batch dispatch fails,\n// but still reject each request so they do not hang.\n\n\nfunction failedDispatch(loader, batch, error) {\n  // Cache hits are resolved, even though the batch failed.\n  resolveCacheHits(batch);\n\n  for (var i = 0; i < batch.keys.length; i++) {\n    loader.clear(batch.keys[i]);\n    batch.callbacks[i].reject(error);\n  }\n} // Private: Resolves the Promises for any cache hits in this batch.\n\n\nfunction resolveCacheHits(batch) {\n  if (batch.cacheHits) {\n    for (var i = 0; i < batch.cacheHits.length; i++) {\n      batch.cacheHits[i]();\n    }\n  }\n} // Private: given the DataLoader's options, produce a valid max batch size.\n\n\nfunction getValidMaxBatchSize(options) {\n  var shouldBatch = !options || options.batch !== false;\n\n  if (!shouldBatch) {\n    return 1;\n  }\n\n  var maxBatchSize = options && options.maxBatchSize;\n\n  if (maxBatchSize === undefined) {\n    return Infinity;\n  }\n\n  if (typeof maxBatchSize !== 'number' || maxBatchSize < 1) {\n    throw new TypeError(\"maxBatchSize must be a positive number: \" + maxBatchSize);\n  }\n\n  return maxBatchSize;\n} // Private\n\n\nfunction getValidBatchScheduleFn(options) {\n  var batchScheduleFn = options && options.batchScheduleFn;\n\n  if (batchScheduleFn === undefined) {\n    return enqueuePostPromiseJob;\n  }\n\n  if (typeof batchScheduleFn !== 'function') {\n    throw new TypeError(\"batchScheduleFn must be a function: \" + batchScheduleFn);\n  }\n\n  return batchScheduleFn;\n} // Private: given the DataLoader's options, produce a cache key function.\n\n\nfunction getValidCacheKeyFn(options) {\n  var cacheKeyFn = options && options.cacheKeyFn;\n\n  if (cacheKeyFn === undefined) {\n    return function (key) {\n      return key;\n    };\n  }\n\n  if (typeof cacheKeyFn !== 'function') {\n    throw new TypeError(\"cacheKeyFn must be a function: \" + cacheKeyFn);\n  }\n\n  return cacheKeyFn;\n} // Private: given the DataLoader's options, produce a CacheMap to be used.\n\n\nfunction getValidCacheMap(options) {\n  var shouldCache = !options || options.cache !== false;\n\n  if (!shouldCache) {\n    return null;\n  }\n\n  var cacheMap = options && options.cacheMap;\n\n  if (cacheMap === undefined) {\n    return new Map();\n  }\n\n  if (cacheMap !== null) {\n    var cacheFunctions = ['get', 'set', 'delete', 'clear'];\n    var missingFunctions = cacheFunctions.filter(function (fnName) {\n      return cacheMap && typeof cacheMap[fnName] !== 'function';\n    });\n\n    if (missingFunctions.length !== 0) {\n      throw new TypeError('Custom cacheMap missing methods: ' + missingFunctions.join(', '));\n    }\n  }\n\n  return cacheMap;\n}\n\nfunction getValidName(options) {\n  if (options && options.name) {\n    return options.name;\n  }\n\n  return null;\n} // Private\n\n\nfunction isArrayLike(x) {\n  return typeof x === 'object' && x !== null && typeof x.length === 'number' && (x.length === 0 || x.length > 0 && Object.prototype.hasOwnProperty.call(x, x.length - 1));\n}\n\nmodule.exports = DataLoader;//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dataloader/index.js\n");

/***/ })

};
;