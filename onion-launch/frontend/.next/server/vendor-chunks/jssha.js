/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/jssha";
exports.ids = ["vendor-chunks/jssha"];
exports.modules = {

/***/ "(ssr)/../node_modules/jssha/dist/sha.js":
/*!*****************************************!*\
  !*** ../node_modules/jssha/dist/sha.js ***!
  \*****************************************/
/***/ (function(module) {

eval("/**\n * A JavaScript implementation of the SHA family of hashes - defined in FIPS PUB 180-4, FIPS PUB 202,\n * and SP 800-185 - as well as the corresponding HMAC implementation as defined in FIPS PUB 198-1.\n *\n * Copyright 2008-2020 Brian Turek, 1998-2009 Paul Johnston & Contributors\n * Distributed under the BSD License\n * See http://caligatio.github.com/jsSHA/ for more information\n *\n * Two ECMAScript polyfill functions carry the following license:\n *\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\"); you may not use this file except in compliance with\n * the License. You may obtain a copy of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * THIS CODE IS PROVIDED ON AN *AS IS* BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, EITHER EXPRESS OR IMPLIED,\n * INCLUDING WITHOUT LIMITATION ANY IMPLIED WARRANTIES OR CONDITIONS OF TITLE, FITNESS FOR A PARTICULAR PURPOSE,\n * MERCHANTABLITY OR NON-INFRINGEMENT.\n *\n * See the Apache Version 2.0 License for specific language governing permissions and limitations under the License.\n */\n!function(n,r){ true?module.exports=r():0}(this,(function(){\"use strict\";var n=\"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/\";function r(n,r,t,e){var i,o,u,f=r||[0],w=(t=t||0)>>>3,s=-1===e?3:0;for(i=0;i<n.length;i+=1)o=(u=i+w)>>>2,f.length<=o&&f.push(0),f[o]|=n[i]<<8*(s+e*(u%4));return{value:f,binLen:8*n.length+t}}function t(t,e,i){switch(e){case\"UTF8\":case\"UTF16BE\":case\"UTF16LE\":break;default:throw new Error(\"encoding must be UTF8, UTF16BE, or UTF16LE\")}switch(t){case\"HEX\":return function(n,r,t){return function(n,r,t,e){var i,o,u,f;if(0!=n.length%2)throw new Error(\"String of HEX type must be in byte increments\");var w=r||[0],s=(t=t||0)>>>3,a=-1===e?3:0;for(i=0;i<n.length;i+=2){if(o=parseInt(n.substr(i,2),16),isNaN(o))throw new Error(\"String of HEX type contains invalid characters\");for(u=(f=(i>>>1)+s)>>>2;w.length<=u;)w.push(0);w[u]|=o<<8*(a+e*(f%4))}return{value:w,binLen:4*n.length+t}}(n,r,t,i)};case\"TEXT\":return function(n,r,t){return function(n,r,t,e,i){var o,u,f,w,s,a,h,c,v=0,A=t||[0],E=(e=e||0)>>>3;if(\"UTF8\"===r)for(h=-1===i?3:0,f=0;f<n.length;f+=1)for(u=[],128>(o=n.charCodeAt(f))?u.push(o):2048>o?(u.push(192|o>>>6),u.push(128|63&o)):55296>o||57344<=o?u.push(224|o>>>12,128|o>>>6&63,128|63&o):(f+=1,o=65536+((1023&o)<<10|1023&n.charCodeAt(f)),u.push(240|o>>>18,128|o>>>12&63,128|o>>>6&63,128|63&o)),w=0;w<u.length;w+=1){for(s=(a=v+E)>>>2;A.length<=s;)A.push(0);A[s]|=u[w]<<8*(h+i*(a%4)),v+=1}else for(h=-1===i?2:0,c=\"UTF16LE\"===r&&1!==i||\"UTF16LE\"!==r&&1===i,f=0;f<n.length;f+=1){for(o=n.charCodeAt(f),!0===c&&(o=(w=255&o)<<8|o>>>8),s=(a=v+E)>>>2;A.length<=s;)A.push(0);A[s]|=o<<8*(h+i*(a%4)),v+=2}return{value:A,binLen:8*v+e}}(n,e,r,t,i)};case\"B64\":return function(r,t,e){return function(r,t,e,i){var o,u,f,w,s,a,h=0,c=t||[0],v=(e=e||0)>>>3,A=-1===i?3:0,E=r.indexOf(\"=\");if(-1===r.search(/^[a-zA-Z0-9=+/]+$/))throw new Error(\"Invalid character in base-64 string\");if(r=r.replace(/=/g,\"\"),-1!==E&&E<r.length)throw new Error(\"Invalid '=' found in base-64 string\");for(o=0;o<r.length;o+=4){for(w=r.substr(o,4),f=0,u=0;u<w.length;u+=1)f|=n.indexOf(w.charAt(u))<<18-6*u;for(u=0;u<w.length-1;u+=1){for(s=(a=h+v)>>>2;c.length<=s;)c.push(0);c[s]|=(f>>>16-8*u&255)<<8*(A+i*(a%4)),h+=1}}return{value:c,binLen:8*h+e}}(r,t,e,i)};case\"BYTES\":return function(n,r,t){return function(n,r,t,e){var i,o,u,f,w=r||[0],s=(t=t||0)>>>3,a=-1===e?3:0;for(o=0;o<n.length;o+=1)i=n.charCodeAt(o),u=(f=o+s)>>>2,w.length<=u&&w.push(0),w[u]|=i<<8*(a+e*(f%4));return{value:w,binLen:8*n.length+t}}(n,r,t,i)};case\"ARRAYBUFFER\":try{new ArrayBuffer(0)}catch(n){throw new Error(\"ARRAYBUFFER not supported by this environment\")}return function(n,t,e){return function(n,t,e,i){return r(new Uint8Array(n),t,e,i)}(n,t,e,i)};case\"UINT8ARRAY\":try{new Uint8Array(0)}catch(n){throw new Error(\"UINT8ARRAY not supported by this environment\")}return function(n,t,e){return r(n,t,e,i)};default:throw new Error(\"format must be HEX, TEXT, B64, BYTES, ARRAYBUFFER, or UINT8ARRAY\")}}function e(r,t,e,i){switch(r){case\"HEX\":return function(n){return function(n,r,t,e){var i,o,u=\"\",f=r/8,w=-1===t?3:0;for(i=0;i<f;i+=1)o=n[i>>>2]>>>8*(w+t*(i%4)),u+=\"0123456789abcdef\".charAt(o>>>4&15)+\"0123456789abcdef\".charAt(15&o);return e.outputUpper?u.toUpperCase():u}(n,t,e,i)};case\"B64\":return function(r){return function(r,t,e,i){var o,u,f,w,s,a=\"\",h=t/8,c=-1===e?3:0;for(o=0;o<h;o+=3)for(w=o+1<h?r[o+1>>>2]:0,s=o+2<h?r[o+2>>>2]:0,f=(r[o>>>2]>>>8*(c+e*(o%4))&255)<<16|(w>>>8*(c+e*((o+1)%4))&255)<<8|s>>>8*(c+e*((o+2)%4))&255,u=0;u<4;u+=1)a+=8*o+6*u<=t?n.charAt(f>>>6*(3-u)&63):i.b64Pad;return a}(r,t,e,i)};case\"BYTES\":return function(n){return function(n,r,t){var e,i,o=\"\",u=r/8,f=-1===t?3:0;for(e=0;e<u;e+=1)i=n[e>>>2]>>>8*(f+t*(e%4))&255,o+=String.fromCharCode(i);return o}(n,t,e)};case\"ARRAYBUFFER\":try{new ArrayBuffer(0)}catch(n){throw new Error(\"ARRAYBUFFER not supported by this environment\")}return function(n){return function(n,r,t){var e,i=r/8,o=new ArrayBuffer(i),u=new Uint8Array(o),f=-1===t?3:0;for(e=0;e<i;e+=1)u[e]=n[e>>>2]>>>8*(f+t*(e%4))&255;return o}(n,t,e)};case\"UINT8ARRAY\":try{new Uint8Array(0)}catch(n){throw new Error(\"UINT8ARRAY not supported by this environment\")}return function(n){return function(n,r,t){var e,i=r/8,o=-1===t?3:0,u=new Uint8Array(i);for(e=0;e<i;e+=1)u[e]=n[e>>>2]>>>8*(o+t*(e%4))&255;return u}(n,t,e)};default:throw new Error(\"format must be HEX, B64, BYTES, ARRAYBUFFER, or UINT8ARRAY\")}}var i=[1116352408,1899447441,3049323471,3921009573,961987163,1508970993,2453635748,2870763221,3624381080,310598401,607225278,1426881987,1925078388,2162078206,2614888103,3248222580,3835390401,4022224774,264347078,604807628,770255983,1249150122,1555081692,1996064986,2554220882,2821834349,2952996808,3210313671,3336571891,3584528711,113926993,338241895,666307205,773529912,1294757372,1396182291,1695183700,1986661051,2177026350,2456956037,2730485921,2820302411,3259730800,3345764771,3516065817,3600352804,4094571909,275423344,430227734,506948616,659060556,883997877,958139571,1322822218,1537002063,1747873779,1955562222,2024104815,2227730452,2361852424,2428436474,2756734187,3204031479,3329325298],o=[3238371032,914150663,812702999,4144912697,4290775857,1750603025,1694076839,3204075428],u=[1779033703,3144134277,1013904242,2773480762,1359893119,2600822924,528734635,1541459225],f=\"Chosen SHA variant is not supported\";function w(n,r){var t,e,i=n.binLen>>>3,o=r.binLen>>>3,u=i<<3,f=4-i<<3;if(i%4!=0){for(t=0;t<o;t+=4)e=i+t>>>2,n.value[e]|=r.value[t>>>2]<<u,n.value.push(0),n.value[e+1]|=r.value[t>>>2]>>>f;return(n.value.length<<2)-4>=o+i&&n.value.pop(),{value:n.value,binLen:n.binLen+r.binLen}}return{value:n.value.concat(r.value),binLen:n.binLen+r.binLen}}function s(n){var r={outputUpper:!1,b64Pad:\"=\",outputLen:-1},t=n||{},e=\"Output length must be a multiple of 8\";if(r.outputUpper=t.outputUpper||!1,t.b64Pad&&(r.b64Pad=t.b64Pad),t.outputLen){if(t.outputLen%8!=0)throw new Error(e);r.outputLen=t.outputLen}else if(t.shakeLen){if(t.shakeLen%8!=0)throw new Error(e);r.outputLen=t.shakeLen}if(\"boolean\"!=typeof r.outputUpper)throw new Error(\"Invalid outputUpper formatting option\");if(\"string\"!=typeof r.b64Pad)throw new Error(\"Invalid b64Pad formatting option\");return r}function a(n,r,e,i){var o=n+\" must include a value and format\";if(!r){if(!i)throw new Error(o);return i}if(void 0===r.value||!r.format)throw new Error(o);return t(r.format,r.encoding||\"UTF8\",e)(r.value)}var h=function(){function n(n,r,t){var e=t||{};if(this.t=r,this.i=e.encoding||\"UTF8\",this.numRounds=e.numRounds||1,isNaN(this.numRounds)||this.numRounds!==parseInt(this.numRounds,10)||1>this.numRounds)throw new Error(\"numRounds must a integer >= 1\");this.o=n,this.u=[],this.s=0,this.h=!1,this.v=0,this.A=!1,this.l=[],this.H=[]}return n.prototype.update=function(n){var r,t=0,e=this.S>>>5,i=this.p(n,this.u,this.s),o=i.binLen,u=i.value,f=o>>>5;for(r=0;r<f;r+=e)t+this.S<=o&&(this.m=this.R(u.slice(r,r+e),this.m),t+=this.S);this.v+=t,this.u=u.slice(t>>>5),this.s=o%this.S,this.h=!0},n.prototype.getHash=function(n,r){var t,i,o=this.U,u=s(r);if(this.T){if(-1===u.outputLen)throw new Error(\"Output length must be specified in options\");o=u.outputLen}var f=e(n,o,this.C,u);if(this.A&&this.F)return f(this.F(u));for(i=this.K(this.u.slice(),this.s,this.v,this.B(this.m),o),t=1;t<this.numRounds;t+=1)this.T&&o%32!=0&&(i[i.length-1]&=16777215>>>24-o%32),i=this.K(i,o,0,this.L(this.o),o);return f(i)},n.prototype.setHMACKey=function(n,r,e){if(!this.g)throw new Error(\"Variant does not support HMAC\");if(this.h)throw new Error(\"Cannot set MAC key after calling update\");var i=t(r,(e||{}).encoding||\"UTF8\",this.C);this.k(i(n))},n.prototype.k=function(n){var r,t=this.S>>>3,e=t/4-1;if(1!==this.numRounds)throw new Error(\"Cannot set numRounds with MAC\");if(this.A)throw new Error(\"MAC key already set\");for(t<n.binLen/8&&(n.value=this.K(n.value,n.binLen,0,this.L(this.o),this.U));n.value.length<=e;)n.value.push(0);for(r=0;r<=e;r+=1)this.l[r]=909522486^n.value[r],this.H[r]=1549556828^n.value[r];this.m=this.R(this.l,this.m),this.v=this.S,this.A=!0},n.prototype.getHMAC=function(n,r){var t=s(r);return e(n,this.U,this.C,t)(this.Y())},n.prototype.Y=function(){var n;if(!this.A)throw new Error(\"Cannot call getHMAC without first setting MAC key\");var r=this.K(this.u.slice(),this.s,this.v,this.B(this.m),this.U);return n=this.R(this.H,this.L(this.o)),n=this.K(r,this.U,this.S,n,this.U)},n}(),c=function(n,r){return(c=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,r){n.__proto__=r}||function(n,r){for(var t in r)Object.prototype.hasOwnProperty.call(r,t)&&(n[t]=r[t])})(n,r)};function v(n,r){function t(){this.constructor=n}c(n,r),n.prototype=null===r?Object.create(r):(t.prototype=r.prototype,new t)}function A(n,r){return n<<r|n>>>32-r}function E(n,r){return n>>>r|n<<32-r}function l(n,r){return n>>>r}function b(n,r,t){return n^r^t}function H(n,r,t){return n&r^~n&t}function d(n,r,t){return n&r^n&t^r&t}function S(n){return E(n,2)^E(n,13)^E(n,22)}function p(n,r){var t=(65535&n)+(65535&r);return(65535&(n>>>16)+(r>>>16)+(t>>>16))<<16|65535&t}function m(n,r,t,e){var i=(65535&n)+(65535&r)+(65535&t)+(65535&e);return(65535&(n>>>16)+(r>>>16)+(t>>>16)+(e>>>16)+(i>>>16))<<16|65535&i}function y(n,r,t,e,i){var o=(65535&n)+(65535&r)+(65535&t)+(65535&e)+(65535&i);return(65535&(n>>>16)+(r>>>16)+(t>>>16)+(e>>>16)+(i>>>16)+(o>>>16))<<16|65535&o}function R(n){return E(n,7)^E(n,18)^l(n,3)}function U(n){return E(n,6)^E(n,11)^E(n,25)}function T(n){return[1732584193,4023233417,2562383102,271733878,3285377520]}function C(n,r){var t,e,i,o,u,f,w,s=[];for(t=r[0],e=r[1],i=r[2],o=r[3],u=r[4],w=0;w<80;w+=1)s[w]=w<16?n[w]:A(s[w-3]^s[w-8]^s[w-14]^s[w-16],1),f=w<20?y(A(t,5),H(e,i,o),u,1518500249,s[w]):w<40?y(A(t,5),b(e,i,o),u,1859775393,s[w]):w<60?y(A(t,5),d(e,i,o),u,2400959708,s[w]):y(A(t,5),b(e,i,o),u,3395469782,s[w]),u=o,o=i,i=A(e,30),e=t,t=f;return r[0]=p(t,r[0]),r[1]=p(e,r[1]),r[2]=p(i,r[2]),r[3]=p(o,r[3]),r[4]=p(u,r[4]),r}function F(n,r,t,e){for(var i,o=15+(r+65>>>9<<4),u=r+t;n.length<=o;)n.push(0);for(n[r>>>5]|=128<<24-r%32,n[o]=4294967295&u,n[o-1]=u/4294967296|0,i=0;i<n.length;i+=16)e=C(n.slice(i,i+16),e);return e}var K=function(n){function r(r,e,i){var o=this;if(\"SHA-1\"!==r)throw new Error(f);var u=i||{};return(o=n.call(this,r,e,i)||this).g=!0,o.F=o.Y,o.C=-1,o.p=t(o.t,o.i,o.C),o.R=C,o.B=function(n){return n.slice()},o.L=T,o.K=F,o.m=[1732584193,4023233417,2562383102,271733878,3285377520],o.S=512,o.U=160,o.T=!1,u.hmacKey&&o.k(a(\"hmacKey\",u.hmacKey,o.C)),o}return v(r,n),r}(h);function B(n){return\"SHA-224\"==n?o.slice():u.slice()}function L(n,r){var t,e,o,u,f,w,s,a,h,c,v,A,b=[];for(t=r[0],e=r[1],o=r[2],u=r[3],f=r[4],w=r[5],s=r[6],a=r[7],v=0;v<64;v+=1)b[v]=v<16?n[v]:m(E(A=b[v-2],17)^E(A,19)^l(A,10),b[v-7],R(b[v-15]),b[v-16]),h=y(a,U(f),H(f,w,s),i[v],b[v]),c=p(S(t),d(t,e,o)),a=s,s=w,w=f,f=p(u,h),u=o,o=e,e=t,t=p(h,c);return r[0]=p(t,r[0]),r[1]=p(e,r[1]),r[2]=p(o,r[2]),r[3]=p(u,r[3]),r[4]=p(f,r[4]),r[5]=p(w,r[5]),r[6]=p(s,r[6]),r[7]=p(a,r[7]),r}var g=function(n){function r(r,e,i){var o=this;if(\"SHA-224\"!==r&&\"SHA-256\"!==r)throw new Error(f);var u=i||{};return(o=n.call(this,r,e,i)||this).F=o.Y,o.g=!0,o.C=-1,o.p=t(o.t,o.i,o.C),o.R=L,o.B=function(n){return n.slice()},o.L=B,o.K=function(n,t,e,i){return function(n,r,t,e,i){for(var o,u=15+(r+65>>>9<<4),f=r+t;n.length<=u;)n.push(0);for(n[r>>>5]|=128<<24-r%32,n[u]=4294967295&f,n[u-1]=f/4294967296|0,o=0;o<n.length;o+=16)e=L(n.slice(o,o+16),e);return\"SHA-224\"===i?[e[0],e[1],e[2],e[3],e[4],e[5],e[6]]:e}(n,t,e,i,r)},o.m=B(r),o.S=512,o.U=\"SHA-224\"===r?224:256,o.T=!1,u.hmacKey&&o.k(a(\"hmacKey\",u.hmacKey,o.C)),o}return v(r,n),r}(h),k=function(n,r){this.N=n,this.I=r};function Y(n,r){var t;return r>32?(t=64-r,new k(n.I<<r|n.N>>>t,n.N<<r|n.I>>>t)):0!==r?(t=32-r,new k(n.N<<r|n.I>>>t,n.I<<r|n.N>>>t)):n}function N(n,r){var t;return r<32?(t=32-r,new k(n.N>>>r|n.I<<t,n.I>>>r|n.N<<t)):(t=64-r,new k(n.I>>>r|n.N<<t,n.N>>>r|n.I<<t))}function I(n,r){return new k(n.N>>>r,n.I>>>r|n.N<<32-r)}function M(n,r,t){return new k(n.N&r.N^~n.N&t.N,n.I&r.I^~n.I&t.I)}function X(n,r,t){return new k(n.N&r.N^n.N&t.N^r.N&t.N,n.I&r.I^n.I&t.I^r.I&t.I)}function z(n){var r=N(n,28),t=N(n,34),e=N(n,39);return new k(r.N^t.N^e.N,r.I^t.I^e.I)}function O(n,r){var t,e;t=(65535&n.I)+(65535&r.I);var i=(65535&(e=(n.I>>>16)+(r.I>>>16)+(t>>>16)))<<16|65535&t;return t=(65535&n.N)+(65535&r.N)+(e>>>16),e=(n.N>>>16)+(r.N>>>16)+(t>>>16),new k((65535&e)<<16|65535&t,i)}function j(n,r,t,e){var i,o;i=(65535&n.I)+(65535&r.I)+(65535&t.I)+(65535&e.I);var u=(65535&(o=(n.I>>>16)+(r.I>>>16)+(t.I>>>16)+(e.I>>>16)+(i>>>16)))<<16|65535&i;return i=(65535&n.N)+(65535&r.N)+(65535&t.N)+(65535&e.N)+(o>>>16),o=(n.N>>>16)+(r.N>>>16)+(t.N>>>16)+(e.N>>>16)+(i>>>16),new k((65535&o)<<16|65535&i,u)}function _(n,r,t,e,i){var o,u;o=(65535&n.I)+(65535&r.I)+(65535&t.I)+(65535&e.I)+(65535&i.I);var f=(65535&(u=(n.I>>>16)+(r.I>>>16)+(t.I>>>16)+(e.I>>>16)+(i.I>>>16)+(o>>>16)))<<16|65535&o;return o=(65535&n.N)+(65535&r.N)+(65535&t.N)+(65535&e.N)+(65535&i.N)+(u>>>16),u=(n.N>>>16)+(r.N>>>16)+(t.N>>>16)+(e.N>>>16)+(i.N>>>16)+(o>>>16),new k((65535&u)<<16|65535&o,f)}function P(n,r){return new k(n.N^r.N,n.I^r.I)}function x(n){var r=N(n,1),t=N(n,8),e=I(n,7);return new k(r.N^t.N^e.N,r.I^t.I^e.I)}function V(n){var r=N(n,14),t=N(n,18),e=N(n,41);return new k(r.N^t.N^e.N,r.I^t.I^e.I)}var Z=[new k(i[0],3609767458),new k(i[1],602891725),new k(i[2],3964484399),new k(i[3],2173295548),new k(i[4],4081628472),new k(i[5],3053834265),new k(i[6],2937671579),new k(i[7],3664609560),new k(i[8],2734883394),new k(i[9],1164996542),new k(i[10],1323610764),new k(i[11],3590304994),new k(i[12],4068182383),new k(i[13],991336113),new k(i[14],633803317),new k(i[15],3479774868),new k(i[16],2666613458),new k(i[17],944711139),new k(i[18],2341262773),new k(i[19],2007800933),new k(i[20],1495990901),new k(i[21],1856431235),new k(i[22],3175218132),new k(i[23],2198950837),new k(i[24],3999719339),new k(i[25],766784016),new k(i[26],2566594879),new k(i[27],3203337956),new k(i[28],1034457026),new k(i[29],2466948901),new k(i[30],3758326383),new k(i[31],168717936),new k(i[32],1188179964),new k(i[33],1546045734),new k(i[34],1522805485),new k(i[35],2643833823),new k(i[36],2343527390),new k(i[37],1014477480),new k(i[38],1206759142),new k(i[39],344077627),new k(i[40],1290863460),new k(i[41],3158454273),new k(i[42],3505952657),new k(i[43],106217008),new k(i[44],3606008344),new k(i[45],1432725776),new k(i[46],1467031594),new k(i[47],851169720),new k(i[48],3100823752),new k(i[49],1363258195),new k(i[50],3750685593),new k(i[51],3785050280),new k(i[52],3318307427),new k(i[53],3812723403),new k(i[54],2003034995),new k(i[55],3602036899),new k(i[56],1575990012),new k(i[57],1125592928),new k(i[58],2716904306),new k(i[59],442776044),new k(i[60],593698344),new k(i[61],3733110249),new k(i[62],2999351573),new k(i[63],3815920427),new k(3391569614,3928383900),new k(3515267271,566280711),new k(3940187606,3454069534),new k(4118630271,4000239992),new k(116418474,1914138554),new k(174292421,2731055270),new k(289380356,3203993006),new k(460393269,320620315),new k(685471733,587496836),new k(852142971,1086792851),new k(1017036298,365543100),new k(1126000580,2618297676),new k(1288033470,3409855158),new k(1501505948,4234509866),new k(1607167915,987167468),new k(1816402316,1246189591)];function q(n){return\"SHA-384\"===n?[new k(3418070365,o[0]),new k(1654270250,o[1]),new k(2438529370,o[2]),new k(355462360,o[3]),new k(1731405415,o[4]),new k(41048885895,o[5]),new k(3675008525,o[6]),new k(1203062813,o[7])]:[new k(u[0],4089235720),new k(u[1],2227873595),new k(u[2],4271175723),new k(u[3],1595750129),new k(u[4],2917565137),new k(u[5],725511199),new k(u[6],4215389547),new k(u[7],327033209)]}function D(n,r){var t,e,i,o,u,f,w,s,a,h,c,v,A,E,l,b,H=[];for(t=r[0],e=r[1],i=r[2],o=r[3],u=r[4],f=r[5],w=r[6],s=r[7],c=0;c<80;c+=1)c<16?(v=2*c,H[c]=new k(n[v],n[v+1])):H[c]=j((A=H[c-2],E=void 0,l=void 0,b=void 0,E=N(A,19),l=N(A,61),b=I(A,6),new k(E.N^l.N^b.N,E.I^l.I^b.I)),H[c-7],x(H[c-15]),H[c-16]),a=_(s,V(u),M(u,f,w),Z[c],H[c]),h=O(z(t),X(t,e,i)),s=w,w=f,f=u,u=O(o,a),o=i,i=e,e=t,t=O(a,h);return r[0]=O(t,r[0]),r[1]=O(e,r[1]),r[2]=O(i,r[2]),r[3]=O(o,r[3]),r[4]=O(u,r[4]),r[5]=O(f,r[5]),r[6]=O(w,r[6]),r[7]=O(s,r[7]),r}var G=function(n){function r(r,e,i){var o=this;if(\"SHA-384\"!==r&&\"SHA-512\"!==r)throw new Error(f);var u=i||{};return(o=n.call(this,r,e,i)||this).F=o.Y,o.g=!0,o.C=-1,o.p=t(o.t,o.i,o.C),o.R=D,o.B=function(n){return n.slice()},o.L=q,o.K=function(n,t,e,i){return function(n,r,t,e,i){for(var o,u=31+(r+129>>>10<<5),f=r+t;n.length<=u;)n.push(0);for(n[r>>>5]|=128<<24-r%32,n[u]=4294967295&f,n[u-1]=f/4294967296|0,o=0;o<n.length;o+=32)e=D(n.slice(o,o+32),e);return\"SHA-384\"===i?[(e=e)[0].N,e[0].I,e[1].N,e[1].I,e[2].N,e[2].I,e[3].N,e[3].I,e[4].N,e[4].I,e[5].N,e[5].I]:[e[0].N,e[0].I,e[1].N,e[1].I,e[2].N,e[2].I,e[3].N,e[3].I,e[4].N,e[4].I,e[5].N,e[5].I,e[6].N,e[6].I,e[7].N,e[7].I]}(n,t,e,i,r)},o.m=q(r),o.S=1024,o.U=\"SHA-384\"===r?384:512,o.T=!1,u.hmacKey&&o.k(a(\"hmacKey\",u.hmacKey,o.C)),o}return v(r,n),r}(h),J=[new k(0,1),new k(0,32898),new k(2147483648,32906),new k(2147483648,2147516416),new k(0,32907),new k(0,2147483649),new k(2147483648,2147516545),new k(2147483648,32777),new k(0,138),new k(0,136),new k(0,2147516425),new k(0,2147483658),new k(0,2147516555),new k(2147483648,139),new k(2147483648,32905),new k(2147483648,32771),new k(2147483648,32770),new k(2147483648,128),new k(0,32778),new k(2147483648,2147483658),new k(2147483648,2147516545),new k(2147483648,32896),new k(0,2147483649),new k(2147483648,2147516424)],Q=[[0,36,3,41,18],[1,44,10,45,2],[62,6,43,15,61],[28,55,25,21,56],[27,20,39,8,14]];function W(n){var r,t=[];for(r=0;r<5;r+=1)t[r]=[new k(0,0),new k(0,0),new k(0,0),new k(0,0),new k(0,0)];return t}function $(n){var r,t=[];for(r=0;r<5;r+=1)t[r]=n[r].slice();return t}function nn(n,r){var t,e,i,o,u,f,w,s,a,h=[],c=[];if(null!==n)for(e=0;e<n.length;e+=2)r[(e>>>1)%5][(e>>>1)/5|0]=P(r[(e>>>1)%5][(e>>>1)/5|0],new k(n[e+1],n[e]));for(t=0;t<24;t+=1){for(o=W(),e=0;e<5;e+=1)h[e]=(u=r[e][0],f=r[e][1],w=r[e][2],s=r[e][3],a=r[e][4],new k(u.N^f.N^w.N^s.N^a.N,u.I^f.I^w.I^s.I^a.I));for(e=0;e<5;e+=1)c[e]=P(h[(e+4)%5],Y(h[(e+1)%5],1));for(e=0;e<5;e+=1)for(i=0;i<5;i+=1)r[e][i]=P(r[e][i],c[e]);for(e=0;e<5;e+=1)for(i=0;i<5;i+=1)o[i][(2*e+3*i)%5]=Y(r[e][i],Q[e][i]);for(e=0;e<5;e+=1)for(i=0;i<5;i+=1)r[e][i]=P(o[e][i],new k(~o[(e+1)%5][i].N&o[(e+2)%5][i].N,~o[(e+1)%5][i].I&o[(e+2)%5][i].I));r[0][0]=P(r[0][0],J[t])}return r}function rn(n){var r,t,e=0,i=[0,0],o=[4294967295&n,n/4294967296&2097151];for(r=6;r>=0;r--)0===(t=o[r>>2]>>>8*r&255)&&0===e||(i[e+1>>2]|=t<<8*(e+1),e+=1);return e=0!==e?e:1,i[0]|=e,{value:e+1>4?i:[i[0]],binLen:8+8*e}}function tn(n){return w(rn(n.binLen),n)}function en(n,r){var t,e=rn(r),i=r>>>2,o=(i-(e=w(e,n)).value.length%i)%i;for(t=0;t<o;t++)e.value.push(0);return e.value}var on=function(n){function r(r,e,i){var o=this,u=6,w=0,s=i||{};if(1!==(o=n.call(this,r,e,i)||this).numRounds){if(s.kmacKey||s.hmacKey)throw new Error(\"Cannot set numRounds with MAC\");if(\"CSHAKE128\"===o.o||\"CSHAKE256\"===o.o)throw new Error(\"Cannot set numRounds for CSHAKE variants\")}switch(o.C=1,o.p=t(o.t,o.i,o.C),o.R=nn,o.B=$,o.L=W,o.m=W(),o.T=!1,r){case\"SHA3-224\":o.S=w=1152,o.U=224,o.g=!0,o.F=o.Y;break;case\"SHA3-256\":o.S=w=1088,o.U=256,o.g=!0,o.F=o.Y;break;case\"SHA3-384\":o.S=w=832,o.U=384,o.g=!0,o.F=o.Y;break;case\"SHA3-512\":o.S=w=576,o.U=512,o.g=!0,o.F=o.Y;break;case\"SHAKE128\":u=31,o.S=w=1344,o.U=-1,o.T=!0,o.g=!1,o.F=null;break;case\"SHAKE256\":u=31,o.S=w=1088,o.U=-1,o.T=!0,o.g=!1,o.F=null;break;case\"KMAC128\":u=4,o.S=w=1344,o.M(i),o.U=-1,o.T=!0,o.g=!1,o.F=o.X;break;case\"KMAC256\":u=4,o.S=w=1088,o.M(i),o.U=-1,o.T=!0,o.g=!1,o.F=o.X;break;case\"CSHAKE128\":o.S=w=1344,u=o.O(i),o.U=-1,o.T=!0,o.g=!1,o.F=null;break;case\"CSHAKE256\":o.S=w=1088,u=o.O(i),o.U=-1,o.T=!0,o.g=!1,o.F=null;break;default:throw new Error(f)}return o.K=function(n,r,t,e,i){return function(n,r,t,e,i,o,u){var f,w,s=0,a=[],h=i>>>5,c=r>>>5;for(f=0;f<c&&r>=i;f+=h)e=nn(n.slice(f,f+h),e),r-=i;for(n=n.slice(f),r%=i;n.length<h;)n.push(0);for(n[(f=r>>>3)>>2]^=o<<f%4*8,n[h-1]^=2147483648,e=nn(n,e);32*a.length<u&&(w=e[s%5][s/5|0],a.push(w.I),!(32*a.length>=u));)a.push(w.N),0==64*(s+=1)%i&&(nn(null,e),s=0);return a}(n,r,0,e,w,u,i)},s.hmacKey&&o.k(a(\"hmacKey\",s.hmacKey,o.C)),o}return v(r,n),r.prototype.O=function(n,r){var t=function(n){var r=n||{};return{funcName:a(\"funcName\",r.funcName,1,{value:[],binLen:0}),customization:a(\"Customization\",r.customization,1,{value:[],binLen:0})}}(n||{});r&&(t.funcName=r);var e=w(tn(t.funcName),tn(t.customization));if(0!==t.customization.binLen||0!==t.funcName.binLen){for(var i=en(e,this.S>>>3),o=0;o<i.length;o+=this.S>>>5)this.m=this.R(i.slice(o,o+(this.S>>>5)),this.m),this.v+=this.S;return 4}return 31},r.prototype.M=function(n){var r=function(n){var r=n||{};return{kmacKey:a(\"kmacKey\",r.kmacKey,1),funcName:{value:[1128353099],binLen:32},customization:a(\"Customization\",r.customization,1,{value:[],binLen:0})}}(n||{});this.O(n,r.funcName);for(var t=en(tn(r.kmacKey),this.S>>>3),e=0;e<t.length;e+=this.S>>>5)this.m=this.R(t.slice(e,e+(this.S>>>5)),this.m),this.v+=this.S;this.A=!0},r.prototype.X=function(n){var r=w({value:this.u.slice(),binLen:this.s},function(n){var r,t,e=0,i=[0,0],o=[4294967295&n,n/4294967296&2097151];for(r=6;r>=0;r--)0==(t=o[r>>2]>>>8*r&255)&&0===e||(i[e>>2]|=t<<8*e,e+=1);return i[(e=0!==e?e:1)>>2]|=e<<8*e,{value:e+1>4?i:[i[0]],binLen:8+8*e}}(n.outputLen));return this.K(r.value,r.binLen,this.v,this.B(this.m),n.outputLen)},r}(h);return function(){function n(n,r,t){if(\"SHA-1\"==n)this.j=new K(n,r,t);else if(\"SHA-224\"==n||\"SHA-256\"==n)this.j=new g(n,r,t);else if(\"SHA-384\"==n||\"SHA-512\"==n)this.j=new G(n,r,t);else{if(\"SHA3-224\"!=n&&\"SHA3-256\"!=n&&\"SHA3-384\"!=n&&\"SHA3-512\"!=n&&\"SHAKE128\"!=n&&\"SHAKE256\"!=n&&\"CSHAKE128\"!=n&&\"CSHAKE256\"!=n&&\"KMAC128\"!=n&&\"KMAC256\"!=n)throw new Error(f);this.j=new on(n,r,t)}}return n.prototype.update=function(n){this.j.update(n)},n.prototype.getHash=function(n,r){return this.j.getHash(n,r)},n.prototype.setHMACKey=function(n,r,t){this.j.setHMACKey(n,r,t)},n.prototype.getHMAC=function(n,r){return this.j.getHMAC(n,r)},n}()}));\n//# sourceMappingURL=sha.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/jssha/dist/sha.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/jssha/dist/sha.js":
/*!****************************************!*\
  !*** ./node_modules/jssha/dist/sha.js ***!
  \****************************************/
/***/ (function(module) {

eval("/**\n * A JavaScript implementation of the SHA family of hashes - defined in FIPS PUB 180-4, FIPS PUB 202,\n * and SP 800-185 - as well as the corresponding HMAC implementation as defined in FIPS PUB 198-1.\n *\n * Copyright 2008-2020 Brian Turek, 1998-2009 Paul Johnston & Contributors\n * Distributed under the BSD License\n * See http://caligatio.github.com/jsSHA/ for more information\n *\n * Two ECMAScript polyfill functions carry the following license:\n *\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Licensed under the Apache License, Version 2.0 (the \"License\"); you may not use this file except in compliance with\n * the License. You may obtain a copy of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * THIS CODE IS PROVIDED ON AN *AS IS* BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, EITHER EXPRESS OR IMPLIED,\n * INCLUDING WITHOUT LIMITATION ANY IMPLIED WARRANTIES OR CONDITIONS OF TITLE, FITNESS FOR A PARTICULAR PURPOSE,\n * MERCHANTABLITY OR NON-INFRINGEMENT.\n *\n * See the Apache Version 2.0 License for specific language governing permissions and limitations under the License.\n */\n!function(n,r){ true?module.exports=r():0}(this,(function(){\"use strict\";var n=\"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/\";function r(n,r,t,e){var i,o,u,f=r||[0],w=(t=t||0)>>>3,s=-1===e?3:0;for(i=0;i<n.length;i+=1)o=(u=i+w)>>>2,f.length<=o&&f.push(0),f[o]|=n[i]<<8*(s+e*(u%4));return{value:f,binLen:8*n.length+t}}function t(t,e,i){switch(e){case\"UTF8\":case\"UTF16BE\":case\"UTF16LE\":break;default:throw new Error(\"encoding must be UTF8, UTF16BE, or UTF16LE\")}switch(t){case\"HEX\":return function(n,r,t){return function(n,r,t,e){var i,o,u,f;if(0!=n.length%2)throw new Error(\"String of HEX type must be in byte increments\");var w=r||[0],s=(t=t||0)>>>3,a=-1===e?3:0;for(i=0;i<n.length;i+=2){if(o=parseInt(n.substr(i,2),16),isNaN(o))throw new Error(\"String of HEX type contains invalid characters\");for(u=(f=(i>>>1)+s)>>>2;w.length<=u;)w.push(0);w[u]|=o<<8*(a+e*(f%4))}return{value:w,binLen:4*n.length+t}}(n,r,t,i)};case\"TEXT\":return function(n,r,t){return function(n,r,t,e,i){var o,u,f,w,s,a,h,c,v=0,A=t||[0],E=(e=e||0)>>>3;if(\"UTF8\"===r)for(h=-1===i?3:0,f=0;f<n.length;f+=1)for(u=[],128>(o=n.charCodeAt(f))?u.push(o):2048>o?(u.push(192|o>>>6),u.push(128|63&o)):55296>o||57344<=o?u.push(224|o>>>12,128|o>>>6&63,128|63&o):(f+=1,o=65536+((1023&o)<<10|1023&n.charCodeAt(f)),u.push(240|o>>>18,128|o>>>12&63,128|o>>>6&63,128|63&o)),w=0;w<u.length;w+=1){for(s=(a=v+E)>>>2;A.length<=s;)A.push(0);A[s]|=u[w]<<8*(h+i*(a%4)),v+=1}else for(h=-1===i?2:0,c=\"UTF16LE\"===r&&1!==i||\"UTF16LE\"!==r&&1===i,f=0;f<n.length;f+=1){for(o=n.charCodeAt(f),!0===c&&(o=(w=255&o)<<8|o>>>8),s=(a=v+E)>>>2;A.length<=s;)A.push(0);A[s]|=o<<8*(h+i*(a%4)),v+=2}return{value:A,binLen:8*v+e}}(n,e,r,t,i)};case\"B64\":return function(r,t,e){return function(r,t,e,i){var o,u,f,w,s,a,h=0,c=t||[0],v=(e=e||0)>>>3,A=-1===i?3:0,E=r.indexOf(\"=\");if(-1===r.search(/^[a-zA-Z0-9=+/]+$/))throw new Error(\"Invalid character in base-64 string\");if(r=r.replace(/=/g,\"\"),-1!==E&&E<r.length)throw new Error(\"Invalid '=' found in base-64 string\");for(o=0;o<r.length;o+=4){for(w=r.substr(o,4),f=0,u=0;u<w.length;u+=1)f|=n.indexOf(w.charAt(u))<<18-6*u;for(u=0;u<w.length-1;u+=1){for(s=(a=h+v)>>>2;c.length<=s;)c.push(0);c[s]|=(f>>>16-8*u&255)<<8*(A+i*(a%4)),h+=1}}return{value:c,binLen:8*h+e}}(r,t,e,i)};case\"BYTES\":return function(n,r,t){return function(n,r,t,e){var i,o,u,f,w=r||[0],s=(t=t||0)>>>3,a=-1===e?3:0;for(o=0;o<n.length;o+=1)i=n.charCodeAt(o),u=(f=o+s)>>>2,w.length<=u&&w.push(0),w[u]|=i<<8*(a+e*(f%4));return{value:w,binLen:8*n.length+t}}(n,r,t,i)};case\"ARRAYBUFFER\":try{new ArrayBuffer(0)}catch(n){throw new Error(\"ARRAYBUFFER not supported by this environment\")}return function(n,t,e){return function(n,t,e,i){return r(new Uint8Array(n),t,e,i)}(n,t,e,i)};case\"UINT8ARRAY\":try{new Uint8Array(0)}catch(n){throw new Error(\"UINT8ARRAY not supported by this environment\")}return function(n,t,e){return r(n,t,e,i)};default:throw new Error(\"format must be HEX, TEXT, B64, BYTES, ARRAYBUFFER, or UINT8ARRAY\")}}function e(r,t,e,i){switch(r){case\"HEX\":return function(n){return function(n,r,t,e){var i,o,u=\"\",f=r/8,w=-1===t?3:0;for(i=0;i<f;i+=1)o=n[i>>>2]>>>8*(w+t*(i%4)),u+=\"0123456789abcdef\".charAt(o>>>4&15)+\"0123456789abcdef\".charAt(15&o);return e.outputUpper?u.toUpperCase():u}(n,t,e,i)};case\"B64\":return function(r){return function(r,t,e,i){var o,u,f,w,s,a=\"\",h=t/8,c=-1===e?3:0;for(o=0;o<h;o+=3)for(w=o+1<h?r[o+1>>>2]:0,s=o+2<h?r[o+2>>>2]:0,f=(r[o>>>2]>>>8*(c+e*(o%4))&255)<<16|(w>>>8*(c+e*((o+1)%4))&255)<<8|s>>>8*(c+e*((o+2)%4))&255,u=0;u<4;u+=1)a+=8*o+6*u<=t?n.charAt(f>>>6*(3-u)&63):i.b64Pad;return a}(r,t,e,i)};case\"BYTES\":return function(n){return function(n,r,t){var e,i,o=\"\",u=r/8,f=-1===t?3:0;for(e=0;e<u;e+=1)i=n[e>>>2]>>>8*(f+t*(e%4))&255,o+=String.fromCharCode(i);return o}(n,t,e)};case\"ARRAYBUFFER\":try{new ArrayBuffer(0)}catch(n){throw new Error(\"ARRAYBUFFER not supported by this environment\")}return function(n){return function(n,r,t){var e,i=r/8,o=new ArrayBuffer(i),u=new Uint8Array(o),f=-1===t?3:0;for(e=0;e<i;e+=1)u[e]=n[e>>>2]>>>8*(f+t*(e%4))&255;return o}(n,t,e)};case\"UINT8ARRAY\":try{new Uint8Array(0)}catch(n){throw new Error(\"UINT8ARRAY not supported by this environment\")}return function(n){return function(n,r,t){var e,i=r/8,o=-1===t?3:0,u=new Uint8Array(i);for(e=0;e<i;e+=1)u[e]=n[e>>>2]>>>8*(o+t*(e%4))&255;return u}(n,t,e)};default:throw new Error(\"format must be HEX, B64, BYTES, ARRAYBUFFER, or UINT8ARRAY\")}}var i=[1116352408,1899447441,3049323471,3921009573,961987163,1508970993,2453635748,2870763221,3624381080,310598401,607225278,1426881987,1925078388,2162078206,2614888103,3248222580,3835390401,4022224774,264347078,604807628,770255983,1249150122,1555081692,1996064986,2554220882,2821834349,2952996808,3210313671,3336571891,3584528711,113926993,338241895,666307205,773529912,1294757372,1396182291,1695183700,1986661051,2177026350,2456956037,2730485921,2820302411,3259730800,3345764771,3516065817,3600352804,4094571909,275423344,430227734,506948616,659060556,883997877,958139571,1322822218,1537002063,1747873779,1955562222,2024104815,2227730452,2361852424,2428436474,2756734187,3204031479,3329325298],o=[3238371032,914150663,812702999,4144912697,4290775857,1750603025,1694076839,3204075428],u=[1779033703,3144134277,1013904242,2773480762,1359893119,2600822924,528734635,1541459225],f=\"Chosen SHA variant is not supported\";function w(n,r){var t,e,i=n.binLen>>>3,o=r.binLen>>>3,u=i<<3,f=4-i<<3;if(i%4!=0){for(t=0;t<o;t+=4)e=i+t>>>2,n.value[e]|=r.value[t>>>2]<<u,n.value.push(0),n.value[e+1]|=r.value[t>>>2]>>>f;return(n.value.length<<2)-4>=o+i&&n.value.pop(),{value:n.value,binLen:n.binLen+r.binLen}}return{value:n.value.concat(r.value),binLen:n.binLen+r.binLen}}function s(n){var r={outputUpper:!1,b64Pad:\"=\",outputLen:-1},t=n||{},e=\"Output length must be a multiple of 8\";if(r.outputUpper=t.outputUpper||!1,t.b64Pad&&(r.b64Pad=t.b64Pad),t.outputLen){if(t.outputLen%8!=0)throw new Error(e);r.outputLen=t.outputLen}else if(t.shakeLen){if(t.shakeLen%8!=0)throw new Error(e);r.outputLen=t.shakeLen}if(\"boolean\"!=typeof r.outputUpper)throw new Error(\"Invalid outputUpper formatting option\");if(\"string\"!=typeof r.b64Pad)throw new Error(\"Invalid b64Pad formatting option\");return r}function a(n,r,e,i){var o=n+\" must include a value and format\";if(!r){if(!i)throw new Error(o);return i}if(void 0===r.value||!r.format)throw new Error(o);return t(r.format,r.encoding||\"UTF8\",e)(r.value)}var h=function(){function n(n,r,t){var e=t||{};if(this.t=r,this.i=e.encoding||\"UTF8\",this.numRounds=e.numRounds||1,isNaN(this.numRounds)||this.numRounds!==parseInt(this.numRounds,10)||1>this.numRounds)throw new Error(\"numRounds must a integer >= 1\");this.o=n,this.u=[],this.s=0,this.h=!1,this.v=0,this.A=!1,this.l=[],this.H=[]}return n.prototype.update=function(n){var r,t=0,e=this.S>>>5,i=this.p(n,this.u,this.s),o=i.binLen,u=i.value,f=o>>>5;for(r=0;r<f;r+=e)t+this.S<=o&&(this.m=this.R(u.slice(r,r+e),this.m),t+=this.S);this.v+=t,this.u=u.slice(t>>>5),this.s=o%this.S,this.h=!0},n.prototype.getHash=function(n,r){var t,i,o=this.U,u=s(r);if(this.T){if(-1===u.outputLen)throw new Error(\"Output length must be specified in options\");o=u.outputLen}var f=e(n,o,this.C,u);if(this.A&&this.F)return f(this.F(u));for(i=this.K(this.u.slice(),this.s,this.v,this.B(this.m),o),t=1;t<this.numRounds;t+=1)this.T&&o%32!=0&&(i[i.length-1]&=16777215>>>24-o%32),i=this.K(i,o,0,this.L(this.o),o);return f(i)},n.prototype.setHMACKey=function(n,r,e){if(!this.g)throw new Error(\"Variant does not support HMAC\");if(this.h)throw new Error(\"Cannot set MAC key after calling update\");var i=t(r,(e||{}).encoding||\"UTF8\",this.C);this.k(i(n))},n.prototype.k=function(n){var r,t=this.S>>>3,e=t/4-1;if(1!==this.numRounds)throw new Error(\"Cannot set numRounds with MAC\");if(this.A)throw new Error(\"MAC key already set\");for(t<n.binLen/8&&(n.value=this.K(n.value,n.binLen,0,this.L(this.o),this.U));n.value.length<=e;)n.value.push(0);for(r=0;r<=e;r+=1)this.l[r]=909522486^n.value[r],this.H[r]=1549556828^n.value[r];this.m=this.R(this.l,this.m),this.v=this.S,this.A=!0},n.prototype.getHMAC=function(n,r){var t=s(r);return e(n,this.U,this.C,t)(this.Y())},n.prototype.Y=function(){var n;if(!this.A)throw new Error(\"Cannot call getHMAC without first setting MAC key\");var r=this.K(this.u.slice(),this.s,this.v,this.B(this.m),this.U);return n=this.R(this.H,this.L(this.o)),n=this.K(r,this.U,this.S,n,this.U)},n}(),c=function(n,r){return(c=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,r){n.__proto__=r}||function(n,r){for(var t in r)Object.prototype.hasOwnProperty.call(r,t)&&(n[t]=r[t])})(n,r)};function v(n,r){function t(){this.constructor=n}c(n,r),n.prototype=null===r?Object.create(r):(t.prototype=r.prototype,new t)}function A(n,r){return n<<r|n>>>32-r}function E(n,r){return n>>>r|n<<32-r}function l(n,r){return n>>>r}function b(n,r,t){return n^r^t}function H(n,r,t){return n&r^~n&t}function d(n,r,t){return n&r^n&t^r&t}function S(n){return E(n,2)^E(n,13)^E(n,22)}function p(n,r){var t=(65535&n)+(65535&r);return(65535&(n>>>16)+(r>>>16)+(t>>>16))<<16|65535&t}function m(n,r,t,e){var i=(65535&n)+(65535&r)+(65535&t)+(65535&e);return(65535&(n>>>16)+(r>>>16)+(t>>>16)+(e>>>16)+(i>>>16))<<16|65535&i}function y(n,r,t,e,i){var o=(65535&n)+(65535&r)+(65535&t)+(65535&e)+(65535&i);return(65535&(n>>>16)+(r>>>16)+(t>>>16)+(e>>>16)+(i>>>16)+(o>>>16))<<16|65535&o}function R(n){return E(n,7)^E(n,18)^l(n,3)}function U(n){return E(n,6)^E(n,11)^E(n,25)}function T(n){return[1732584193,4023233417,2562383102,271733878,3285377520]}function C(n,r){var t,e,i,o,u,f,w,s=[];for(t=r[0],e=r[1],i=r[2],o=r[3],u=r[4],w=0;w<80;w+=1)s[w]=w<16?n[w]:A(s[w-3]^s[w-8]^s[w-14]^s[w-16],1),f=w<20?y(A(t,5),H(e,i,o),u,1518500249,s[w]):w<40?y(A(t,5),b(e,i,o),u,1859775393,s[w]):w<60?y(A(t,5),d(e,i,o),u,2400959708,s[w]):y(A(t,5),b(e,i,o),u,3395469782,s[w]),u=o,o=i,i=A(e,30),e=t,t=f;return r[0]=p(t,r[0]),r[1]=p(e,r[1]),r[2]=p(i,r[2]),r[3]=p(o,r[3]),r[4]=p(u,r[4]),r}function F(n,r,t,e){for(var i,o=15+(r+65>>>9<<4),u=r+t;n.length<=o;)n.push(0);for(n[r>>>5]|=128<<24-r%32,n[o]=4294967295&u,n[o-1]=u/4294967296|0,i=0;i<n.length;i+=16)e=C(n.slice(i,i+16),e);return e}var K=function(n){function r(r,e,i){var o=this;if(\"SHA-1\"!==r)throw new Error(f);var u=i||{};return(o=n.call(this,r,e,i)||this).g=!0,o.F=o.Y,o.C=-1,o.p=t(o.t,o.i,o.C),o.R=C,o.B=function(n){return n.slice()},o.L=T,o.K=F,o.m=[1732584193,4023233417,2562383102,271733878,3285377520],o.S=512,o.U=160,o.T=!1,u.hmacKey&&o.k(a(\"hmacKey\",u.hmacKey,o.C)),o}return v(r,n),r}(h);function B(n){return\"SHA-224\"==n?o.slice():u.slice()}function L(n,r){var t,e,o,u,f,w,s,a,h,c,v,A,b=[];for(t=r[0],e=r[1],o=r[2],u=r[3],f=r[4],w=r[5],s=r[6],a=r[7],v=0;v<64;v+=1)b[v]=v<16?n[v]:m(E(A=b[v-2],17)^E(A,19)^l(A,10),b[v-7],R(b[v-15]),b[v-16]),h=y(a,U(f),H(f,w,s),i[v],b[v]),c=p(S(t),d(t,e,o)),a=s,s=w,w=f,f=p(u,h),u=o,o=e,e=t,t=p(h,c);return r[0]=p(t,r[0]),r[1]=p(e,r[1]),r[2]=p(o,r[2]),r[3]=p(u,r[3]),r[4]=p(f,r[4]),r[5]=p(w,r[5]),r[6]=p(s,r[6]),r[7]=p(a,r[7]),r}var g=function(n){function r(r,e,i){var o=this;if(\"SHA-224\"!==r&&\"SHA-256\"!==r)throw new Error(f);var u=i||{};return(o=n.call(this,r,e,i)||this).F=o.Y,o.g=!0,o.C=-1,o.p=t(o.t,o.i,o.C),o.R=L,o.B=function(n){return n.slice()},o.L=B,o.K=function(n,t,e,i){return function(n,r,t,e,i){for(var o,u=15+(r+65>>>9<<4),f=r+t;n.length<=u;)n.push(0);for(n[r>>>5]|=128<<24-r%32,n[u]=4294967295&f,n[u-1]=f/4294967296|0,o=0;o<n.length;o+=16)e=L(n.slice(o,o+16),e);return\"SHA-224\"===i?[e[0],e[1],e[2],e[3],e[4],e[5],e[6]]:e}(n,t,e,i,r)},o.m=B(r),o.S=512,o.U=\"SHA-224\"===r?224:256,o.T=!1,u.hmacKey&&o.k(a(\"hmacKey\",u.hmacKey,o.C)),o}return v(r,n),r}(h),k=function(n,r){this.N=n,this.I=r};function Y(n,r){var t;return r>32?(t=64-r,new k(n.I<<r|n.N>>>t,n.N<<r|n.I>>>t)):0!==r?(t=32-r,new k(n.N<<r|n.I>>>t,n.I<<r|n.N>>>t)):n}function N(n,r){var t;return r<32?(t=32-r,new k(n.N>>>r|n.I<<t,n.I>>>r|n.N<<t)):(t=64-r,new k(n.I>>>r|n.N<<t,n.N>>>r|n.I<<t))}function I(n,r){return new k(n.N>>>r,n.I>>>r|n.N<<32-r)}function M(n,r,t){return new k(n.N&r.N^~n.N&t.N,n.I&r.I^~n.I&t.I)}function X(n,r,t){return new k(n.N&r.N^n.N&t.N^r.N&t.N,n.I&r.I^n.I&t.I^r.I&t.I)}function z(n){var r=N(n,28),t=N(n,34),e=N(n,39);return new k(r.N^t.N^e.N,r.I^t.I^e.I)}function O(n,r){var t,e;t=(65535&n.I)+(65535&r.I);var i=(65535&(e=(n.I>>>16)+(r.I>>>16)+(t>>>16)))<<16|65535&t;return t=(65535&n.N)+(65535&r.N)+(e>>>16),e=(n.N>>>16)+(r.N>>>16)+(t>>>16),new k((65535&e)<<16|65535&t,i)}function j(n,r,t,e){var i,o;i=(65535&n.I)+(65535&r.I)+(65535&t.I)+(65535&e.I);var u=(65535&(o=(n.I>>>16)+(r.I>>>16)+(t.I>>>16)+(e.I>>>16)+(i>>>16)))<<16|65535&i;return i=(65535&n.N)+(65535&r.N)+(65535&t.N)+(65535&e.N)+(o>>>16),o=(n.N>>>16)+(r.N>>>16)+(t.N>>>16)+(e.N>>>16)+(i>>>16),new k((65535&o)<<16|65535&i,u)}function _(n,r,t,e,i){var o,u;o=(65535&n.I)+(65535&r.I)+(65535&t.I)+(65535&e.I)+(65535&i.I);var f=(65535&(u=(n.I>>>16)+(r.I>>>16)+(t.I>>>16)+(e.I>>>16)+(i.I>>>16)+(o>>>16)))<<16|65535&o;return o=(65535&n.N)+(65535&r.N)+(65535&t.N)+(65535&e.N)+(65535&i.N)+(u>>>16),u=(n.N>>>16)+(r.N>>>16)+(t.N>>>16)+(e.N>>>16)+(i.N>>>16)+(o>>>16),new k((65535&u)<<16|65535&o,f)}function P(n,r){return new k(n.N^r.N,n.I^r.I)}function x(n){var r=N(n,1),t=N(n,8),e=I(n,7);return new k(r.N^t.N^e.N,r.I^t.I^e.I)}function V(n){var r=N(n,14),t=N(n,18),e=N(n,41);return new k(r.N^t.N^e.N,r.I^t.I^e.I)}var Z=[new k(i[0],3609767458),new k(i[1],602891725),new k(i[2],3964484399),new k(i[3],2173295548),new k(i[4],4081628472),new k(i[5],3053834265),new k(i[6],2937671579),new k(i[7],3664609560),new k(i[8],2734883394),new k(i[9],1164996542),new k(i[10],1323610764),new k(i[11],3590304994),new k(i[12],4068182383),new k(i[13],991336113),new k(i[14],633803317),new k(i[15],3479774868),new k(i[16],2666613458),new k(i[17],944711139),new k(i[18],2341262773),new k(i[19],2007800933),new k(i[20],1495990901),new k(i[21],1856431235),new k(i[22],3175218132),new k(i[23],2198950837),new k(i[24],3999719339),new k(i[25],766784016),new k(i[26],2566594879),new k(i[27],3203337956),new k(i[28],1034457026),new k(i[29],2466948901),new k(i[30],3758326383),new k(i[31],168717936),new k(i[32],1188179964),new k(i[33],1546045734),new k(i[34],1522805485),new k(i[35],2643833823),new k(i[36],2343527390),new k(i[37],1014477480),new k(i[38],1206759142),new k(i[39],344077627),new k(i[40],1290863460),new k(i[41],3158454273),new k(i[42],3505952657),new k(i[43],106217008),new k(i[44],3606008344),new k(i[45],1432725776),new k(i[46],1467031594),new k(i[47],851169720),new k(i[48],3100823752),new k(i[49],1363258195),new k(i[50],3750685593),new k(i[51],3785050280),new k(i[52],3318307427),new k(i[53],3812723403),new k(i[54],2003034995),new k(i[55],3602036899),new k(i[56],1575990012),new k(i[57],1125592928),new k(i[58],2716904306),new k(i[59],442776044),new k(i[60],593698344),new k(i[61],3733110249),new k(i[62],2999351573),new k(i[63],3815920427),new k(3391569614,3928383900),new k(3515267271,566280711),new k(3940187606,3454069534),new k(4118630271,4000239992),new k(116418474,1914138554),new k(174292421,2731055270),new k(289380356,3203993006),new k(460393269,320620315),new k(685471733,587496836),new k(852142971,1086792851),new k(1017036298,365543100),new k(1126000580,2618297676),new k(1288033470,3409855158),new k(1501505948,4234509866),new k(1607167915,987167468),new k(1816402316,1246189591)];function q(n){return\"SHA-384\"===n?[new k(3418070365,o[0]),new k(1654270250,o[1]),new k(2438529370,o[2]),new k(355462360,o[3]),new k(1731405415,o[4]),new k(41048885895,o[5]),new k(3675008525,o[6]),new k(1203062813,o[7])]:[new k(u[0],4089235720),new k(u[1],2227873595),new k(u[2],4271175723),new k(u[3],1595750129),new k(u[4],2917565137),new k(u[5],725511199),new k(u[6],4215389547),new k(u[7],327033209)]}function D(n,r){var t,e,i,o,u,f,w,s,a,h,c,v,A,E,l,b,H=[];for(t=r[0],e=r[1],i=r[2],o=r[3],u=r[4],f=r[5],w=r[6],s=r[7],c=0;c<80;c+=1)c<16?(v=2*c,H[c]=new k(n[v],n[v+1])):H[c]=j((A=H[c-2],E=void 0,l=void 0,b=void 0,E=N(A,19),l=N(A,61),b=I(A,6),new k(E.N^l.N^b.N,E.I^l.I^b.I)),H[c-7],x(H[c-15]),H[c-16]),a=_(s,V(u),M(u,f,w),Z[c],H[c]),h=O(z(t),X(t,e,i)),s=w,w=f,f=u,u=O(o,a),o=i,i=e,e=t,t=O(a,h);return r[0]=O(t,r[0]),r[1]=O(e,r[1]),r[2]=O(i,r[2]),r[3]=O(o,r[3]),r[4]=O(u,r[4]),r[5]=O(f,r[5]),r[6]=O(w,r[6]),r[7]=O(s,r[7]),r}var G=function(n){function r(r,e,i){var o=this;if(\"SHA-384\"!==r&&\"SHA-512\"!==r)throw new Error(f);var u=i||{};return(o=n.call(this,r,e,i)||this).F=o.Y,o.g=!0,o.C=-1,o.p=t(o.t,o.i,o.C),o.R=D,o.B=function(n){return n.slice()},o.L=q,o.K=function(n,t,e,i){return function(n,r,t,e,i){for(var o,u=31+(r+129>>>10<<5),f=r+t;n.length<=u;)n.push(0);for(n[r>>>5]|=128<<24-r%32,n[u]=4294967295&f,n[u-1]=f/4294967296|0,o=0;o<n.length;o+=32)e=D(n.slice(o,o+32),e);return\"SHA-384\"===i?[(e=e)[0].N,e[0].I,e[1].N,e[1].I,e[2].N,e[2].I,e[3].N,e[3].I,e[4].N,e[4].I,e[5].N,e[5].I]:[e[0].N,e[0].I,e[1].N,e[1].I,e[2].N,e[2].I,e[3].N,e[3].I,e[4].N,e[4].I,e[5].N,e[5].I,e[6].N,e[6].I,e[7].N,e[7].I]}(n,t,e,i,r)},o.m=q(r),o.S=1024,o.U=\"SHA-384\"===r?384:512,o.T=!1,u.hmacKey&&o.k(a(\"hmacKey\",u.hmacKey,o.C)),o}return v(r,n),r}(h),J=[new k(0,1),new k(0,32898),new k(2147483648,32906),new k(2147483648,2147516416),new k(0,32907),new k(0,2147483649),new k(2147483648,2147516545),new k(2147483648,32777),new k(0,138),new k(0,136),new k(0,2147516425),new k(0,2147483658),new k(0,2147516555),new k(2147483648,139),new k(2147483648,32905),new k(2147483648,32771),new k(2147483648,32770),new k(2147483648,128),new k(0,32778),new k(2147483648,2147483658),new k(2147483648,2147516545),new k(2147483648,32896),new k(0,2147483649),new k(2147483648,2147516424)],Q=[[0,36,3,41,18],[1,44,10,45,2],[62,6,43,15,61],[28,55,25,21,56],[27,20,39,8,14]];function W(n){var r,t=[];for(r=0;r<5;r+=1)t[r]=[new k(0,0),new k(0,0),new k(0,0),new k(0,0),new k(0,0)];return t}function $(n){var r,t=[];for(r=0;r<5;r+=1)t[r]=n[r].slice();return t}function nn(n,r){var t,e,i,o,u,f,w,s,a,h=[],c=[];if(null!==n)for(e=0;e<n.length;e+=2)r[(e>>>1)%5][(e>>>1)/5|0]=P(r[(e>>>1)%5][(e>>>1)/5|0],new k(n[e+1],n[e]));for(t=0;t<24;t+=1){for(o=W(),e=0;e<5;e+=1)h[e]=(u=r[e][0],f=r[e][1],w=r[e][2],s=r[e][3],a=r[e][4],new k(u.N^f.N^w.N^s.N^a.N,u.I^f.I^w.I^s.I^a.I));for(e=0;e<5;e+=1)c[e]=P(h[(e+4)%5],Y(h[(e+1)%5],1));for(e=0;e<5;e+=1)for(i=0;i<5;i+=1)r[e][i]=P(r[e][i],c[e]);for(e=0;e<5;e+=1)for(i=0;i<5;i+=1)o[i][(2*e+3*i)%5]=Y(r[e][i],Q[e][i]);for(e=0;e<5;e+=1)for(i=0;i<5;i+=1)r[e][i]=P(o[e][i],new k(~o[(e+1)%5][i].N&o[(e+2)%5][i].N,~o[(e+1)%5][i].I&o[(e+2)%5][i].I));r[0][0]=P(r[0][0],J[t])}return r}function rn(n){var r,t,e=0,i=[0,0],o=[4294967295&n,n/4294967296&2097151];for(r=6;r>=0;r--)0===(t=o[r>>2]>>>8*r&255)&&0===e||(i[e+1>>2]|=t<<8*(e+1),e+=1);return e=0!==e?e:1,i[0]|=e,{value:e+1>4?i:[i[0]],binLen:8+8*e}}function tn(n){return w(rn(n.binLen),n)}function en(n,r){var t,e=rn(r),i=r>>>2,o=(i-(e=w(e,n)).value.length%i)%i;for(t=0;t<o;t++)e.value.push(0);return e.value}var on=function(n){function r(r,e,i){var o=this,u=6,w=0,s=i||{};if(1!==(o=n.call(this,r,e,i)||this).numRounds){if(s.kmacKey||s.hmacKey)throw new Error(\"Cannot set numRounds with MAC\");if(\"CSHAKE128\"===o.o||\"CSHAKE256\"===o.o)throw new Error(\"Cannot set numRounds for CSHAKE variants\")}switch(o.C=1,o.p=t(o.t,o.i,o.C),o.R=nn,o.B=$,o.L=W,o.m=W(),o.T=!1,r){case\"SHA3-224\":o.S=w=1152,o.U=224,o.g=!0,o.F=o.Y;break;case\"SHA3-256\":o.S=w=1088,o.U=256,o.g=!0,o.F=o.Y;break;case\"SHA3-384\":o.S=w=832,o.U=384,o.g=!0,o.F=o.Y;break;case\"SHA3-512\":o.S=w=576,o.U=512,o.g=!0,o.F=o.Y;break;case\"SHAKE128\":u=31,o.S=w=1344,o.U=-1,o.T=!0,o.g=!1,o.F=null;break;case\"SHAKE256\":u=31,o.S=w=1088,o.U=-1,o.T=!0,o.g=!1,o.F=null;break;case\"KMAC128\":u=4,o.S=w=1344,o.M(i),o.U=-1,o.T=!0,o.g=!1,o.F=o.X;break;case\"KMAC256\":u=4,o.S=w=1088,o.M(i),o.U=-1,o.T=!0,o.g=!1,o.F=o.X;break;case\"CSHAKE128\":o.S=w=1344,u=o.O(i),o.U=-1,o.T=!0,o.g=!1,o.F=null;break;case\"CSHAKE256\":o.S=w=1088,u=o.O(i),o.U=-1,o.T=!0,o.g=!1,o.F=null;break;default:throw new Error(f)}return o.K=function(n,r,t,e,i){return function(n,r,t,e,i,o,u){var f,w,s=0,a=[],h=i>>>5,c=r>>>5;for(f=0;f<c&&r>=i;f+=h)e=nn(n.slice(f,f+h),e),r-=i;for(n=n.slice(f),r%=i;n.length<h;)n.push(0);for(n[(f=r>>>3)>>2]^=o<<f%4*8,n[h-1]^=2147483648,e=nn(n,e);32*a.length<u&&(w=e[s%5][s/5|0],a.push(w.I),!(32*a.length>=u));)a.push(w.N),0==64*(s+=1)%i&&(nn(null,e),s=0);return a}(n,r,0,e,w,u,i)},s.hmacKey&&o.k(a(\"hmacKey\",s.hmacKey,o.C)),o}return v(r,n),r.prototype.O=function(n,r){var t=function(n){var r=n||{};return{funcName:a(\"funcName\",r.funcName,1,{value:[],binLen:0}),customization:a(\"Customization\",r.customization,1,{value:[],binLen:0})}}(n||{});r&&(t.funcName=r);var e=w(tn(t.funcName),tn(t.customization));if(0!==t.customization.binLen||0!==t.funcName.binLen){for(var i=en(e,this.S>>>3),o=0;o<i.length;o+=this.S>>>5)this.m=this.R(i.slice(o,o+(this.S>>>5)),this.m),this.v+=this.S;return 4}return 31},r.prototype.M=function(n){var r=function(n){var r=n||{};return{kmacKey:a(\"kmacKey\",r.kmacKey,1),funcName:{value:[1128353099],binLen:32},customization:a(\"Customization\",r.customization,1,{value:[],binLen:0})}}(n||{});this.O(n,r.funcName);for(var t=en(tn(r.kmacKey),this.S>>>3),e=0;e<t.length;e+=this.S>>>5)this.m=this.R(t.slice(e,e+(this.S>>>5)),this.m),this.v+=this.S;this.A=!0},r.prototype.X=function(n){var r=w({value:this.u.slice(),binLen:this.s},function(n){var r,t,e=0,i=[0,0],o=[4294967295&n,n/4294967296&2097151];for(r=6;r>=0;r--)0==(t=o[r>>2]>>>8*r&255)&&0===e||(i[e>>2]|=t<<8*e,e+=1);return i[(e=0!==e?e:1)>>2]|=e<<8*e,{value:e+1>4?i:[i[0]],binLen:8+8*e}}(n.outputLen));return this.K(r.value,r.binLen,this.v,this.B(this.m),n.outputLen)},r}(h);return function(){function n(n,r,t){if(\"SHA-1\"==n)this.j=new K(n,r,t);else if(\"SHA-224\"==n||\"SHA-256\"==n)this.j=new g(n,r,t);else if(\"SHA-384\"==n||\"SHA-512\"==n)this.j=new G(n,r,t);else{if(\"SHA3-224\"!=n&&\"SHA3-256\"!=n&&\"SHA3-384\"!=n&&\"SHA3-512\"!=n&&\"SHAKE128\"!=n&&\"SHAKE256\"!=n&&\"CSHAKE128\"!=n&&\"CSHAKE256\"!=n&&\"KMAC128\"!=n&&\"KMAC256\"!=n)throw new Error(f);this.j=new on(n,r,t)}}return n.prototype.update=function(n){this.j.update(n)},n.prototype.getHash=function(n,r){return this.j.getHash(n,r)},n.prototype.setHMACKey=function(n,r,t){this.j.setHMACKey(n,r,t)},n.prototype.getHMAC=function(n,r){return this.j.getHMAC(n,r)},n}()}));\n//# sourceMappingURL=sha.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/jssha/dist/sha.js\n");

/***/ })

};
;