"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@ledgerhq";
exports.ids = ["vendor-chunks/@ledgerhq"];
exports.modules = {

/***/ "(ssr)/./node_modules/@ledgerhq/devices/lib-es/index.js":
/*!********************************************************!*\
  !*** ./node_modules/@ledgerhq/devices/lib-es/index.js ***!
  \********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   IICCID: () => (/* binding */ IICCID),\n/* harmony export */   IIGenericHID: () => (/* binding */ IIGenericHID),\n/* harmony export */   IIKeyboardHID: () => (/* binding */ IIKeyboardHID),\n/* harmony export */   IIU2F: () => (/* binding */ IIU2F),\n/* harmony export */   IIWebUSB: () => (/* binding */ IIWebUSB),\n/* harmony export */   getBluetoothServiceUuids: () => (/* binding */ getBluetoothServiceUuids),\n/* harmony export */   getDeviceModel: () => (/* binding */ getDeviceModel),\n/* harmony export */   getInfosForServiceUuid: () => (/* binding */ getInfosForServiceUuid),\n/* harmony export */   identifyProductName: () => (/* binding */ identifyProductName),\n/* harmony export */   identifyUSBProductId: () => (/* binding */ identifyUSBProductId),\n/* harmony export */   ledgerUSBVendorId: () => (/* binding */ ledgerUSBVendorId)\n/* harmony export */ });\n/* harmony import */ var semver__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! semver */ \"(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/index.js\");\n/* harmony import */ var semver__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(semver__WEBPACK_IMPORTED_MODULE_0__);\n\n/**\n * The USB product IDs will be defined as MMII, encoding a model (MM) and an interface bitfield (II)\n *\n ** Model\n * Ledger Nano S : 0x10\n * Ledger Blue : 0x00\n * Ledger Nano X : 0x40\n *\n ** Interface support bitfield\n * Generic HID : 0x01\n * Keyboard HID : 0x02\n * U2F : 0x04\n * CCID : 0x08\n * WebUSB : 0x10\n */\n\nconst IIGenericHID = 0x01;\nconst IIKeyboardHID = 0x02;\nconst IIU2F = 0x04;\nconst IICCID = 0x08;\nconst IIWebUSB = 0x10;\nconst devices = {\n  blue: {\n    id: \"blue\",\n    productName: \"Ledger Blue\",\n    productIdMM: 0x00,\n    legacyUsbProductId: 0x0000,\n    usbOnly: true,\n    memorySize: 480 * 1024,\n    blockSize: 4 * 1024,\n    getBlockSize: _firwareVersion => 4 * 1024\n  },\n  nanoS: {\n    id: \"nanoS\",\n    productName: \"Ledger Nano S\",\n    productIdMM: 0x10,\n    legacyUsbProductId: 0x0001,\n    usbOnly: true,\n    memorySize: 320 * 1024,\n    blockSize: 4 * 1024,\n    getBlockSize: firmwareVersion => semver__WEBPACK_IMPORTED_MODULE_0___default().lt(semver__WEBPACK_IMPORTED_MODULE_0___default().coerce(firmwareVersion), \"2.0.0\") ? 4 * 1024 : 2 * 1024\n  },\n  nanoX: {\n    id: \"nanoX\",\n    productName: \"Ledger Nano X\",\n    productIdMM: 0x40,\n    legacyUsbProductId: 0x0004,\n    usbOnly: false,\n    memorySize: 2 * 1024 * 1024,\n    blockSize: 4 * 1024,\n    getBlockSize: _firwareVersion => 4 * 1024,\n    bluetoothSpec: [{\n      // this is the legacy one (prototype version). we will eventually drop it.\n      serviceUuid: \"d973f2e0-b19e-11e2-9e96-0800200c9a66\",\n      notifyUuid: \"d973f2e1-b19e-11e2-9e96-0800200c9a66\",\n      writeUuid: \"d973f2e2-b19e-11e2-9e96-0800200c9a66\"\n    }, {\n      serviceUuid: \"13d63400-2c97-0004-0000-4c6564676572\",\n      notifyUuid: \"13d63400-2c97-0004-0001-4c6564676572\",\n      writeUuid: \"13d63400-2c97-0004-0002-4c6564676572\"\n    }]\n  }\n};\nconst productMap = {\n  Blue: \"blue\",\n  \"Nano S\": \"nanoS\",\n  \"Nano X\": \"nanoX\"\n}; // $FlowFixMe\n\nconst devicesList = Object.values(devices);\n/**\n *\n */\n\nconst ledgerUSBVendorId = 0x2c97;\n/**\n *\n */\n\nconst getDeviceModel = id => {\n  const info = devices[id];\n  if (!info) throw new Error(\"device '\" + id + \"' does not exist\");\n  return info;\n};\n/**\n *\n */\n\nconst identifyUSBProductId = usbProductId => {\n  const legacy = devicesList.find(d => d.legacyUsbProductId === usbProductId);\n  if (legacy) return legacy;\n  const mm = usbProductId >> 8;\n  const deviceModel = devicesList.find(d => d.productIdMM === mm);\n  return deviceModel;\n};\nconst identifyProductName = productName => {\n  const productId = productMap[productName];\n  const deviceModel = devicesList.find(d => d.id === productId);\n  return deviceModel;\n};\nconst bluetoothServices = [];\nconst serviceUuidToInfos = {};\n\nfor (let id in devices) {\n  const deviceModel = devices[id];\n  const {\n    bluetoothSpec\n  } = deviceModel;\n\n  if (bluetoothSpec) {\n    for (let i = 0; i < bluetoothSpec.length; i++) {\n      const spec = bluetoothSpec[i];\n      bluetoothServices.push(spec.serviceUuid);\n      serviceUuidToInfos[spec.serviceUuid] = serviceUuidToInfos[spec.serviceUuid.replace(/-/g, \"\")] = {\n        deviceModel,\n        ...spec\n      };\n    }\n  }\n}\n/**\n *\n */\n\n\nconst getBluetoothServiceUuids = () => bluetoothServices;\n/**\n *\n */\n\nconst getInfosForServiceUuid = uuid => serviceUuidToInfos[uuid.toLowerCase()];\n/**\n *\n */\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@ledgerhq/devices/lib-es/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@ledgerhq/devices/lib/ble/receiveAPDU.js":
/*!***************************************************************!*\
  !*** ./node_modules/@ledgerhq/devices/lib/ble/receiveAPDU.js ***!
  \***************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports.receiveAPDU = void 0;\n\nvar _errors = __webpack_require__(/*! @ledgerhq/errors */ \"(ssr)/./node_modules/@ledgerhq/errors/dist/index.js\");\n\nvar _rxjs = __webpack_require__(/*! rxjs */ \"(ssr)/./node_modules/rxjs/_esm5/index.js\");\n\nvar _logs = __webpack_require__(/*! @ledgerhq/logs */ \"(ssr)/./node_modules/@ledgerhq/logs/lib-es/index.js\");\n\nconst TagId = 0x05; // operator that transform the input raw stream into one apdu response and finishes\n\nconst receiveAPDU = rawStream => _rxjs.Observable.create(o => {\n  let notifiedIndex = 0;\n  let notifiedDataLength = 0;\n  let notifiedData = Buffer.alloc(0);\n  const sub = rawStream.subscribe({\n    complete: () => {\n      o.error(new _errors.DisconnectedDevice());\n      sub.unsubscribe();\n    },\n    error: e => {\n      (0, _logs.log)(\"ble-error\", \"in receiveAPDU \" + String(e));\n      o.error(e);\n      sub.unsubscribe();\n    },\n    next: value => {\n      const tag = value.readUInt8(0);\n      const index = value.readUInt16BE(1);\n      let data = value.slice(3);\n\n      if (tag !== TagId) {\n        o.error(new _errors.TransportError(\"Invalid tag \" + tag.toString(16), \"InvalidTag\"));\n        return;\n      }\n\n      if (notifiedIndex !== index) {\n        o.error(new _errors.TransportError(\"BLE: Invalid sequence number. discontinued chunk. Received \" + index + \" but expected \" + notifiedIndex, \"InvalidSequence\"));\n        return;\n      }\n\n      if (index === 0) {\n        notifiedDataLength = data.readUInt16BE(0);\n        data = data.slice(2);\n      }\n\n      notifiedIndex++;\n      notifiedData = Buffer.concat([notifiedData, data]);\n\n      if (notifiedData.length > notifiedDataLength) {\n        o.error(new _errors.TransportError(\"BLE: received too much data. discontinued chunk. Received \" + notifiedData.length + \" but expected \" + notifiedDataLength, \"BLETooMuchData\"));\n        return;\n      }\n\n      if (notifiedData.length === notifiedDataLength) {\n        o.next(notifiedData);\n        o.complete();\n        sub.unsubscribe();\n      }\n    }\n  });\n  return () => {\n    sub.unsubscribe();\n  };\n});\n\nexports.receiveAPDU = receiveAPDU;\n//# sourceMappingURL=receiveAPDU.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQGxlZGdlcmhxL2RldmljZXMvbGliL2JsZS9yZWNlaXZlQVBEVS5qcyIsIm1hcHBpbmdzIjoiQUFBYTs7QUFFYiw4Q0FBNkM7QUFDN0M7QUFDQSxDQUFDLEVBQUM7QUFDRixtQkFBbUI7O0FBRW5CLGNBQWMsbUJBQU8sQ0FBQyw2RUFBa0I7O0FBRXhDLFlBQVksbUJBQU8sQ0FBQyxzREFBTTs7QUFFMUIsWUFBWSxtQkFBTyxDQUFDLDJFQUFnQjs7QUFFcEMsb0JBQW9COztBQUVwQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQTtBQUNBLENBQUM7O0FBRUQsbUJBQW1CO0FBQ25CIiwic291cmNlcyI6WyIvVXNlcnMveHltL2NvZGUvdGJvb2svb250b25fY2Mvb25pb24tbGF1bmNoL2Zyb250ZW5kL25vZGVfbW9kdWxlcy9AbGVkZ2VyaHEvZGV2aWNlcy9saWIvYmxlL3JlY2VpdmVBUERVLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuXG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHtcbiAgdmFsdWU6IHRydWVcbn0pO1xuZXhwb3J0cy5yZWNlaXZlQVBEVSA9IHZvaWQgMDtcblxudmFyIF9lcnJvcnMgPSByZXF1aXJlKFwiQGxlZGdlcmhxL2Vycm9yc1wiKTtcblxudmFyIF9yeGpzID0gcmVxdWlyZShcInJ4anNcIik7XG5cbnZhciBfbG9ncyA9IHJlcXVpcmUoXCJAbGVkZ2VyaHEvbG9nc1wiKTtcblxuY29uc3QgVGFnSWQgPSAweDA1OyAvLyBvcGVyYXRvciB0aGF0IHRyYW5zZm9ybSB0aGUgaW5wdXQgcmF3IHN0cmVhbSBpbnRvIG9uZSBhcGR1IHJlc3BvbnNlIGFuZCBmaW5pc2hlc1xuXG5jb25zdCByZWNlaXZlQVBEVSA9IHJhd1N0cmVhbSA9PiBfcnhqcy5PYnNlcnZhYmxlLmNyZWF0ZShvID0+IHtcbiAgbGV0IG5vdGlmaWVkSW5kZXggPSAwO1xuICBsZXQgbm90aWZpZWREYXRhTGVuZ3RoID0gMDtcbiAgbGV0IG5vdGlmaWVkRGF0YSA9IEJ1ZmZlci5hbGxvYygwKTtcbiAgY29uc3Qgc3ViID0gcmF3U3RyZWFtLnN1YnNjcmliZSh7XG4gICAgY29tcGxldGU6ICgpID0+IHtcbiAgICAgIG8uZXJyb3IobmV3IF9lcnJvcnMuRGlzY29ubmVjdGVkRGV2aWNlKCkpO1xuICAgICAgc3ViLnVuc3Vic2NyaWJlKCk7XG4gICAgfSxcbiAgICBlcnJvcjogZSA9PiB7XG4gICAgICAoMCwgX2xvZ3MubG9nKShcImJsZS1lcnJvclwiLCBcImluIHJlY2VpdmVBUERVIFwiICsgU3RyaW5nKGUpKTtcbiAgICAgIG8uZXJyb3IoZSk7XG4gICAgICBzdWIudW5zdWJzY3JpYmUoKTtcbiAgICB9LFxuICAgIG5leHQ6IHZhbHVlID0+IHtcbiAgICAgIGNvbnN0IHRhZyA9IHZhbHVlLnJlYWRVSW50OCgwKTtcbiAgICAgIGNvbnN0IGluZGV4ID0gdmFsdWUucmVhZFVJbnQxNkJFKDEpO1xuICAgICAgbGV0IGRhdGEgPSB2YWx1ZS5zbGljZSgzKTtcblxuICAgICAgaWYgKHRhZyAhPT0gVGFnSWQpIHtcbiAgICAgICAgby5lcnJvcihuZXcgX2Vycm9ycy5UcmFuc3BvcnRFcnJvcihcIkludmFsaWQgdGFnIFwiICsgdGFnLnRvU3RyaW5nKDE2KSwgXCJJbnZhbGlkVGFnXCIpKTtcbiAgICAgICAgcmV0dXJuO1xuICAgICAgfVxuXG4gICAgICBpZiAobm90aWZpZWRJbmRleCAhPT0gaW5kZXgpIHtcbiAgICAgICAgby5lcnJvcihuZXcgX2Vycm9ycy5UcmFuc3BvcnRFcnJvcihcIkJMRTogSW52YWxpZCBzZXF1ZW5jZSBudW1iZXIuIGRpc2NvbnRpbnVlZCBjaHVuay4gUmVjZWl2ZWQgXCIgKyBpbmRleCArIFwiIGJ1dCBleHBlY3RlZCBcIiArIG5vdGlmaWVkSW5kZXgsIFwiSW52YWxpZFNlcXVlbmNlXCIpKTtcbiAgICAgICAgcmV0dXJuO1xuICAgICAgfVxuXG4gICAgICBpZiAoaW5kZXggPT09IDApIHtcbiAgICAgICAgbm90aWZpZWREYXRhTGVuZ3RoID0gZGF0YS5yZWFkVUludDE2QkUoMCk7XG4gICAgICAgIGRhdGEgPSBkYXRhLnNsaWNlKDIpO1xuICAgICAgfVxuXG4gICAgICBub3RpZmllZEluZGV4Kys7XG4gICAgICBub3RpZmllZERhdGEgPSBCdWZmZXIuY29uY2F0KFtub3RpZmllZERhdGEsIGRhdGFdKTtcblxuICAgICAgaWYgKG5vdGlmaWVkRGF0YS5sZW5ndGggPiBub3RpZmllZERhdGFMZW5ndGgpIHtcbiAgICAgICAgby5lcnJvcihuZXcgX2Vycm9ycy5UcmFuc3BvcnRFcnJvcihcIkJMRTogcmVjZWl2ZWQgdG9vIG11Y2ggZGF0YS4gZGlzY29udGludWVkIGNodW5rLiBSZWNlaXZlZCBcIiArIG5vdGlmaWVkRGF0YS5sZW5ndGggKyBcIiBidXQgZXhwZWN0ZWQgXCIgKyBub3RpZmllZERhdGFMZW5ndGgsIFwiQkxFVG9vTXVjaERhdGFcIikpO1xuICAgICAgICByZXR1cm47XG4gICAgICB9XG5cbiAgICAgIGlmIChub3RpZmllZERhdGEubGVuZ3RoID09PSBub3RpZmllZERhdGFMZW5ndGgpIHtcbiAgICAgICAgby5uZXh0KG5vdGlmaWVkRGF0YSk7XG4gICAgICAgIG8uY29tcGxldGUoKTtcbiAgICAgICAgc3ViLnVuc3Vic2NyaWJlKCk7XG4gICAgICB9XG4gICAgfVxuICB9KTtcbiAgcmV0dXJuICgpID0+IHtcbiAgICBzdWIudW5zdWJzY3JpYmUoKTtcbiAgfTtcbn0pO1xuXG5leHBvcnRzLnJlY2VpdmVBUERVID0gcmVjZWl2ZUFQRFU7XG4vLyMgc291cmNlTWFwcGluZ1VSTD1yZWNlaXZlQVBEVS5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@ledgerhq/devices/lib/ble/receiveAPDU.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@ledgerhq/devices/lib/ble/sendAPDU.js":
/*!************************************************************!*\
  !*** ./node_modules/@ledgerhq/devices/lib/ble/sendAPDU.js ***!
  \************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports.sendAPDU = void 0;\n\nvar _rxjs = __webpack_require__(/*! rxjs */ \"(ssr)/./node_modules/rxjs/_esm5/index.js\");\n\nvar _logs = __webpack_require__(/*! @ledgerhq/logs */ \"(ssr)/./node_modules/@ledgerhq/logs/lib-es/index.js\");\n\nconst TagId = 0x05;\n\nfunction chunkBuffer(buffer, sizeForIndex) {\n  const chunks = [];\n\n  for (let i = 0, size = sizeForIndex(0); i < buffer.length; i += size, size = sizeForIndex(i)) {\n    chunks.push(buffer.slice(i, i + size));\n  }\n\n  return chunks;\n}\n\nconst sendAPDU = (write, apdu, mtuSize) => {\n  const chunks = chunkBuffer(apdu, i => mtuSize - (i === 0 ? 5 : 3)).map((buffer, i) => {\n    const head = Buffer.alloc(i === 0 ? 5 : 3);\n    head.writeUInt8(TagId, 0);\n    head.writeUInt16BE(i, 1);\n\n    if (i === 0) {\n      head.writeUInt16BE(apdu.length, 3);\n    }\n\n    return Buffer.concat([head, buffer]);\n  });\n  return _rxjs.Observable.create(o => {\n    let terminated = false;\n\n    async function main() {\n      for (const chunk of chunks) {\n        if (terminated) return;\n        await write(chunk);\n      }\n    }\n\n    main().then(() => {\n      terminated = true;\n      o.complete();\n    }, e => {\n      terminated = true;\n      (0, _logs.log)(\"ble-error\", \"sendAPDU failure \" + String(e));\n      o.error(e);\n    });\n\n    const unsubscribe = () => {\n      if (!terminated) {\n        (0, _logs.log)(\"ble-verbose\", \"sendAPDU interruption\");\n        terminated = true;\n      }\n    };\n\n    return unsubscribe;\n  });\n};\n\nexports.sendAPDU = sendAPDU;\n//# sourceMappingURL=sendAPDU.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@ledgerhq/devices/lib/ble/sendAPDU.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@ledgerhq/devices/lib/hid-framing.js":
/*!***********************************************************!*\
  !*** ./node_modules/@ledgerhq/devices/lib/hid-framing.js ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports[\"default\"] = void 0;\n\nvar _errors = __webpack_require__(/*! @ledgerhq/errors */ \"(ssr)/./node_modules/@ledgerhq/errors/dist/index.js\");\n\nconst Tag = 0x05;\n\nfunction asUInt16BE(value) {\n  const b = Buffer.alloc(2);\n  b.writeUInt16BE(value, 0);\n  return b;\n}\n\nconst initialAcc = {\n  data: Buffer.alloc(0),\n  dataLength: 0,\n  sequence: 0\n};\n/**\n *\n */\n\nconst createHIDframing = (channel, packetSize) => {\n  return {\n    makeBlocks(apdu) {\n      let data = Buffer.concat([asUInt16BE(apdu.length), apdu]);\n      const blockSize = packetSize - 5;\n      const nbBlocks = Math.ceil(data.length / blockSize);\n      data = Buffer.concat([data, // fill data with padding\n      Buffer.alloc(nbBlocks * blockSize - data.length + 1).fill(0)]);\n      const blocks = [];\n\n      for (let i = 0; i < nbBlocks; i++) {\n        const head = Buffer.alloc(5);\n        head.writeUInt16BE(channel, 0);\n        head.writeUInt8(Tag, 2);\n        head.writeUInt16BE(i, 3);\n        const chunk = data.slice(i * blockSize, (i + 1) * blockSize);\n        blocks.push(Buffer.concat([head, chunk]));\n      }\n\n      return blocks;\n    },\n\n    reduceResponse(acc, chunk) {\n      let {\n        data,\n        dataLength,\n        sequence\n      } = acc || initialAcc;\n\n      if (chunk.readUInt16BE(0) !== channel) {\n        throw new _errors.TransportError(\"Invalid channel\", \"InvalidChannel\");\n      }\n\n      if (chunk.readUInt8(2) !== Tag) {\n        throw new _errors.TransportError(\"Invalid tag\", \"InvalidTag\");\n      }\n\n      if (chunk.readUInt16BE(3) !== sequence) {\n        throw new _errors.TransportError(\"Invalid sequence\", \"InvalidSequence\");\n      }\n\n      if (!acc) {\n        dataLength = chunk.readUInt16BE(5);\n      }\n\n      sequence++;\n      const chunkData = chunk.slice(acc ? 5 : 7);\n      data = Buffer.concat([data, chunkData]);\n\n      if (data.length > dataLength) {\n        data = data.slice(0, dataLength);\n      }\n\n      return {\n        data,\n        dataLength,\n        sequence\n      };\n    },\n\n    getReducedResult(acc) {\n      if (acc && acc.dataLength === acc.data.length) {\n        return acc.data;\n      }\n    }\n\n  };\n};\n\nvar _default = createHIDframing;\nexports[\"default\"] = _default;\n//# sourceMappingURL=hid-framing.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@ledgerhq/devices/lib/hid-framing.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/classes/comparator.js":
/*!**********************************************************************************!*\
  !*** ./node_modules/@ledgerhq/devices/node_modules/semver/classes/comparator.js ***!
  \**********************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst ANY = Symbol('SemVer ANY')\n// hoisted class for cyclic dependency\nclass Comparator {\n  static get ANY () {\n    return ANY\n  }\n\n  constructor (comp, options) {\n    options = parseOptions(options)\n\n    if (comp instanceof Comparator) {\n      if (comp.loose === !!options.loose) {\n        return comp\n      } else {\n        comp = comp.value\n      }\n    }\n\n    comp = comp.trim().split(/\\s+/).join(' ')\n    debug('comparator', comp, options)\n    this.options = options\n    this.loose = !!options.loose\n    this.parse(comp)\n\n    if (this.semver === ANY) {\n      this.value = ''\n    } else {\n      this.value = this.operator + this.semver.version\n    }\n\n    debug('comp', this)\n  }\n\n  parse (comp) {\n    const r = this.options.loose ? re[t.COMPARATORLOOSE] : re[t.COMPARATOR]\n    const m = comp.match(r)\n\n    if (!m) {\n      throw new TypeError(`Invalid comparator: ${comp}`)\n    }\n\n    this.operator = m[1] !== undefined ? m[1] : ''\n    if (this.operator === '=') {\n      this.operator = ''\n    }\n\n    // if it literally is just '>' or '' then allow anything.\n    if (!m[2]) {\n      this.semver = ANY\n    } else {\n      this.semver = new SemVer(m[2], this.options.loose)\n    }\n  }\n\n  toString () {\n    return this.value\n  }\n\n  test (version) {\n    debug('Comparator.test', version, this.options.loose)\n\n    if (this.semver === ANY || version === ANY) {\n      return true\n    }\n\n    if (typeof version === 'string') {\n      try {\n        version = new SemVer(version, this.options)\n      } catch (er) {\n        return false\n      }\n    }\n\n    return cmp(version, this.operator, this.semver, this.options)\n  }\n\n  intersects (comp, options) {\n    if (!(comp instanceof Comparator)) {\n      throw new TypeError('a Comparator is required')\n    }\n\n    if (this.operator === '') {\n      if (this.value === '') {\n        return true\n      }\n      return new Range(comp.value, options).test(this.value)\n    } else if (comp.operator === '') {\n      if (comp.value === '') {\n        return true\n      }\n      return new Range(this.value, options).test(comp.semver)\n    }\n\n    options = parseOptions(options)\n\n    // Special cases where nothing can possibly be lower\n    if (options.includePrerelease &&\n      (this.value === '<0.0.0-0' || comp.value === '<0.0.0-0')) {\n      return false\n    }\n    if (!options.includePrerelease &&\n      (this.value.startsWith('<0.0.0') || comp.value.startsWith('<0.0.0'))) {\n      return false\n    }\n\n    // Same direction increasing (> or >=)\n    if (this.operator.startsWith('>') && comp.operator.startsWith('>')) {\n      return true\n    }\n    // Same direction decreasing (< or <=)\n    if (this.operator.startsWith('<') && comp.operator.startsWith('<')) {\n      return true\n    }\n    // same SemVer and both sides are inclusive (<= or >=)\n    if (\n      (this.semver.version === comp.semver.version) &&\n      this.operator.includes('=') && comp.operator.includes('=')) {\n      return true\n    }\n    // opposite directions less than\n    if (cmp(this.semver, '<', comp.semver, options) &&\n      this.operator.startsWith('>') && comp.operator.startsWith('<')) {\n      return true\n    }\n    // opposite directions greater than\n    if (cmp(this.semver, '>', comp.semver, options) &&\n      this.operator.startsWith('<') && comp.operator.startsWith('>')) {\n      return true\n    }\n    return false\n  }\n}\n\nmodule.exports = Comparator\n\nconst parseOptions = __webpack_require__(/*! ../internal/parse-options */ \"(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/internal/parse-options.js\")\nconst { safeRe: re, t } = __webpack_require__(/*! ../internal/re */ \"(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/internal/re.js\")\nconst cmp = __webpack_require__(/*! ../functions/cmp */ \"(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/functions/cmp.js\")\nconst debug = __webpack_require__(/*! ../internal/debug */ \"(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/internal/debug.js\")\nconst SemVer = __webpack_require__(/*! ./semver */ \"(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/classes/semver.js\")\nconst Range = __webpack_require__(/*! ./range */ \"(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/classes/range.js\")\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/classes/comparator.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/classes/range.js":
/*!*****************************************************************************!*\
  !*** ./node_modules/@ledgerhq/devices/node_modules/semver/classes/range.js ***!
  \*****************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst SPACE_CHARACTERS = /\\s+/g\n\n// hoisted class for cyclic dependency\nclass Range {\n  constructor (range, options) {\n    options = parseOptions(options)\n\n    if (range instanceof Range) {\n      if (\n        range.loose === !!options.loose &&\n        range.includePrerelease === !!options.includePrerelease\n      ) {\n        return range\n      } else {\n        return new Range(range.raw, options)\n      }\n    }\n\n    if (range instanceof Comparator) {\n      // just put it in the set and return\n      this.raw = range.value\n      this.set = [[range]]\n      this.formatted = undefined\n      return this\n    }\n\n    this.options = options\n    this.loose = !!options.loose\n    this.includePrerelease = !!options.includePrerelease\n\n    // First reduce all whitespace as much as possible so we do not have to rely\n    // on potentially slow regexes like \\s*. This is then stored and used for\n    // future error messages as well.\n    this.raw = range.trim().replace(SPACE_CHARACTERS, ' ')\n\n    // First, split on ||\n    this.set = this.raw\n      .split('||')\n      // map the range to a 2d array of comparators\n      .map(r => this.parseRange(r.trim()))\n      // throw out any comparator lists that are empty\n      // this generally means that it was not a valid range, which is allowed\n      // in loose mode, but will still throw if the WHOLE range is invalid.\n      .filter(c => c.length)\n\n    if (!this.set.length) {\n      throw new TypeError(`Invalid SemVer Range: ${this.raw}`)\n    }\n\n    // if we have any that are not the null set, throw out null sets.\n    if (this.set.length > 1) {\n      // keep the first one, in case they're all null sets\n      const first = this.set[0]\n      this.set = this.set.filter(c => !isNullSet(c[0]))\n      if (this.set.length === 0) {\n        this.set = [first]\n      } else if (this.set.length > 1) {\n        // if we have any that are *, then the range is just *\n        for (const c of this.set) {\n          if (c.length === 1 && isAny(c[0])) {\n            this.set = [c]\n            break\n          }\n        }\n      }\n    }\n\n    this.formatted = undefined\n  }\n\n  get range () {\n    if (this.formatted === undefined) {\n      this.formatted = ''\n      for (let i = 0; i < this.set.length; i++) {\n        if (i > 0) {\n          this.formatted += '||'\n        }\n        const comps = this.set[i]\n        for (let k = 0; k < comps.length; k++) {\n          if (k > 0) {\n            this.formatted += ' '\n          }\n          this.formatted += comps[k].toString().trim()\n        }\n      }\n    }\n    return this.formatted\n  }\n\n  format () {\n    return this.range\n  }\n\n  toString () {\n    return this.range\n  }\n\n  parseRange (range) {\n    // memoize range parsing for performance.\n    // this is a very hot path, and fully deterministic.\n    const memoOpts =\n      (this.options.includePrerelease && FLAG_INCLUDE_PRERELEASE) |\n      (this.options.loose && FLAG_LOOSE)\n    const memoKey = memoOpts + ':' + range\n    const cached = cache.get(memoKey)\n    if (cached) {\n      return cached\n    }\n\n    const loose = this.options.loose\n    // `1.2.3 - 1.2.4` => `>=1.2.3 <=1.2.4`\n    const hr = loose ? re[t.HYPHENRANGELOOSE] : re[t.HYPHENRANGE]\n    range = range.replace(hr, hyphenReplace(this.options.includePrerelease))\n    debug('hyphen replace', range)\n\n    // `> 1.2.3 < 1.2.5` => `>1.2.3 <1.2.5`\n    range = range.replace(re[t.COMPARATORTRIM], comparatorTrimReplace)\n    debug('comparator trim', range)\n\n    // `~ 1.2.3` => `~1.2.3`\n    range = range.replace(re[t.TILDETRIM], tildeTrimReplace)\n    debug('tilde trim', range)\n\n    // `^ 1.2.3` => `^1.2.3`\n    range = range.replace(re[t.CARETTRIM], caretTrimReplace)\n    debug('caret trim', range)\n\n    // At this point, the range is completely trimmed and\n    // ready to be split into comparators.\n\n    let rangeList = range\n      .split(' ')\n      .map(comp => parseComparator(comp, this.options))\n      .join(' ')\n      .split(/\\s+/)\n      // >=0.0.0 is equivalent to *\n      .map(comp => replaceGTE0(comp, this.options))\n\n    if (loose) {\n      // in loose mode, throw out any that are not valid comparators\n      rangeList = rangeList.filter(comp => {\n        debug('loose invalid filter', comp, this.options)\n        return !!comp.match(re[t.COMPARATORLOOSE])\n      })\n    }\n    debug('range list', rangeList)\n\n    // if any comparators are the null set, then replace with JUST null set\n    // if more than one comparator, remove any * comparators\n    // also, don't include the same comparator more than once\n    const rangeMap = new Map()\n    const comparators = rangeList.map(comp => new Comparator(comp, this.options))\n    for (const comp of comparators) {\n      if (isNullSet(comp)) {\n        return [comp]\n      }\n      rangeMap.set(comp.value, comp)\n    }\n    if (rangeMap.size > 1 && rangeMap.has('')) {\n      rangeMap.delete('')\n    }\n\n    const result = [...rangeMap.values()]\n    cache.set(memoKey, result)\n    return result\n  }\n\n  intersects (range, options) {\n    if (!(range instanceof Range)) {\n      throw new TypeError('a Range is required')\n    }\n\n    return this.set.some((thisComparators) => {\n      return (\n        isSatisfiable(thisComparators, options) &&\n        range.set.some((rangeComparators) => {\n          return (\n            isSatisfiable(rangeComparators, options) &&\n            thisComparators.every((thisComparator) => {\n              return rangeComparators.every((rangeComparator) => {\n                return thisComparator.intersects(rangeComparator, options)\n              })\n            })\n          )\n        })\n      )\n    })\n  }\n\n  // if ANY of the sets match ALL of its comparators, then pass\n  test (version) {\n    if (!version) {\n      return false\n    }\n\n    if (typeof version === 'string') {\n      try {\n        version = new SemVer(version, this.options)\n      } catch (er) {\n        return false\n      }\n    }\n\n    for (let i = 0; i < this.set.length; i++) {\n      if (testSet(this.set[i], version, this.options)) {\n        return true\n      }\n    }\n    return false\n  }\n}\n\nmodule.exports = Range\n\nconst LRU = __webpack_require__(/*! ../internal/lrucache */ \"(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/internal/lrucache.js\")\nconst cache = new LRU()\n\nconst parseOptions = __webpack_require__(/*! ../internal/parse-options */ \"(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/internal/parse-options.js\")\nconst Comparator = __webpack_require__(/*! ./comparator */ \"(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/classes/comparator.js\")\nconst debug = __webpack_require__(/*! ../internal/debug */ \"(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/internal/debug.js\")\nconst SemVer = __webpack_require__(/*! ./semver */ \"(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/classes/semver.js\")\nconst {\n  safeRe: re,\n  t,\n  comparatorTrimReplace,\n  tildeTrimReplace,\n  caretTrimReplace,\n} = __webpack_require__(/*! ../internal/re */ \"(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/internal/re.js\")\nconst { FLAG_INCLUDE_PRERELEASE, FLAG_LOOSE } = __webpack_require__(/*! ../internal/constants */ \"(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/internal/constants.js\")\n\nconst isNullSet = c => c.value === '<0.0.0-0'\nconst isAny = c => c.value === ''\n\n// take a set of comparators and determine whether there\n// exists a version which can satisfy it\nconst isSatisfiable = (comparators, options) => {\n  let result = true\n  const remainingComparators = comparators.slice()\n  let testComparator = remainingComparators.pop()\n\n  while (result && remainingComparators.length) {\n    result = remainingComparators.every((otherComparator) => {\n      return testComparator.intersects(otherComparator, options)\n    })\n\n    testComparator = remainingComparators.pop()\n  }\n\n  return result\n}\n\n// comprised of xranges, tildes, stars, and gtlt's at this point.\n// already replaced the hyphen ranges\n// turn into a set of JUST comparators.\nconst parseComparator = (comp, options) => {\n  debug('comp', comp, options)\n  comp = replaceCarets(comp, options)\n  debug('caret', comp)\n  comp = replaceTildes(comp, options)\n  debug('tildes', comp)\n  comp = replaceXRanges(comp, options)\n  debug('xrange', comp)\n  comp = replaceStars(comp, options)\n  debug('stars', comp)\n  return comp\n}\n\nconst isX = id => !id || id.toLowerCase() === 'x' || id === '*'\n\n// ~, ~> --> * (any, kinda silly)\n// ~2, ~2.x, ~2.x.x, ~>2, ~>2.x ~>2.x.x --> >=2.0.0 <3.0.0-0\n// ~2.0, ~2.0.x, ~>2.0, ~>2.0.x --> >=2.0.0 <2.1.0-0\n// ~1.2, ~1.2.x, ~>1.2, ~>1.2.x --> >=1.2.0 <1.3.0-0\n// ~1.2.3, ~>1.2.3 --> >=1.2.3 <1.3.0-0\n// ~1.2.0, ~>1.2.0 --> >=1.2.0 <1.3.0-0\n// ~0.0.1 --> >=0.0.1 <0.1.0-0\nconst replaceTildes = (comp, options) => {\n  return comp\n    .trim()\n    .split(/\\s+/)\n    .map((c) => replaceTilde(c, options))\n    .join(' ')\n}\n\nconst replaceTilde = (comp, options) => {\n  const r = options.loose ? re[t.TILDELOOSE] : re[t.TILDE]\n  return comp.replace(r, (_, M, m, p, pr) => {\n    debug('tilde', comp, _, M, m, p, pr)\n    let ret\n\n    if (isX(M)) {\n      ret = ''\n    } else if (isX(m)) {\n      ret = `>=${M}.0.0 <${+M + 1}.0.0-0`\n    } else if (isX(p)) {\n      // ~1.2 == >=1.2.0 <1.3.0-0\n      ret = `>=${M}.${m}.0 <${M}.${+m + 1}.0-0`\n    } else if (pr) {\n      debug('replaceTilde pr', pr)\n      ret = `>=${M}.${m}.${p}-${pr\n      } <${M}.${+m + 1}.0-0`\n    } else {\n      // ~1.2.3 == >=1.2.3 <1.3.0-0\n      ret = `>=${M}.${m}.${p\n      } <${M}.${+m + 1}.0-0`\n    }\n\n    debug('tilde return', ret)\n    return ret\n  })\n}\n\n// ^ --> * (any, kinda silly)\n// ^2, ^2.x, ^2.x.x --> >=2.0.0 <3.0.0-0\n// ^2.0, ^2.0.x --> >=2.0.0 <3.0.0-0\n// ^1.2, ^1.2.x --> >=1.2.0 <2.0.0-0\n// ^1.2.3 --> >=1.2.3 <2.0.0-0\n// ^1.2.0 --> >=1.2.0 <2.0.0-0\n// ^0.0.1 --> >=0.0.1 <0.0.2-0\n// ^0.1.0 --> >=0.1.0 <0.2.0-0\nconst replaceCarets = (comp, options) => {\n  return comp\n    .trim()\n    .split(/\\s+/)\n    .map((c) => replaceCaret(c, options))\n    .join(' ')\n}\n\nconst replaceCaret = (comp, options) => {\n  debug('caret', comp, options)\n  const r = options.loose ? re[t.CARETLOOSE] : re[t.CARET]\n  const z = options.includePrerelease ? '-0' : ''\n  return comp.replace(r, (_, M, m, p, pr) => {\n    debug('caret', comp, _, M, m, p, pr)\n    let ret\n\n    if (isX(M)) {\n      ret = ''\n    } else if (isX(m)) {\n      ret = `>=${M}.0.0${z} <${+M + 1}.0.0-0`\n    } else if (isX(p)) {\n      if (M === '0') {\n        ret = `>=${M}.${m}.0${z} <${M}.${+m + 1}.0-0`\n      } else {\n        ret = `>=${M}.${m}.0${z} <${+M + 1}.0.0-0`\n      }\n    } else if (pr) {\n      debug('replaceCaret pr', pr)\n      if (M === '0') {\n        if (m === '0') {\n          ret = `>=${M}.${m}.${p}-${pr\n          } <${M}.${m}.${+p + 1}-0`\n        } else {\n          ret = `>=${M}.${m}.${p}-${pr\n          } <${M}.${+m + 1}.0-0`\n        }\n      } else {\n        ret = `>=${M}.${m}.${p}-${pr\n        } <${+M + 1}.0.0-0`\n      }\n    } else {\n      debug('no pr')\n      if (M === '0') {\n        if (m === '0') {\n          ret = `>=${M}.${m}.${p\n          }${z} <${M}.${m}.${+p + 1}-0`\n        } else {\n          ret = `>=${M}.${m}.${p\n          }${z} <${M}.${+m + 1}.0-0`\n        }\n      } else {\n        ret = `>=${M}.${m}.${p\n        } <${+M + 1}.0.0-0`\n      }\n    }\n\n    debug('caret return', ret)\n    return ret\n  })\n}\n\nconst replaceXRanges = (comp, options) => {\n  debug('replaceXRanges', comp, options)\n  return comp\n    .split(/\\s+/)\n    .map((c) => replaceXRange(c, options))\n    .join(' ')\n}\n\nconst replaceXRange = (comp, options) => {\n  comp = comp.trim()\n  const r = options.loose ? re[t.XRANGELOOSE] : re[t.XRANGE]\n  return comp.replace(r, (ret, gtlt, M, m, p, pr) => {\n    debug('xRange', comp, ret, gtlt, M, m, p, pr)\n    const xM = isX(M)\n    const xm = xM || isX(m)\n    const xp = xm || isX(p)\n    const anyX = xp\n\n    if (gtlt === '=' && anyX) {\n      gtlt = ''\n    }\n\n    // if we're including prereleases in the match, then we need\n    // to fix this to -0, the lowest possible prerelease value\n    pr = options.includePrerelease ? '-0' : ''\n\n    if (xM) {\n      if (gtlt === '>' || gtlt === '<') {\n        // nothing is allowed\n        ret = '<0.0.0-0'\n      } else {\n        // nothing is forbidden\n        ret = '*'\n      }\n    } else if (gtlt && anyX) {\n      // we know patch is an x, because we have any x at all.\n      // replace X with 0\n      if (xm) {\n        m = 0\n      }\n      p = 0\n\n      if (gtlt === '>') {\n        // >1 => >=2.0.0\n        // >1.2 => >=1.3.0\n        gtlt = '>='\n        if (xm) {\n          M = +M + 1\n          m = 0\n          p = 0\n        } else {\n          m = +m + 1\n          p = 0\n        }\n      } else if (gtlt === '<=') {\n        // <=0.7.x is actually <0.8.0, since any 0.7.x should\n        // pass.  Similarly, <=7.x is actually <8.0.0, etc.\n        gtlt = '<'\n        if (xm) {\n          M = +M + 1\n        } else {\n          m = +m + 1\n        }\n      }\n\n      if (gtlt === '<') {\n        pr = '-0'\n      }\n\n      ret = `${gtlt + M}.${m}.${p}${pr}`\n    } else if (xm) {\n      ret = `>=${M}.0.0${pr} <${+M + 1}.0.0-0`\n    } else if (xp) {\n      ret = `>=${M}.${m}.0${pr\n      } <${M}.${+m + 1}.0-0`\n    }\n\n    debug('xRange return', ret)\n\n    return ret\n  })\n}\n\n// Because * is AND-ed with everything else in the comparator,\n// and '' means \"any version\", just remove the *s entirely.\nconst replaceStars = (comp, options) => {\n  debug('replaceStars', comp, options)\n  // Looseness is ignored here.  star is always as loose as it gets!\n  return comp\n    .trim()\n    .replace(re[t.STAR], '')\n}\n\nconst replaceGTE0 = (comp, options) => {\n  debug('replaceGTE0', comp, options)\n  return comp\n    .trim()\n    .replace(re[options.includePrerelease ? t.GTE0PRE : t.GTE0], '')\n}\n\n// This function is passed to string.replace(re[t.HYPHENRANGE])\n// M, m, patch, prerelease, build\n// 1.2 - 3.4.5 => >=1.2.0 <=3.4.5\n// 1.2.3 - 3.4 => >=1.2.0 <3.5.0-0 Any 3.4.x will do\n// 1.2 - 3.4 => >=1.2.0 <3.5.0-0\n// TODO build?\nconst hyphenReplace = incPr => ($0,\n  from, fM, fm, fp, fpr, fb,\n  to, tM, tm, tp, tpr) => {\n  if (isX(fM)) {\n    from = ''\n  } else if (isX(fm)) {\n    from = `>=${fM}.0.0${incPr ? '-0' : ''}`\n  } else if (isX(fp)) {\n    from = `>=${fM}.${fm}.0${incPr ? '-0' : ''}`\n  } else if (fpr) {\n    from = `>=${from}`\n  } else {\n    from = `>=${from}${incPr ? '-0' : ''}`\n  }\n\n  if (isX(tM)) {\n    to = ''\n  } else if (isX(tm)) {\n    to = `<${+tM + 1}.0.0-0`\n  } else if (isX(tp)) {\n    to = `<${tM}.${+tm + 1}.0-0`\n  } else if (tpr) {\n    to = `<=${tM}.${tm}.${tp}-${tpr}`\n  } else if (incPr) {\n    to = `<${tM}.${tm}.${+tp + 1}-0`\n  } else {\n    to = `<=${to}`\n  }\n\n  return `${from} ${to}`.trim()\n}\n\nconst testSet = (set, version, options) => {\n  for (let i = 0; i < set.length; i++) {\n    if (!set[i].test(version)) {\n      return false\n    }\n  }\n\n  if (version.prerelease.length && !options.includePrerelease) {\n    // Find the set of versions that are allowed to have prereleases\n    // For example, ^1.2.3-pr.1 desugars to >=1.2.3-pr.1 <2.0.0\n    // That should allow `1.2.3-pr.2` to pass.\n    // However, `1.2.4-alpha.notready` should NOT be allowed,\n    // even though it's within the range set by the comparators.\n    for (let i = 0; i < set.length; i++) {\n      debug(set[i].semver)\n      if (set[i].semver === Comparator.ANY) {\n        continue\n      }\n\n      if (set[i].semver.prerelease.length > 0) {\n        const allowed = set[i].semver\n        if (allowed.major === version.major &&\n            allowed.minor === version.minor &&\n            allowed.patch === version.patch) {\n          return true\n        }\n      }\n    }\n\n    // Version has a -pre, but it's not one of the ones we like.\n    return false\n  }\n\n  return true\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/classes/range.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/classes/semver.js":
/*!******************************************************************************!*\
  !*** ./node_modules/@ledgerhq/devices/node_modules/semver/classes/semver.js ***!
  \******************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst debug = __webpack_require__(/*! ../internal/debug */ \"(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/internal/debug.js\")\nconst { MAX_LENGTH, MAX_SAFE_INTEGER } = __webpack_require__(/*! ../internal/constants */ \"(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/internal/constants.js\")\nconst { safeRe: re, t } = __webpack_require__(/*! ../internal/re */ \"(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/internal/re.js\")\n\nconst parseOptions = __webpack_require__(/*! ../internal/parse-options */ \"(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/internal/parse-options.js\")\nconst { compareIdentifiers } = __webpack_require__(/*! ../internal/identifiers */ \"(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/internal/identifiers.js\")\nclass SemVer {\n  constructor (version, options) {\n    options = parseOptions(options)\n\n    if (version instanceof SemVer) {\n      if (version.loose === !!options.loose &&\n        version.includePrerelease === !!options.includePrerelease) {\n        return version\n      } else {\n        version = version.version\n      }\n    } else if (typeof version !== 'string') {\n      throw new TypeError(`Invalid version. Must be a string. Got type \"${typeof version}\".`)\n    }\n\n    if (version.length > MAX_LENGTH) {\n      throw new TypeError(\n        `version is longer than ${MAX_LENGTH} characters`\n      )\n    }\n\n    debug('SemVer', version, options)\n    this.options = options\n    this.loose = !!options.loose\n    // this isn't actually relevant for versions, but keep it so that we\n    // don't run into trouble passing this.options around.\n    this.includePrerelease = !!options.includePrerelease\n\n    const m = version.trim().match(options.loose ? re[t.LOOSE] : re[t.FULL])\n\n    if (!m) {\n      throw new TypeError(`Invalid Version: ${version}`)\n    }\n\n    this.raw = version\n\n    // these are actually numbers\n    this.major = +m[1]\n    this.minor = +m[2]\n    this.patch = +m[3]\n\n    if (this.major > MAX_SAFE_INTEGER || this.major < 0) {\n      throw new TypeError('Invalid major version')\n    }\n\n    if (this.minor > MAX_SAFE_INTEGER || this.minor < 0) {\n      throw new TypeError('Invalid minor version')\n    }\n\n    if (this.patch > MAX_SAFE_INTEGER || this.patch < 0) {\n      throw new TypeError('Invalid patch version')\n    }\n\n    // numberify any prerelease numeric ids\n    if (!m[4]) {\n      this.prerelease = []\n    } else {\n      this.prerelease = m[4].split('.').map((id) => {\n        if (/^[0-9]+$/.test(id)) {\n          const num = +id\n          if (num >= 0 && num < MAX_SAFE_INTEGER) {\n            return num\n          }\n        }\n        return id\n      })\n    }\n\n    this.build = m[5] ? m[5].split('.') : []\n    this.format()\n  }\n\n  format () {\n    this.version = `${this.major}.${this.minor}.${this.patch}`\n    if (this.prerelease.length) {\n      this.version += `-${this.prerelease.join('.')}`\n    }\n    return this.version\n  }\n\n  toString () {\n    return this.version\n  }\n\n  compare (other) {\n    debug('SemVer.compare', this.version, this.options, other)\n    if (!(other instanceof SemVer)) {\n      if (typeof other === 'string' && other === this.version) {\n        return 0\n      }\n      other = new SemVer(other, this.options)\n    }\n\n    if (other.version === this.version) {\n      return 0\n    }\n\n    return this.compareMain(other) || this.comparePre(other)\n  }\n\n  compareMain (other) {\n    if (!(other instanceof SemVer)) {\n      other = new SemVer(other, this.options)\n    }\n\n    return (\n      compareIdentifiers(this.major, other.major) ||\n      compareIdentifiers(this.minor, other.minor) ||\n      compareIdentifiers(this.patch, other.patch)\n    )\n  }\n\n  comparePre (other) {\n    if (!(other instanceof SemVer)) {\n      other = new SemVer(other, this.options)\n    }\n\n    // NOT having a prerelease is > having one\n    if (this.prerelease.length && !other.prerelease.length) {\n      return -1\n    } else if (!this.prerelease.length && other.prerelease.length) {\n      return 1\n    } else if (!this.prerelease.length && !other.prerelease.length) {\n      return 0\n    }\n\n    let i = 0\n    do {\n      const a = this.prerelease[i]\n      const b = other.prerelease[i]\n      debug('prerelease compare', i, a, b)\n      if (a === undefined && b === undefined) {\n        return 0\n      } else if (b === undefined) {\n        return 1\n      } else if (a === undefined) {\n        return -1\n      } else if (a === b) {\n        continue\n      } else {\n        return compareIdentifiers(a, b)\n      }\n    } while (++i)\n  }\n\n  compareBuild (other) {\n    if (!(other instanceof SemVer)) {\n      other = new SemVer(other, this.options)\n    }\n\n    let i = 0\n    do {\n      const a = this.build[i]\n      const b = other.build[i]\n      debug('build compare', i, a, b)\n      if (a === undefined && b === undefined) {\n        return 0\n      } else if (b === undefined) {\n        return 1\n      } else if (a === undefined) {\n        return -1\n      } else if (a === b) {\n        continue\n      } else {\n        return compareIdentifiers(a, b)\n      }\n    } while (++i)\n  }\n\n  // preminor will bump the version up to the next minor release, and immediately\n  // down to pre-release. premajor and prepatch work the same way.\n  inc (release, identifier, identifierBase) {\n    if (release.startsWith('pre')) {\n      if (!identifier && identifierBase === false) {\n        throw new Error('invalid increment argument: identifier is empty')\n      }\n      // Avoid an invalid semver results\n      if (identifier) {\n        const match = `-${identifier}`.match(this.options.loose ? re[t.PRERELEASELOOSE] : re[t.PRERELEASE])\n        if (!match || match[1] !== identifier) {\n          throw new Error(`invalid identifier: ${identifier}`)\n        }\n      }\n    }\n\n    switch (release) {\n      case 'premajor':\n        this.prerelease.length = 0\n        this.patch = 0\n        this.minor = 0\n        this.major++\n        this.inc('pre', identifier, identifierBase)\n        break\n      case 'preminor':\n        this.prerelease.length = 0\n        this.patch = 0\n        this.minor++\n        this.inc('pre', identifier, identifierBase)\n        break\n      case 'prepatch':\n        // If this is already a prerelease, it will bump to the next version\n        // drop any prereleases that might already exist, since they are not\n        // relevant at this point.\n        this.prerelease.length = 0\n        this.inc('patch', identifier, identifierBase)\n        this.inc('pre', identifier, identifierBase)\n        break\n      // If the input is a non-prerelease version, this acts the same as\n      // prepatch.\n      case 'prerelease':\n        if (this.prerelease.length === 0) {\n          this.inc('patch', identifier, identifierBase)\n        }\n        this.inc('pre', identifier, identifierBase)\n        break\n      case 'release':\n        if (this.prerelease.length === 0) {\n          throw new Error(`version ${this.raw} is not a prerelease`)\n        }\n        this.prerelease.length = 0\n        break\n\n      case 'major':\n        // If this is a pre-major version, bump up to the same major version.\n        // Otherwise increment major.\n        // 1.0.0-5 bumps to 1.0.0\n        // 1.1.0 bumps to 2.0.0\n        if (\n          this.minor !== 0 ||\n          this.patch !== 0 ||\n          this.prerelease.length === 0\n        ) {\n          this.major++\n        }\n        this.minor = 0\n        this.patch = 0\n        this.prerelease = []\n        break\n      case 'minor':\n        // If this is a pre-minor version, bump up to the same minor version.\n        // Otherwise increment minor.\n        // 1.2.0-5 bumps to 1.2.0\n        // 1.2.1 bumps to 1.3.0\n        if (this.patch !== 0 || this.prerelease.length === 0) {\n          this.minor++\n        }\n        this.patch = 0\n        this.prerelease = []\n        break\n      case 'patch':\n        // If this is not a pre-release version, it will increment the patch.\n        // If it is a pre-release it will bump up to the same patch version.\n        // 1.2.0-5 patches to 1.2.0\n        // 1.2.0 patches to 1.2.1\n        if (this.prerelease.length === 0) {\n          this.patch++\n        }\n        this.prerelease = []\n        break\n      // This probably shouldn't be used publicly.\n      // 1.0.0 'pre' would become 1.0.0-0 which is the wrong direction.\n      case 'pre': {\n        const base = Number(identifierBase) ? 1 : 0\n\n        if (this.prerelease.length === 0) {\n          this.prerelease = [base]\n        } else {\n          let i = this.prerelease.length\n          while (--i >= 0) {\n            if (typeof this.prerelease[i] === 'number') {\n              this.prerelease[i]++\n              i = -2\n            }\n          }\n          if (i === -1) {\n            // didn't increment anything\n            if (identifier === this.prerelease.join('.') && identifierBase === false) {\n              throw new Error('invalid increment argument: identifier already exists')\n            }\n            this.prerelease.push(base)\n          }\n        }\n        if (identifier) {\n          // 1.2.0-beta.1 bumps to 1.2.0-beta.2,\n          // 1.2.0-beta.fooblz or 1.2.0-beta bumps to 1.2.0-beta.0\n          let prerelease = [identifier, base]\n          if (identifierBase === false) {\n            prerelease = [identifier]\n          }\n          if (compareIdentifiers(this.prerelease[0], identifier) === 0) {\n            if (isNaN(this.prerelease[1])) {\n              this.prerelease = prerelease\n            }\n          } else {\n            this.prerelease = prerelease\n          }\n        }\n        break\n      }\n      default:\n        throw new Error(`invalid increment argument: ${release}`)\n    }\n    this.raw = this.format()\n    if (this.build.length) {\n      this.raw += `+${this.build.join('.')}`\n    }\n    return this\n  }\n}\n\nmodule.exports = SemVer\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQGxlZGdlcmhxL2RldmljZXMvbm9kZV9tb2R1bGVzL3NlbXZlci9jbGFzc2VzL3NlbXZlci5qcyIsIm1hcHBpbmdzIjoiQUFBWTs7QUFFWixjQUFjLG1CQUFPLENBQUMsdUdBQW1CO0FBQ3pDLFFBQVEsK0JBQStCLEVBQUUsbUJBQU8sQ0FBQywrR0FBdUI7QUFDeEUsUUFBUSxnQkFBZ0IsRUFBRSxtQkFBTyxDQUFDLGlHQUFnQjs7QUFFbEQscUJBQXFCLG1CQUFPLENBQUMsdUhBQTJCO0FBQ3hELFFBQVEscUJBQXFCLEVBQUUsbUJBQU8sQ0FBQyxtSEFBeUI7QUFDaEU7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsUUFBUTtBQUNSO0FBQ0E7QUFDQSxNQUFNO0FBQ04sMEVBQTBFLGVBQWU7QUFDekY7O0FBRUE7QUFDQTtBQUNBLGtDQUFrQyxZQUFZO0FBQzlDO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBOztBQUVBO0FBQ0EsOENBQThDLFFBQVE7QUFDdEQ7O0FBRUE7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsTUFBTTtBQUNOO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0Esc0JBQXNCLFdBQVcsR0FBRyxXQUFXLEdBQUcsV0FBVztBQUM3RDtBQUNBLDBCQUEwQiwwQkFBMEI7QUFDcEQ7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxNQUFNO0FBQ047QUFDQSxNQUFNO0FBQ047QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVE7QUFDUjtBQUNBLFFBQVE7QUFDUjtBQUNBLFFBQVE7QUFDUjtBQUNBLFFBQVE7QUFDUjtBQUNBO0FBQ0EsTUFBTTtBQUNOOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsUUFBUTtBQUNSO0FBQ0EsUUFBUTtBQUNSO0FBQ0EsUUFBUTtBQUNSO0FBQ0EsUUFBUTtBQUNSO0FBQ0E7QUFDQSxNQUFNO0FBQ047O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMEJBQTBCLFdBQVc7QUFDckM7QUFDQSxpREFBaUQsV0FBVztBQUM1RDtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHFDQUFxQyxVQUFVO0FBQy9DO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQSxVQUFVO0FBQ1Y7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFlBQVk7QUFDWjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx1REFBdUQsUUFBUTtBQUMvRDtBQUNBO0FBQ0E7QUFDQSxzQkFBc0IscUJBQXFCO0FBQzNDO0FBQ0E7QUFDQTtBQUNBOztBQUVBIiwic291cmNlcyI6WyIvVXNlcnMveHltL2NvZGUvdGJvb2svb250b25fY2Mvb25pb24tbGF1bmNoL2Zyb250ZW5kL25vZGVfbW9kdWxlcy9AbGVkZ2VyaHEvZGV2aWNlcy9ub2RlX21vZHVsZXMvc2VtdmVyL2NsYXNzZXMvc2VtdmVyLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0J1xuXG5jb25zdCBkZWJ1ZyA9IHJlcXVpcmUoJy4uL2ludGVybmFsL2RlYnVnJylcbmNvbnN0IHsgTUFYX0xFTkdUSCwgTUFYX1NBRkVfSU5URUdFUiB9ID0gcmVxdWlyZSgnLi4vaW50ZXJuYWwvY29uc3RhbnRzJylcbmNvbnN0IHsgc2FmZVJlOiByZSwgdCB9ID0gcmVxdWlyZSgnLi4vaW50ZXJuYWwvcmUnKVxuXG5jb25zdCBwYXJzZU9wdGlvbnMgPSByZXF1aXJlKCcuLi9pbnRlcm5hbC9wYXJzZS1vcHRpb25zJylcbmNvbnN0IHsgY29tcGFyZUlkZW50aWZpZXJzIH0gPSByZXF1aXJlKCcuLi9pbnRlcm5hbC9pZGVudGlmaWVycycpXG5jbGFzcyBTZW1WZXIge1xuICBjb25zdHJ1Y3RvciAodmVyc2lvbiwgb3B0aW9ucykge1xuICAgIG9wdGlvbnMgPSBwYXJzZU9wdGlvbnMob3B0aW9ucylcblxuICAgIGlmICh2ZXJzaW9uIGluc3RhbmNlb2YgU2VtVmVyKSB7XG4gICAgICBpZiAodmVyc2lvbi5sb29zZSA9PT0gISFvcHRpb25zLmxvb3NlICYmXG4gICAgICAgIHZlcnNpb24uaW5jbHVkZVByZXJlbGVhc2UgPT09ICEhb3B0aW9ucy5pbmNsdWRlUHJlcmVsZWFzZSkge1xuICAgICAgICByZXR1cm4gdmVyc2lvblxuICAgICAgfSBlbHNlIHtcbiAgICAgICAgdmVyc2lvbiA9IHZlcnNpb24udmVyc2lvblxuICAgICAgfVxuICAgIH0gZWxzZSBpZiAodHlwZW9mIHZlcnNpb24gIT09ICdzdHJpbmcnKSB7XG4gICAgICB0aHJvdyBuZXcgVHlwZUVycm9yKGBJbnZhbGlkIHZlcnNpb24uIE11c3QgYmUgYSBzdHJpbmcuIEdvdCB0eXBlIFwiJHt0eXBlb2YgdmVyc2lvbn1cIi5gKVxuICAgIH1cblxuICAgIGlmICh2ZXJzaW9uLmxlbmd0aCA+IE1BWF9MRU5HVEgpIHtcbiAgICAgIHRocm93IG5ldyBUeXBlRXJyb3IoXG4gICAgICAgIGB2ZXJzaW9uIGlzIGxvbmdlciB0aGFuICR7TUFYX0xFTkdUSH0gY2hhcmFjdGVyc2BcbiAgICAgIClcbiAgICB9XG5cbiAgICBkZWJ1ZygnU2VtVmVyJywgdmVyc2lvbiwgb3B0aW9ucylcbiAgICB0aGlzLm9wdGlvbnMgPSBvcHRpb25zXG4gICAgdGhpcy5sb29zZSA9ICEhb3B0aW9ucy5sb29zZVxuICAgIC8vIHRoaXMgaXNuJ3QgYWN0dWFsbHkgcmVsZXZhbnQgZm9yIHZlcnNpb25zLCBidXQga2VlcCBpdCBzbyB0aGF0IHdlXG4gICAgLy8gZG9uJ3QgcnVuIGludG8gdHJvdWJsZSBwYXNzaW5nIHRoaXMub3B0aW9ucyBhcm91bmQuXG4gICAgdGhpcy5pbmNsdWRlUHJlcmVsZWFzZSA9ICEhb3B0aW9ucy5pbmNsdWRlUHJlcmVsZWFzZVxuXG4gICAgY29uc3QgbSA9IHZlcnNpb24udHJpbSgpLm1hdGNoKG9wdGlvbnMubG9vc2UgPyByZVt0LkxPT1NFXSA6IHJlW3QuRlVMTF0pXG5cbiAgICBpZiAoIW0pIHtcbiAgICAgIHRocm93IG5ldyBUeXBlRXJyb3IoYEludmFsaWQgVmVyc2lvbjogJHt2ZXJzaW9ufWApXG4gICAgfVxuXG4gICAgdGhpcy5yYXcgPSB2ZXJzaW9uXG5cbiAgICAvLyB0aGVzZSBhcmUgYWN0dWFsbHkgbnVtYmVyc1xuICAgIHRoaXMubWFqb3IgPSArbVsxXVxuICAgIHRoaXMubWlub3IgPSArbVsyXVxuICAgIHRoaXMucGF0Y2ggPSArbVszXVxuXG4gICAgaWYgKHRoaXMubWFqb3IgPiBNQVhfU0FGRV9JTlRFR0VSIHx8IHRoaXMubWFqb3IgPCAwKSB7XG4gICAgICB0aHJvdyBuZXcgVHlwZUVycm9yKCdJbnZhbGlkIG1ham9yIHZlcnNpb24nKVxuICAgIH1cblxuICAgIGlmICh0aGlzLm1pbm9yID4gTUFYX1NBRkVfSU5URUdFUiB8fCB0aGlzLm1pbm9yIDwgMCkge1xuICAgICAgdGhyb3cgbmV3IFR5cGVFcnJvcignSW52YWxpZCBtaW5vciB2ZXJzaW9uJylcbiAgICB9XG5cbiAgICBpZiAodGhpcy5wYXRjaCA+IE1BWF9TQUZFX0lOVEVHRVIgfHwgdGhpcy5wYXRjaCA8IDApIHtcbiAgICAgIHRocm93IG5ldyBUeXBlRXJyb3IoJ0ludmFsaWQgcGF0Y2ggdmVyc2lvbicpXG4gICAgfVxuXG4gICAgLy8gbnVtYmVyaWZ5IGFueSBwcmVyZWxlYXNlIG51bWVyaWMgaWRzXG4gICAgaWYgKCFtWzRdKSB7XG4gICAgICB0aGlzLnByZXJlbGVhc2UgPSBbXVxuICAgIH0gZWxzZSB7XG4gICAgICB0aGlzLnByZXJlbGVhc2UgPSBtWzRdLnNwbGl0KCcuJykubWFwKChpZCkgPT4ge1xuICAgICAgICBpZiAoL15bMC05XSskLy50ZXN0KGlkKSkge1xuICAgICAgICAgIGNvbnN0IG51bSA9ICtpZFxuICAgICAgICAgIGlmIChudW0gPj0gMCAmJiBudW0gPCBNQVhfU0FGRV9JTlRFR0VSKSB7XG4gICAgICAgICAgICByZXR1cm4gbnVtXG4gICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICAgIHJldHVybiBpZFxuICAgICAgfSlcbiAgICB9XG5cbiAgICB0aGlzLmJ1aWxkID0gbVs1XSA/IG1bNV0uc3BsaXQoJy4nKSA6IFtdXG4gICAgdGhpcy5mb3JtYXQoKVxuICB9XG5cbiAgZm9ybWF0ICgpIHtcbiAgICB0aGlzLnZlcnNpb24gPSBgJHt0aGlzLm1ham9yfS4ke3RoaXMubWlub3J9LiR7dGhpcy5wYXRjaH1gXG4gICAgaWYgKHRoaXMucHJlcmVsZWFzZS5sZW5ndGgpIHtcbiAgICAgIHRoaXMudmVyc2lvbiArPSBgLSR7dGhpcy5wcmVyZWxlYXNlLmpvaW4oJy4nKX1gXG4gICAgfVxuICAgIHJldHVybiB0aGlzLnZlcnNpb25cbiAgfVxuXG4gIHRvU3RyaW5nICgpIHtcbiAgICByZXR1cm4gdGhpcy52ZXJzaW9uXG4gIH1cblxuICBjb21wYXJlIChvdGhlcikge1xuICAgIGRlYnVnKCdTZW1WZXIuY29tcGFyZScsIHRoaXMudmVyc2lvbiwgdGhpcy5vcHRpb25zLCBvdGhlcilcbiAgICBpZiAoIShvdGhlciBpbnN0YW5jZW9mIFNlbVZlcikpIHtcbiAgICAgIGlmICh0eXBlb2Ygb3RoZXIgPT09ICdzdHJpbmcnICYmIG90aGVyID09PSB0aGlzLnZlcnNpb24pIHtcbiAgICAgICAgcmV0dXJuIDBcbiAgICAgIH1cbiAgICAgIG90aGVyID0gbmV3IFNlbVZlcihvdGhlciwgdGhpcy5vcHRpb25zKVxuICAgIH1cblxuICAgIGlmIChvdGhlci52ZXJzaW9uID09PSB0aGlzLnZlcnNpb24pIHtcbiAgICAgIHJldHVybiAwXG4gICAgfVxuXG4gICAgcmV0dXJuIHRoaXMuY29tcGFyZU1haW4ob3RoZXIpIHx8IHRoaXMuY29tcGFyZVByZShvdGhlcilcbiAgfVxuXG4gIGNvbXBhcmVNYWluIChvdGhlcikge1xuICAgIGlmICghKG90aGVyIGluc3RhbmNlb2YgU2VtVmVyKSkge1xuICAgICAgb3RoZXIgPSBuZXcgU2VtVmVyKG90aGVyLCB0aGlzLm9wdGlvbnMpXG4gICAgfVxuXG4gICAgcmV0dXJuIChcbiAgICAgIGNvbXBhcmVJZGVudGlmaWVycyh0aGlzLm1ham9yLCBvdGhlci5tYWpvcikgfHxcbiAgICAgIGNvbXBhcmVJZGVudGlmaWVycyh0aGlzLm1pbm9yLCBvdGhlci5taW5vcikgfHxcbiAgICAgIGNvbXBhcmVJZGVudGlmaWVycyh0aGlzLnBhdGNoLCBvdGhlci5wYXRjaClcbiAgICApXG4gIH1cblxuICBjb21wYXJlUHJlIChvdGhlcikge1xuICAgIGlmICghKG90aGVyIGluc3RhbmNlb2YgU2VtVmVyKSkge1xuICAgICAgb3RoZXIgPSBuZXcgU2VtVmVyKG90aGVyLCB0aGlzLm9wdGlvbnMpXG4gICAgfVxuXG4gICAgLy8gTk9UIGhhdmluZyBhIHByZXJlbGVhc2UgaXMgPiBoYXZpbmcgb25lXG4gICAgaWYgKHRoaXMucHJlcmVsZWFzZS5sZW5ndGggJiYgIW90aGVyLnByZXJlbGVhc2UubGVuZ3RoKSB7XG4gICAgICByZXR1cm4gLTFcbiAgICB9IGVsc2UgaWYgKCF0aGlzLnByZXJlbGVhc2UubGVuZ3RoICYmIG90aGVyLnByZXJlbGVhc2UubGVuZ3RoKSB7XG4gICAgICByZXR1cm4gMVxuICAgIH0gZWxzZSBpZiAoIXRoaXMucHJlcmVsZWFzZS5sZW5ndGggJiYgIW90aGVyLnByZXJlbGVhc2UubGVuZ3RoKSB7XG4gICAgICByZXR1cm4gMFxuICAgIH1cblxuICAgIGxldCBpID0gMFxuICAgIGRvIHtcbiAgICAgIGNvbnN0IGEgPSB0aGlzLnByZXJlbGVhc2VbaV1cbiAgICAgIGNvbnN0IGIgPSBvdGhlci5wcmVyZWxlYXNlW2ldXG4gICAgICBkZWJ1ZygncHJlcmVsZWFzZSBjb21wYXJlJywgaSwgYSwgYilcbiAgICAgIGlmIChhID09PSB1bmRlZmluZWQgJiYgYiA9PT0gdW5kZWZpbmVkKSB7XG4gICAgICAgIHJldHVybiAwXG4gICAgICB9IGVsc2UgaWYgKGIgPT09IHVuZGVmaW5lZCkge1xuICAgICAgICByZXR1cm4gMVxuICAgICAgfSBlbHNlIGlmIChhID09PSB1bmRlZmluZWQpIHtcbiAgICAgICAgcmV0dXJuIC0xXG4gICAgICB9IGVsc2UgaWYgKGEgPT09IGIpIHtcbiAgICAgICAgY29udGludWVcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIHJldHVybiBjb21wYXJlSWRlbnRpZmllcnMoYSwgYilcbiAgICAgIH1cbiAgICB9IHdoaWxlICgrK2kpXG4gIH1cblxuICBjb21wYXJlQnVpbGQgKG90aGVyKSB7XG4gICAgaWYgKCEob3RoZXIgaW5zdGFuY2VvZiBTZW1WZXIpKSB7XG4gICAgICBvdGhlciA9IG5ldyBTZW1WZXIob3RoZXIsIHRoaXMub3B0aW9ucylcbiAgICB9XG5cbiAgICBsZXQgaSA9IDBcbiAgICBkbyB7XG4gICAgICBjb25zdCBhID0gdGhpcy5idWlsZFtpXVxuICAgICAgY29uc3QgYiA9IG90aGVyLmJ1aWxkW2ldXG4gICAgICBkZWJ1ZygnYnVpbGQgY29tcGFyZScsIGksIGEsIGIpXG4gICAgICBpZiAoYSA9PT0gdW5kZWZpbmVkICYmIGIgPT09IHVuZGVmaW5lZCkge1xuICAgICAgICByZXR1cm4gMFxuICAgICAgfSBlbHNlIGlmIChiID09PSB1bmRlZmluZWQpIHtcbiAgICAgICAgcmV0dXJuIDFcbiAgICAgIH0gZWxzZSBpZiAoYSA9PT0gdW5kZWZpbmVkKSB7XG4gICAgICAgIHJldHVybiAtMVxuICAgICAgfSBlbHNlIGlmIChhID09PSBiKSB7XG4gICAgICAgIGNvbnRpbnVlXG4gICAgICB9IGVsc2Uge1xuICAgICAgICByZXR1cm4gY29tcGFyZUlkZW50aWZpZXJzKGEsIGIpXG4gICAgICB9XG4gICAgfSB3aGlsZSAoKytpKVxuICB9XG5cbiAgLy8gcHJlbWlub3Igd2lsbCBidW1wIHRoZSB2ZXJzaW9uIHVwIHRvIHRoZSBuZXh0IG1pbm9yIHJlbGVhc2UsIGFuZCBpbW1lZGlhdGVseVxuICAvLyBkb3duIHRvIHByZS1yZWxlYXNlLiBwcmVtYWpvciBhbmQgcHJlcGF0Y2ggd29yayB0aGUgc2FtZSB3YXkuXG4gIGluYyAocmVsZWFzZSwgaWRlbnRpZmllciwgaWRlbnRpZmllckJhc2UpIHtcbiAgICBpZiAocmVsZWFzZS5zdGFydHNXaXRoKCdwcmUnKSkge1xuICAgICAgaWYgKCFpZGVudGlmaWVyICYmIGlkZW50aWZpZXJCYXNlID09PSBmYWxzZSkge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ2ludmFsaWQgaW5jcmVtZW50IGFyZ3VtZW50OiBpZGVudGlmaWVyIGlzIGVtcHR5JylcbiAgICAgIH1cbiAgICAgIC8vIEF2b2lkIGFuIGludmFsaWQgc2VtdmVyIHJlc3VsdHNcbiAgICAgIGlmIChpZGVudGlmaWVyKSB7XG4gICAgICAgIGNvbnN0IG1hdGNoID0gYC0ke2lkZW50aWZpZXJ9YC5tYXRjaCh0aGlzLm9wdGlvbnMubG9vc2UgPyByZVt0LlBSRVJFTEVBU0VMT09TRV0gOiByZVt0LlBSRVJFTEVBU0VdKVxuICAgICAgICBpZiAoIW1hdGNoIHx8IG1hdGNoWzFdICE9PSBpZGVudGlmaWVyKSB7XG4gICAgICAgICAgdGhyb3cgbmV3IEVycm9yKGBpbnZhbGlkIGlkZW50aWZpZXI6ICR7aWRlbnRpZmllcn1gKVxuICAgICAgICB9XG4gICAgICB9XG4gICAgfVxuXG4gICAgc3dpdGNoIChyZWxlYXNlKSB7XG4gICAgICBjYXNlICdwcmVtYWpvcic6XG4gICAgICAgIHRoaXMucHJlcmVsZWFzZS5sZW5ndGggPSAwXG4gICAgICAgIHRoaXMucGF0Y2ggPSAwXG4gICAgICAgIHRoaXMubWlub3IgPSAwXG4gICAgICAgIHRoaXMubWFqb3IrK1xuICAgICAgICB0aGlzLmluYygncHJlJywgaWRlbnRpZmllciwgaWRlbnRpZmllckJhc2UpXG4gICAgICAgIGJyZWFrXG4gICAgICBjYXNlICdwcmVtaW5vcic6XG4gICAgICAgIHRoaXMucHJlcmVsZWFzZS5sZW5ndGggPSAwXG4gICAgICAgIHRoaXMucGF0Y2ggPSAwXG4gICAgICAgIHRoaXMubWlub3IrK1xuICAgICAgICB0aGlzLmluYygncHJlJywgaWRlbnRpZmllciwgaWRlbnRpZmllckJhc2UpXG4gICAgICAgIGJyZWFrXG4gICAgICBjYXNlICdwcmVwYXRjaCc6XG4gICAgICAgIC8vIElmIHRoaXMgaXMgYWxyZWFkeSBhIHByZXJlbGVhc2UsIGl0IHdpbGwgYnVtcCB0byB0aGUgbmV4dCB2ZXJzaW9uXG4gICAgICAgIC8vIGRyb3AgYW55IHByZXJlbGVhc2VzIHRoYXQgbWlnaHQgYWxyZWFkeSBleGlzdCwgc2luY2UgdGhleSBhcmUgbm90XG4gICAgICAgIC8vIHJlbGV2YW50IGF0IHRoaXMgcG9pbnQuXG4gICAgICAgIHRoaXMucHJlcmVsZWFzZS5sZW5ndGggPSAwXG4gICAgICAgIHRoaXMuaW5jKCdwYXRjaCcsIGlkZW50aWZpZXIsIGlkZW50aWZpZXJCYXNlKVxuICAgICAgICB0aGlzLmluYygncHJlJywgaWRlbnRpZmllciwgaWRlbnRpZmllckJhc2UpXG4gICAgICAgIGJyZWFrXG4gICAgICAvLyBJZiB0aGUgaW5wdXQgaXMgYSBub24tcHJlcmVsZWFzZSB2ZXJzaW9uLCB0aGlzIGFjdHMgdGhlIHNhbWUgYXNcbiAgICAgIC8vIHByZXBhdGNoLlxuICAgICAgY2FzZSAncHJlcmVsZWFzZSc6XG4gICAgICAgIGlmICh0aGlzLnByZXJlbGVhc2UubGVuZ3RoID09PSAwKSB7XG4gICAgICAgICAgdGhpcy5pbmMoJ3BhdGNoJywgaWRlbnRpZmllciwgaWRlbnRpZmllckJhc2UpXG4gICAgICAgIH1cbiAgICAgICAgdGhpcy5pbmMoJ3ByZScsIGlkZW50aWZpZXIsIGlkZW50aWZpZXJCYXNlKVxuICAgICAgICBicmVha1xuICAgICAgY2FzZSAncmVsZWFzZSc6XG4gICAgICAgIGlmICh0aGlzLnByZXJlbGVhc2UubGVuZ3RoID09PSAwKSB7XG4gICAgICAgICAgdGhyb3cgbmV3IEVycm9yKGB2ZXJzaW9uICR7dGhpcy5yYXd9IGlzIG5vdCBhIHByZXJlbGVhc2VgKVxuICAgICAgICB9XG4gICAgICAgIHRoaXMucHJlcmVsZWFzZS5sZW5ndGggPSAwXG4gICAgICAgIGJyZWFrXG5cbiAgICAgIGNhc2UgJ21ham9yJzpcbiAgICAgICAgLy8gSWYgdGhpcyBpcyBhIHByZS1tYWpvciB2ZXJzaW9uLCBidW1wIHVwIHRvIHRoZSBzYW1lIG1ham9yIHZlcnNpb24uXG4gICAgICAgIC8vIE90aGVyd2lzZSBpbmNyZW1lbnQgbWFqb3IuXG4gICAgICAgIC8vIDEuMC4wLTUgYnVtcHMgdG8gMS4wLjBcbiAgICAgICAgLy8gMS4xLjAgYnVtcHMgdG8gMi4wLjBcbiAgICAgICAgaWYgKFxuICAgICAgICAgIHRoaXMubWlub3IgIT09IDAgfHxcbiAgICAgICAgICB0aGlzLnBhdGNoICE9PSAwIHx8XG4gICAgICAgICAgdGhpcy5wcmVyZWxlYXNlLmxlbmd0aCA9PT0gMFxuICAgICAgICApIHtcbiAgICAgICAgICB0aGlzLm1ham9yKytcbiAgICAgICAgfVxuICAgICAgICB0aGlzLm1pbm9yID0gMFxuICAgICAgICB0aGlzLnBhdGNoID0gMFxuICAgICAgICB0aGlzLnByZXJlbGVhc2UgPSBbXVxuICAgICAgICBicmVha1xuICAgICAgY2FzZSAnbWlub3InOlxuICAgICAgICAvLyBJZiB0aGlzIGlzIGEgcHJlLW1pbm9yIHZlcnNpb24sIGJ1bXAgdXAgdG8gdGhlIHNhbWUgbWlub3IgdmVyc2lvbi5cbiAgICAgICAgLy8gT3RoZXJ3aXNlIGluY3JlbWVudCBtaW5vci5cbiAgICAgICAgLy8gMS4yLjAtNSBidW1wcyB0byAxLjIuMFxuICAgICAgICAvLyAxLjIuMSBidW1wcyB0byAxLjMuMFxuICAgICAgICBpZiAodGhpcy5wYXRjaCAhPT0gMCB8fCB0aGlzLnByZXJlbGVhc2UubGVuZ3RoID09PSAwKSB7XG4gICAgICAgICAgdGhpcy5taW5vcisrXG4gICAgICAgIH1cbiAgICAgICAgdGhpcy5wYXRjaCA9IDBcbiAgICAgICAgdGhpcy5wcmVyZWxlYXNlID0gW11cbiAgICAgICAgYnJlYWtcbiAgICAgIGNhc2UgJ3BhdGNoJzpcbiAgICAgICAgLy8gSWYgdGhpcyBpcyBub3QgYSBwcmUtcmVsZWFzZSB2ZXJzaW9uLCBpdCB3aWxsIGluY3JlbWVudCB0aGUgcGF0Y2guXG4gICAgICAgIC8vIElmIGl0IGlzIGEgcHJlLXJlbGVhc2UgaXQgd2lsbCBidW1wIHVwIHRvIHRoZSBzYW1lIHBhdGNoIHZlcnNpb24uXG4gICAgICAgIC8vIDEuMi4wLTUgcGF0Y2hlcyB0byAxLjIuMFxuICAgICAgICAvLyAxLjIuMCBwYXRjaGVzIHRvIDEuMi4xXG4gICAgICAgIGlmICh0aGlzLnByZXJlbGVhc2UubGVuZ3RoID09PSAwKSB7XG4gICAgICAgICAgdGhpcy5wYXRjaCsrXG4gICAgICAgIH1cbiAgICAgICAgdGhpcy5wcmVyZWxlYXNlID0gW11cbiAgICAgICAgYnJlYWtcbiAgICAgIC8vIFRoaXMgcHJvYmFibHkgc2hvdWxkbid0IGJlIHVzZWQgcHVibGljbHkuXG4gICAgICAvLyAxLjAuMCAncHJlJyB3b3VsZCBiZWNvbWUgMS4wLjAtMCB3aGljaCBpcyB0aGUgd3JvbmcgZGlyZWN0aW9uLlxuICAgICAgY2FzZSAncHJlJzoge1xuICAgICAgICBjb25zdCBiYXNlID0gTnVtYmVyKGlkZW50aWZpZXJCYXNlKSA/IDEgOiAwXG5cbiAgICAgICAgaWYgKHRoaXMucHJlcmVsZWFzZS5sZW5ndGggPT09IDApIHtcbiAgICAgICAgICB0aGlzLnByZXJlbGVhc2UgPSBbYmFzZV1cbiAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICBsZXQgaSA9IHRoaXMucHJlcmVsZWFzZS5sZW5ndGhcbiAgICAgICAgICB3aGlsZSAoLS1pID49IDApIHtcbiAgICAgICAgICAgIGlmICh0eXBlb2YgdGhpcy5wcmVyZWxlYXNlW2ldID09PSAnbnVtYmVyJykge1xuICAgICAgICAgICAgICB0aGlzLnByZXJlbGVhc2VbaV0rK1xuICAgICAgICAgICAgICBpID0gLTJcbiAgICAgICAgICAgIH1cbiAgICAgICAgICB9XG4gICAgICAgICAgaWYgKGkgPT09IC0xKSB7XG4gICAgICAgICAgICAvLyBkaWRuJ3QgaW5jcmVtZW50IGFueXRoaW5nXG4gICAgICAgICAgICBpZiAoaWRlbnRpZmllciA9PT0gdGhpcy5wcmVyZWxlYXNlLmpvaW4oJy4nKSAmJiBpZGVudGlmaWVyQmFzZSA9PT0gZmFsc2UpIHtcbiAgICAgICAgICAgICAgdGhyb3cgbmV3IEVycm9yKCdpbnZhbGlkIGluY3JlbWVudCBhcmd1bWVudDogaWRlbnRpZmllciBhbHJlYWR5IGV4aXN0cycpXG4gICAgICAgICAgICB9XG4gICAgICAgICAgICB0aGlzLnByZXJlbGVhc2UucHVzaChiYXNlKVxuICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgICBpZiAoaWRlbnRpZmllcikge1xuICAgICAgICAgIC8vIDEuMi4wLWJldGEuMSBidW1wcyB0byAxLjIuMC1iZXRhLjIsXG4gICAgICAgICAgLy8gMS4yLjAtYmV0YS5mb29ibHogb3IgMS4yLjAtYmV0YSBidW1wcyB0byAxLjIuMC1iZXRhLjBcbiAgICAgICAgICBsZXQgcHJlcmVsZWFzZSA9IFtpZGVudGlmaWVyLCBiYXNlXVxuICAgICAgICAgIGlmIChpZGVudGlmaWVyQmFzZSA9PT0gZmFsc2UpIHtcbiAgICAgICAgICAgIHByZXJlbGVhc2UgPSBbaWRlbnRpZmllcl1cbiAgICAgICAgICB9XG4gICAgICAgICAgaWYgKGNvbXBhcmVJZGVudGlmaWVycyh0aGlzLnByZXJlbGVhc2VbMF0sIGlkZW50aWZpZXIpID09PSAwKSB7XG4gICAgICAgICAgICBpZiAoaXNOYU4odGhpcy5wcmVyZWxlYXNlWzFdKSkge1xuICAgICAgICAgICAgICB0aGlzLnByZXJlbGVhc2UgPSBwcmVyZWxlYXNlXG4gICAgICAgICAgICB9XG4gICAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgIHRoaXMucHJlcmVsZWFzZSA9IHByZXJlbGVhc2VcbiAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgICAgYnJlYWtcbiAgICAgIH1cbiAgICAgIGRlZmF1bHQ6XG4gICAgICAgIHRocm93IG5ldyBFcnJvcihgaW52YWxpZCBpbmNyZW1lbnQgYXJndW1lbnQ6ICR7cmVsZWFzZX1gKVxuICAgIH1cbiAgICB0aGlzLnJhdyA9IHRoaXMuZm9ybWF0KClcbiAgICBpZiAodGhpcy5idWlsZC5sZW5ndGgpIHtcbiAgICAgIHRoaXMucmF3ICs9IGArJHt0aGlzLmJ1aWxkLmpvaW4oJy4nKX1gXG4gICAgfVxuICAgIHJldHVybiB0aGlzXG4gIH1cbn1cblxubW9kdWxlLmV4cG9ydHMgPSBTZW1WZXJcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/classes/semver.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/functions/clean.js":
/*!*******************************************************************************!*\
  !*** ./node_modules/@ledgerhq/devices/node_modules/semver/functions/clean.js ***!
  \*******************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst parse = __webpack_require__(/*! ./parse */ \"(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/functions/parse.js\")\nconst clean = (version, options) => {\n  const s = parse(version.trim().replace(/^[=v]+/, ''), options)\n  return s ? s.version : null\n}\nmodule.exports = clean\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQGxlZGdlcmhxL2RldmljZXMvbm9kZV9tb2R1bGVzL3NlbXZlci9mdW5jdGlvbnMvY2xlYW4uanMiLCJtYXBwaW5ncyI6IkFBQVk7O0FBRVosY0FBYyxtQkFBTyxDQUFDLDhGQUFTO0FBQy9CO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIi9Vc2Vycy94eW0vY29kZS90Ym9vay9vbnRvbl9jYy9vbmlvbi1sYXVuY2gvZnJvbnRlbmQvbm9kZV9tb2R1bGVzL0BsZWRnZXJocS9kZXZpY2VzL25vZGVfbW9kdWxlcy9zZW12ZXIvZnVuY3Rpb25zL2NsZWFuLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0J1xuXG5jb25zdCBwYXJzZSA9IHJlcXVpcmUoJy4vcGFyc2UnKVxuY29uc3QgY2xlYW4gPSAodmVyc2lvbiwgb3B0aW9ucykgPT4ge1xuICBjb25zdCBzID0gcGFyc2UodmVyc2lvbi50cmltKCkucmVwbGFjZSgvXls9dl0rLywgJycpLCBvcHRpb25zKVxuICByZXR1cm4gcyA/IHMudmVyc2lvbiA6IG51bGxcbn1cbm1vZHVsZS5leHBvcnRzID0gY2xlYW5cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/functions/clean.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/functions/cmp.js":
/*!*****************************************************************************!*\
  !*** ./node_modules/@ledgerhq/devices/node_modules/semver/functions/cmp.js ***!
  \*****************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst eq = __webpack_require__(/*! ./eq */ \"(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/functions/eq.js\")\nconst neq = __webpack_require__(/*! ./neq */ \"(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/functions/neq.js\")\nconst gt = __webpack_require__(/*! ./gt */ \"(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/functions/gt.js\")\nconst gte = __webpack_require__(/*! ./gte */ \"(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/functions/gte.js\")\nconst lt = __webpack_require__(/*! ./lt */ \"(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/functions/lt.js\")\nconst lte = __webpack_require__(/*! ./lte */ \"(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/functions/lte.js\")\n\nconst cmp = (a, op, b, loose) => {\n  switch (op) {\n    case '===':\n      if (typeof a === 'object') {\n        a = a.version\n      }\n      if (typeof b === 'object') {\n        b = b.version\n      }\n      return a === b\n\n    case '!==':\n      if (typeof a === 'object') {\n        a = a.version\n      }\n      if (typeof b === 'object') {\n        b = b.version\n      }\n      return a !== b\n\n    case '':\n    case '=':\n    case '==':\n      return eq(a, b, loose)\n\n    case '!=':\n      return neq(a, b, loose)\n\n    case '>':\n      return gt(a, b, loose)\n\n    case '>=':\n      return gte(a, b, loose)\n\n    case '<':\n      return lt(a, b, loose)\n\n    case '<=':\n      return lte(a, b, loose)\n\n    default:\n      throw new TypeError(`Invalid operator: ${op}`)\n  }\n}\nmodule.exports = cmp\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/functions/cmp.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/functions/coerce.js":
/*!********************************************************************************!*\
  !*** ./node_modules/@ledgerhq/devices/node_modules/semver/functions/coerce.js ***!
  \********************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst SemVer = __webpack_require__(/*! ../classes/semver */ \"(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/classes/semver.js\")\nconst parse = __webpack_require__(/*! ./parse */ \"(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/functions/parse.js\")\nconst { safeRe: re, t } = __webpack_require__(/*! ../internal/re */ \"(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/internal/re.js\")\n\nconst coerce = (version, options) => {\n  if (version instanceof SemVer) {\n    return version\n  }\n\n  if (typeof version === 'number') {\n    version = String(version)\n  }\n\n  if (typeof version !== 'string') {\n    return null\n  }\n\n  options = options || {}\n\n  let match = null\n  if (!options.rtl) {\n    match = version.match(options.includePrerelease ? re[t.COERCEFULL] : re[t.COERCE])\n  } else {\n    // Find the right-most coercible string that does not share\n    // a terminus with a more left-ward coercible string.\n    // Eg, '1.2.3.4' wants to coerce '2.3.4', not '3.4' or '4'\n    // With includePrerelease option set, '1.2.3.4-rc' wants to coerce '2.3.4-rc', not '2.3.4'\n    //\n    // Walk through the string checking with a /g regexp\n    // Manually set the index so as to pick up overlapping matches.\n    // Stop when we get a match that ends at the string end, since no\n    // coercible string can be more right-ward without the same terminus.\n    const coerceRtlRegex = options.includePrerelease ? re[t.COERCERTLFULL] : re[t.COERCERTL]\n    let next\n    while ((next = coerceRtlRegex.exec(version)) &&\n        (!match || match.index + match[0].length !== version.length)\n    ) {\n      if (!match ||\n            next.index + next[0].length !== match.index + match[0].length) {\n        match = next\n      }\n      coerceRtlRegex.lastIndex = next.index + next[1].length + next[2].length\n    }\n    // leave it in a clean state\n    coerceRtlRegex.lastIndex = -1\n  }\n\n  if (match === null) {\n    return null\n  }\n\n  const major = match[2]\n  const minor = match[3] || '0'\n  const patch = match[4] || '0'\n  const prerelease = options.includePrerelease && match[5] ? `-${match[5]}` : ''\n  const build = options.includePrerelease && match[6] ? `+${match[6]}` : ''\n\n  return parse(`${major}.${minor}.${patch}${prerelease}${build}`, options)\n}\nmodule.exports = coerce\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/functions/coerce.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/functions/compare-build.js":
/*!***************************************************************************************!*\
  !*** ./node_modules/@ledgerhq/devices/node_modules/semver/functions/compare-build.js ***!
  \***************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst SemVer = __webpack_require__(/*! ../classes/semver */ \"(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/classes/semver.js\")\nconst compareBuild = (a, b, loose) => {\n  const versionA = new SemVer(a, loose)\n  const versionB = new SemVer(b, loose)\n  return versionA.compare(versionB) || versionA.compareBuild(versionB)\n}\nmodule.exports = compareBuild\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQGxlZGdlcmhxL2RldmljZXMvbm9kZV9tb2R1bGVzL3NlbXZlci9mdW5jdGlvbnMvY29tcGFyZS1idWlsZC5qcyIsIm1hcHBpbmdzIjoiQUFBWTs7QUFFWixlQUFlLG1CQUFPLENBQUMsdUdBQW1CO0FBQzFDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiL1VzZXJzL3h5bS9jb2RlL3Rib29rL29udG9uX2NjL29uaW9uLWxhdW5jaC9mcm9udGVuZC9ub2RlX21vZHVsZXMvQGxlZGdlcmhxL2RldmljZXMvbm9kZV9tb2R1bGVzL3NlbXZlci9mdW5jdGlvbnMvY29tcGFyZS1idWlsZC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCdcblxuY29uc3QgU2VtVmVyID0gcmVxdWlyZSgnLi4vY2xhc3Nlcy9zZW12ZXInKVxuY29uc3QgY29tcGFyZUJ1aWxkID0gKGEsIGIsIGxvb3NlKSA9PiB7XG4gIGNvbnN0IHZlcnNpb25BID0gbmV3IFNlbVZlcihhLCBsb29zZSlcbiAgY29uc3QgdmVyc2lvbkIgPSBuZXcgU2VtVmVyKGIsIGxvb3NlKVxuICByZXR1cm4gdmVyc2lvbkEuY29tcGFyZSh2ZXJzaW9uQikgfHwgdmVyc2lvbkEuY29tcGFyZUJ1aWxkKHZlcnNpb25CKVxufVxubW9kdWxlLmV4cG9ydHMgPSBjb21wYXJlQnVpbGRcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/functions/compare-build.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/functions/compare-loose.js":
/*!***************************************************************************************!*\
  !*** ./node_modules/@ledgerhq/devices/node_modules/semver/functions/compare-loose.js ***!
  \***************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst compare = __webpack_require__(/*! ./compare */ \"(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/functions/compare.js\")\nconst compareLoose = (a, b) => compare(a, b, true)\nmodule.exports = compareLoose\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQGxlZGdlcmhxL2RldmljZXMvbm9kZV9tb2R1bGVzL3NlbXZlci9mdW5jdGlvbnMvY29tcGFyZS1sb29zZS5qcyIsIm1hcHBpbmdzIjoiQUFBWTs7QUFFWixnQkFBZ0IsbUJBQU8sQ0FBQyxrR0FBVztBQUNuQztBQUNBIiwic291cmNlcyI6WyIvVXNlcnMveHltL2NvZGUvdGJvb2svb250b25fY2Mvb25pb24tbGF1bmNoL2Zyb250ZW5kL25vZGVfbW9kdWxlcy9AbGVkZ2VyaHEvZGV2aWNlcy9ub2RlX21vZHVsZXMvc2VtdmVyL2Z1bmN0aW9ucy9jb21wYXJlLWxvb3NlLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0J1xuXG5jb25zdCBjb21wYXJlID0gcmVxdWlyZSgnLi9jb21wYXJlJylcbmNvbnN0IGNvbXBhcmVMb29zZSA9IChhLCBiKSA9PiBjb21wYXJlKGEsIGIsIHRydWUpXG5tb2R1bGUuZXhwb3J0cyA9IGNvbXBhcmVMb29zZVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/functions/compare-loose.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/functions/compare.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/@ledgerhq/devices/node_modules/semver/functions/compare.js ***!
  \*********************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst SemVer = __webpack_require__(/*! ../classes/semver */ \"(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/classes/semver.js\")\nconst compare = (a, b, loose) =>\n  new SemVer(a, loose).compare(new SemVer(b, loose))\n\nmodule.exports = compare\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQGxlZGdlcmhxL2RldmljZXMvbm9kZV9tb2R1bGVzL3NlbXZlci9mdW5jdGlvbnMvY29tcGFyZS5qcyIsIm1hcHBpbmdzIjoiQUFBWTs7QUFFWixlQUFlLG1CQUFPLENBQUMsdUdBQW1CO0FBQzFDO0FBQ0E7O0FBRUEiLCJzb3VyY2VzIjpbIi9Vc2Vycy94eW0vY29kZS90Ym9vay9vbnRvbl9jYy9vbmlvbi1sYXVuY2gvZnJvbnRlbmQvbm9kZV9tb2R1bGVzL0BsZWRnZXJocS9kZXZpY2VzL25vZGVfbW9kdWxlcy9zZW12ZXIvZnVuY3Rpb25zL2NvbXBhcmUuanMiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnXG5cbmNvbnN0IFNlbVZlciA9IHJlcXVpcmUoJy4uL2NsYXNzZXMvc2VtdmVyJylcbmNvbnN0IGNvbXBhcmUgPSAoYSwgYiwgbG9vc2UpID0+XG4gIG5ldyBTZW1WZXIoYSwgbG9vc2UpLmNvbXBhcmUobmV3IFNlbVZlcihiLCBsb29zZSkpXG5cbm1vZHVsZS5leHBvcnRzID0gY29tcGFyZVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/functions/compare.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/functions/diff.js":
/*!******************************************************************************!*\
  !*** ./node_modules/@ledgerhq/devices/node_modules/semver/functions/diff.js ***!
  \******************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst parse = __webpack_require__(/*! ./parse.js */ \"(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/functions/parse.js\")\n\nconst diff = (version1, version2) => {\n  const v1 = parse(version1, null, true)\n  const v2 = parse(version2, null, true)\n  const comparison = v1.compare(v2)\n\n  if (comparison === 0) {\n    return null\n  }\n\n  const v1Higher = comparison > 0\n  const highVersion = v1Higher ? v1 : v2\n  const lowVersion = v1Higher ? v2 : v1\n  const highHasPre = !!highVersion.prerelease.length\n  const lowHasPre = !!lowVersion.prerelease.length\n\n  if (lowHasPre && !highHasPre) {\n    // Going from prerelease -> no prerelease requires some special casing\n\n    // If the low version has only a major, then it will always be a major\n    // Some examples:\n    // 1.0.0-1 -> 1.0.0\n    // 1.0.0-1 -> 1.1.1\n    // 1.0.0-1 -> 2.0.0\n    if (!lowVersion.patch && !lowVersion.minor) {\n      return 'major'\n    }\n\n    // If the main part has no difference\n    if (lowVersion.compareMain(highVersion) === 0) {\n      if (lowVersion.minor && !lowVersion.patch) {\n        return 'minor'\n      }\n      return 'patch'\n    }\n  }\n\n  // add the `pre` prefix if we are going to a prerelease version\n  const prefix = highHasPre ? 'pre' : ''\n\n  if (v1.major !== v2.major) {\n    return prefix + 'major'\n  }\n\n  if (v1.minor !== v2.minor) {\n    return prefix + 'minor'\n  }\n\n  if (v1.patch !== v2.patch) {\n    return prefix + 'patch'\n  }\n\n  // high and low are preleases\n  return 'prerelease'\n}\n\nmodule.exports = diff\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/functions/diff.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/functions/eq.js":
/*!****************************************************************************!*\
  !*** ./node_modules/@ledgerhq/devices/node_modules/semver/functions/eq.js ***!
  \****************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst compare = __webpack_require__(/*! ./compare */ \"(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/functions/compare.js\")\nconst eq = (a, b, loose) => compare(a, b, loose) === 0\nmodule.exports = eq\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQGxlZGdlcmhxL2RldmljZXMvbm9kZV9tb2R1bGVzL3NlbXZlci9mdW5jdGlvbnMvZXEuanMiLCJtYXBwaW5ncyI6IkFBQVk7O0FBRVosZ0JBQWdCLG1CQUFPLENBQUMsa0dBQVc7QUFDbkM7QUFDQSIsInNvdXJjZXMiOlsiL1VzZXJzL3h5bS9jb2RlL3Rib29rL29udG9uX2NjL29uaW9uLWxhdW5jaC9mcm9udGVuZC9ub2RlX21vZHVsZXMvQGxlZGdlcmhxL2RldmljZXMvbm9kZV9tb2R1bGVzL3NlbXZlci9mdW5jdGlvbnMvZXEuanMiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnXG5cbmNvbnN0IGNvbXBhcmUgPSByZXF1aXJlKCcuL2NvbXBhcmUnKVxuY29uc3QgZXEgPSAoYSwgYiwgbG9vc2UpID0+IGNvbXBhcmUoYSwgYiwgbG9vc2UpID09PSAwXG5tb2R1bGUuZXhwb3J0cyA9IGVxXG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/functions/eq.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/functions/gt.js":
/*!****************************************************************************!*\
  !*** ./node_modules/@ledgerhq/devices/node_modules/semver/functions/gt.js ***!
  \****************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst compare = __webpack_require__(/*! ./compare */ \"(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/functions/compare.js\")\nconst gt = (a, b, loose) => compare(a, b, loose) > 0\nmodule.exports = gt\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQGxlZGdlcmhxL2RldmljZXMvbm9kZV9tb2R1bGVzL3NlbXZlci9mdW5jdGlvbnMvZ3QuanMiLCJtYXBwaW5ncyI6IkFBQVk7O0FBRVosZ0JBQWdCLG1CQUFPLENBQUMsa0dBQVc7QUFDbkM7QUFDQSIsInNvdXJjZXMiOlsiL1VzZXJzL3h5bS9jb2RlL3Rib29rL29udG9uX2NjL29uaW9uLWxhdW5jaC9mcm9udGVuZC9ub2RlX21vZHVsZXMvQGxlZGdlcmhxL2RldmljZXMvbm9kZV9tb2R1bGVzL3NlbXZlci9mdW5jdGlvbnMvZ3QuanMiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnXG5cbmNvbnN0IGNvbXBhcmUgPSByZXF1aXJlKCcuL2NvbXBhcmUnKVxuY29uc3QgZ3QgPSAoYSwgYiwgbG9vc2UpID0+IGNvbXBhcmUoYSwgYiwgbG9vc2UpID4gMFxubW9kdWxlLmV4cG9ydHMgPSBndFxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/functions/gt.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/functions/gte.js":
/*!*****************************************************************************!*\
  !*** ./node_modules/@ledgerhq/devices/node_modules/semver/functions/gte.js ***!
  \*****************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst compare = __webpack_require__(/*! ./compare */ \"(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/functions/compare.js\")\nconst gte = (a, b, loose) => compare(a, b, loose) >= 0\nmodule.exports = gte\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQGxlZGdlcmhxL2RldmljZXMvbm9kZV9tb2R1bGVzL3NlbXZlci9mdW5jdGlvbnMvZ3RlLmpzIiwibWFwcGluZ3MiOiJBQUFZOztBQUVaLGdCQUFnQixtQkFBTyxDQUFDLGtHQUFXO0FBQ25DO0FBQ0EiLCJzb3VyY2VzIjpbIi9Vc2Vycy94eW0vY29kZS90Ym9vay9vbnRvbl9jYy9vbmlvbi1sYXVuY2gvZnJvbnRlbmQvbm9kZV9tb2R1bGVzL0BsZWRnZXJocS9kZXZpY2VzL25vZGVfbW9kdWxlcy9zZW12ZXIvZnVuY3Rpb25zL2d0ZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCdcblxuY29uc3QgY29tcGFyZSA9IHJlcXVpcmUoJy4vY29tcGFyZScpXG5jb25zdCBndGUgPSAoYSwgYiwgbG9vc2UpID0+IGNvbXBhcmUoYSwgYiwgbG9vc2UpID49IDBcbm1vZHVsZS5leHBvcnRzID0gZ3RlXG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/functions/gte.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/functions/inc.js":
/*!*****************************************************************************!*\
  !*** ./node_modules/@ledgerhq/devices/node_modules/semver/functions/inc.js ***!
  \*****************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst SemVer = __webpack_require__(/*! ../classes/semver */ \"(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/classes/semver.js\")\n\nconst inc = (version, release, options, identifier, identifierBase) => {\n  if (typeof (options) === 'string') {\n    identifierBase = identifier\n    identifier = options\n    options = undefined\n  }\n\n  try {\n    return new SemVer(\n      version instanceof SemVer ? version.version : version,\n      options\n    ).inc(release, identifier, identifierBase).version\n  } catch (er) {\n    return null\n  }\n}\nmodule.exports = inc\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQGxlZGdlcmhxL2RldmljZXMvbm9kZV9tb2R1bGVzL3NlbXZlci9mdW5jdGlvbnMvaW5jLmpzIiwibWFwcGluZ3MiOiJBQUFZOztBQUVaLGVBQWUsbUJBQU8sQ0FBQyx1R0FBbUI7O0FBRTFDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsSUFBSTtBQUNKO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyIvVXNlcnMveHltL2NvZGUvdGJvb2svb250b25fY2Mvb25pb24tbGF1bmNoL2Zyb250ZW5kL25vZGVfbW9kdWxlcy9AbGVkZ2VyaHEvZGV2aWNlcy9ub2RlX21vZHVsZXMvc2VtdmVyL2Z1bmN0aW9ucy9pbmMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnXG5cbmNvbnN0IFNlbVZlciA9IHJlcXVpcmUoJy4uL2NsYXNzZXMvc2VtdmVyJylcblxuY29uc3QgaW5jID0gKHZlcnNpb24sIHJlbGVhc2UsIG9wdGlvbnMsIGlkZW50aWZpZXIsIGlkZW50aWZpZXJCYXNlKSA9PiB7XG4gIGlmICh0eXBlb2YgKG9wdGlvbnMpID09PSAnc3RyaW5nJykge1xuICAgIGlkZW50aWZpZXJCYXNlID0gaWRlbnRpZmllclxuICAgIGlkZW50aWZpZXIgPSBvcHRpb25zXG4gICAgb3B0aW9ucyA9IHVuZGVmaW5lZFxuICB9XG5cbiAgdHJ5IHtcbiAgICByZXR1cm4gbmV3IFNlbVZlcihcbiAgICAgIHZlcnNpb24gaW5zdGFuY2VvZiBTZW1WZXIgPyB2ZXJzaW9uLnZlcnNpb24gOiB2ZXJzaW9uLFxuICAgICAgb3B0aW9uc1xuICAgICkuaW5jKHJlbGVhc2UsIGlkZW50aWZpZXIsIGlkZW50aWZpZXJCYXNlKS52ZXJzaW9uXG4gIH0gY2F0Y2ggKGVyKSB7XG4gICAgcmV0dXJuIG51bGxcbiAgfVxufVxubW9kdWxlLmV4cG9ydHMgPSBpbmNcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/functions/inc.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/functions/lt.js":
/*!****************************************************************************!*\
  !*** ./node_modules/@ledgerhq/devices/node_modules/semver/functions/lt.js ***!
  \****************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst compare = __webpack_require__(/*! ./compare */ \"(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/functions/compare.js\")\nconst lt = (a, b, loose) => compare(a, b, loose) < 0\nmodule.exports = lt\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQGxlZGdlcmhxL2RldmljZXMvbm9kZV9tb2R1bGVzL3NlbXZlci9mdW5jdGlvbnMvbHQuanMiLCJtYXBwaW5ncyI6IkFBQVk7O0FBRVosZ0JBQWdCLG1CQUFPLENBQUMsa0dBQVc7QUFDbkM7QUFDQSIsInNvdXJjZXMiOlsiL1VzZXJzL3h5bS9jb2RlL3Rib29rL29udG9uX2NjL29uaW9uLWxhdW5jaC9mcm9udGVuZC9ub2RlX21vZHVsZXMvQGxlZGdlcmhxL2RldmljZXMvbm9kZV9tb2R1bGVzL3NlbXZlci9mdW5jdGlvbnMvbHQuanMiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnXG5cbmNvbnN0IGNvbXBhcmUgPSByZXF1aXJlKCcuL2NvbXBhcmUnKVxuY29uc3QgbHQgPSAoYSwgYiwgbG9vc2UpID0+IGNvbXBhcmUoYSwgYiwgbG9vc2UpIDwgMFxubW9kdWxlLmV4cG9ydHMgPSBsdFxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/functions/lt.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/functions/lte.js":
/*!*****************************************************************************!*\
  !*** ./node_modules/@ledgerhq/devices/node_modules/semver/functions/lte.js ***!
  \*****************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst compare = __webpack_require__(/*! ./compare */ \"(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/functions/compare.js\")\nconst lte = (a, b, loose) => compare(a, b, loose) <= 0\nmodule.exports = lte\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQGxlZGdlcmhxL2RldmljZXMvbm9kZV9tb2R1bGVzL3NlbXZlci9mdW5jdGlvbnMvbHRlLmpzIiwibWFwcGluZ3MiOiJBQUFZOztBQUVaLGdCQUFnQixtQkFBTyxDQUFDLGtHQUFXO0FBQ25DO0FBQ0EiLCJzb3VyY2VzIjpbIi9Vc2Vycy94eW0vY29kZS90Ym9vay9vbnRvbl9jYy9vbmlvbi1sYXVuY2gvZnJvbnRlbmQvbm9kZV9tb2R1bGVzL0BsZWRnZXJocS9kZXZpY2VzL25vZGVfbW9kdWxlcy9zZW12ZXIvZnVuY3Rpb25zL2x0ZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCdcblxuY29uc3QgY29tcGFyZSA9IHJlcXVpcmUoJy4vY29tcGFyZScpXG5jb25zdCBsdGUgPSAoYSwgYiwgbG9vc2UpID0+IGNvbXBhcmUoYSwgYiwgbG9vc2UpIDw9IDBcbm1vZHVsZS5leHBvcnRzID0gbHRlXG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/functions/lte.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/functions/major.js":
/*!*******************************************************************************!*\
  !*** ./node_modules/@ledgerhq/devices/node_modules/semver/functions/major.js ***!
  \*******************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst SemVer = __webpack_require__(/*! ../classes/semver */ \"(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/classes/semver.js\")\nconst major = (a, loose) => new SemVer(a, loose).major\nmodule.exports = major\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQGxlZGdlcmhxL2RldmljZXMvbm9kZV9tb2R1bGVzL3NlbXZlci9mdW5jdGlvbnMvbWFqb3IuanMiLCJtYXBwaW5ncyI6IkFBQVk7O0FBRVosZUFBZSxtQkFBTyxDQUFDLHVHQUFtQjtBQUMxQztBQUNBIiwic291cmNlcyI6WyIvVXNlcnMveHltL2NvZGUvdGJvb2svb250b25fY2Mvb25pb24tbGF1bmNoL2Zyb250ZW5kL25vZGVfbW9kdWxlcy9AbGVkZ2VyaHEvZGV2aWNlcy9ub2RlX21vZHVsZXMvc2VtdmVyL2Z1bmN0aW9ucy9tYWpvci5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCdcblxuY29uc3QgU2VtVmVyID0gcmVxdWlyZSgnLi4vY2xhc3Nlcy9zZW12ZXInKVxuY29uc3QgbWFqb3IgPSAoYSwgbG9vc2UpID0+IG5ldyBTZW1WZXIoYSwgbG9vc2UpLm1ham9yXG5tb2R1bGUuZXhwb3J0cyA9IG1ham9yXG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/functions/major.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/functions/minor.js":
/*!*******************************************************************************!*\
  !*** ./node_modules/@ledgerhq/devices/node_modules/semver/functions/minor.js ***!
  \*******************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst SemVer = __webpack_require__(/*! ../classes/semver */ \"(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/classes/semver.js\")\nconst minor = (a, loose) => new SemVer(a, loose).minor\nmodule.exports = minor\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQGxlZGdlcmhxL2RldmljZXMvbm9kZV9tb2R1bGVzL3NlbXZlci9mdW5jdGlvbnMvbWlub3IuanMiLCJtYXBwaW5ncyI6IkFBQVk7O0FBRVosZUFBZSxtQkFBTyxDQUFDLHVHQUFtQjtBQUMxQztBQUNBIiwic291cmNlcyI6WyIvVXNlcnMveHltL2NvZGUvdGJvb2svb250b25fY2Mvb25pb24tbGF1bmNoL2Zyb250ZW5kL25vZGVfbW9kdWxlcy9AbGVkZ2VyaHEvZGV2aWNlcy9ub2RlX21vZHVsZXMvc2VtdmVyL2Z1bmN0aW9ucy9taW5vci5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCdcblxuY29uc3QgU2VtVmVyID0gcmVxdWlyZSgnLi4vY2xhc3Nlcy9zZW12ZXInKVxuY29uc3QgbWlub3IgPSAoYSwgbG9vc2UpID0+IG5ldyBTZW1WZXIoYSwgbG9vc2UpLm1pbm9yXG5tb2R1bGUuZXhwb3J0cyA9IG1pbm9yXG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/functions/minor.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/functions/neq.js":
/*!*****************************************************************************!*\
  !*** ./node_modules/@ledgerhq/devices/node_modules/semver/functions/neq.js ***!
  \*****************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst compare = __webpack_require__(/*! ./compare */ \"(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/functions/compare.js\")\nconst neq = (a, b, loose) => compare(a, b, loose) !== 0\nmodule.exports = neq\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQGxlZGdlcmhxL2RldmljZXMvbm9kZV9tb2R1bGVzL3NlbXZlci9mdW5jdGlvbnMvbmVxLmpzIiwibWFwcGluZ3MiOiJBQUFZOztBQUVaLGdCQUFnQixtQkFBTyxDQUFDLGtHQUFXO0FBQ25DO0FBQ0EiLCJzb3VyY2VzIjpbIi9Vc2Vycy94eW0vY29kZS90Ym9vay9vbnRvbl9jYy9vbmlvbi1sYXVuY2gvZnJvbnRlbmQvbm9kZV9tb2R1bGVzL0BsZWRnZXJocS9kZXZpY2VzL25vZGVfbW9kdWxlcy9zZW12ZXIvZnVuY3Rpb25zL25lcS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCdcblxuY29uc3QgY29tcGFyZSA9IHJlcXVpcmUoJy4vY29tcGFyZScpXG5jb25zdCBuZXEgPSAoYSwgYiwgbG9vc2UpID0+IGNvbXBhcmUoYSwgYiwgbG9vc2UpICE9PSAwXG5tb2R1bGUuZXhwb3J0cyA9IG5lcVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/functions/neq.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/functions/parse.js":
/*!*******************************************************************************!*\
  !*** ./node_modules/@ledgerhq/devices/node_modules/semver/functions/parse.js ***!
  \*******************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst SemVer = __webpack_require__(/*! ../classes/semver */ \"(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/classes/semver.js\")\nconst parse = (version, options, throwErrors = false) => {\n  if (version instanceof SemVer) {\n    return version\n  }\n  try {\n    return new SemVer(version, options)\n  } catch (er) {\n    if (!throwErrors) {\n      return null\n    }\n    throw er\n  }\n}\n\nmodule.exports = parse\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQGxlZGdlcmhxL2RldmljZXMvbm9kZV9tb2R1bGVzL3NlbXZlci9mdW5jdGlvbnMvcGFyc2UuanMiLCJtYXBwaW5ncyI6IkFBQVk7O0FBRVosZUFBZSxtQkFBTyxDQUFDLHVHQUFtQjtBQUMxQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxJQUFJO0FBQ0o7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBIiwic291cmNlcyI6WyIvVXNlcnMveHltL2NvZGUvdGJvb2svb250b25fY2Mvb25pb24tbGF1bmNoL2Zyb250ZW5kL25vZGVfbW9kdWxlcy9AbGVkZ2VyaHEvZGV2aWNlcy9ub2RlX21vZHVsZXMvc2VtdmVyL2Z1bmN0aW9ucy9wYXJzZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCdcblxuY29uc3QgU2VtVmVyID0gcmVxdWlyZSgnLi4vY2xhc3Nlcy9zZW12ZXInKVxuY29uc3QgcGFyc2UgPSAodmVyc2lvbiwgb3B0aW9ucywgdGhyb3dFcnJvcnMgPSBmYWxzZSkgPT4ge1xuICBpZiAodmVyc2lvbiBpbnN0YW5jZW9mIFNlbVZlcikge1xuICAgIHJldHVybiB2ZXJzaW9uXG4gIH1cbiAgdHJ5IHtcbiAgICByZXR1cm4gbmV3IFNlbVZlcih2ZXJzaW9uLCBvcHRpb25zKVxuICB9IGNhdGNoIChlcikge1xuICAgIGlmICghdGhyb3dFcnJvcnMpIHtcbiAgICAgIHJldHVybiBudWxsXG4gICAgfVxuICAgIHRocm93IGVyXG4gIH1cbn1cblxubW9kdWxlLmV4cG9ydHMgPSBwYXJzZVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/functions/parse.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/functions/patch.js":
/*!*******************************************************************************!*\
  !*** ./node_modules/@ledgerhq/devices/node_modules/semver/functions/patch.js ***!
  \*******************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst SemVer = __webpack_require__(/*! ../classes/semver */ \"(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/classes/semver.js\")\nconst patch = (a, loose) => new SemVer(a, loose).patch\nmodule.exports = patch\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQGxlZGdlcmhxL2RldmljZXMvbm9kZV9tb2R1bGVzL3NlbXZlci9mdW5jdGlvbnMvcGF0Y2guanMiLCJtYXBwaW5ncyI6IkFBQVk7O0FBRVosZUFBZSxtQkFBTyxDQUFDLHVHQUFtQjtBQUMxQztBQUNBIiwic291cmNlcyI6WyIvVXNlcnMveHltL2NvZGUvdGJvb2svb250b25fY2Mvb25pb24tbGF1bmNoL2Zyb250ZW5kL25vZGVfbW9kdWxlcy9AbGVkZ2VyaHEvZGV2aWNlcy9ub2RlX21vZHVsZXMvc2VtdmVyL2Z1bmN0aW9ucy9wYXRjaC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCdcblxuY29uc3QgU2VtVmVyID0gcmVxdWlyZSgnLi4vY2xhc3Nlcy9zZW12ZXInKVxuY29uc3QgcGF0Y2ggPSAoYSwgbG9vc2UpID0+IG5ldyBTZW1WZXIoYSwgbG9vc2UpLnBhdGNoXG5tb2R1bGUuZXhwb3J0cyA9IHBhdGNoXG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/functions/patch.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/functions/prerelease.js":
/*!************************************************************************************!*\
  !*** ./node_modules/@ledgerhq/devices/node_modules/semver/functions/prerelease.js ***!
  \************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst parse = __webpack_require__(/*! ./parse */ \"(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/functions/parse.js\")\nconst prerelease = (version, options) => {\n  const parsed = parse(version, options)\n  return (parsed && parsed.prerelease.length) ? parsed.prerelease : null\n}\nmodule.exports = prerelease\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQGxlZGdlcmhxL2RldmljZXMvbm9kZV9tb2R1bGVzL3NlbXZlci9mdW5jdGlvbnMvcHJlcmVsZWFzZS5qcyIsIm1hcHBpbmdzIjoiQUFBWTs7QUFFWixjQUFjLG1CQUFPLENBQUMsOEZBQVM7QUFDL0I7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiL1VzZXJzL3h5bS9jb2RlL3Rib29rL29udG9uX2NjL29uaW9uLWxhdW5jaC9mcm9udGVuZC9ub2RlX21vZHVsZXMvQGxlZGdlcmhxL2RldmljZXMvbm9kZV9tb2R1bGVzL3NlbXZlci9mdW5jdGlvbnMvcHJlcmVsZWFzZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCdcblxuY29uc3QgcGFyc2UgPSByZXF1aXJlKCcuL3BhcnNlJylcbmNvbnN0IHByZXJlbGVhc2UgPSAodmVyc2lvbiwgb3B0aW9ucykgPT4ge1xuICBjb25zdCBwYXJzZWQgPSBwYXJzZSh2ZXJzaW9uLCBvcHRpb25zKVxuICByZXR1cm4gKHBhcnNlZCAmJiBwYXJzZWQucHJlcmVsZWFzZS5sZW5ndGgpID8gcGFyc2VkLnByZXJlbGVhc2UgOiBudWxsXG59XG5tb2R1bGUuZXhwb3J0cyA9IHByZXJlbGVhc2VcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/functions/prerelease.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/functions/rcompare.js":
/*!**********************************************************************************!*\
  !*** ./node_modules/@ledgerhq/devices/node_modules/semver/functions/rcompare.js ***!
  \**********************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst compare = __webpack_require__(/*! ./compare */ \"(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/functions/compare.js\")\nconst rcompare = (a, b, loose) => compare(b, a, loose)\nmodule.exports = rcompare\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQGxlZGdlcmhxL2RldmljZXMvbm9kZV9tb2R1bGVzL3NlbXZlci9mdW5jdGlvbnMvcmNvbXBhcmUuanMiLCJtYXBwaW5ncyI6IkFBQVk7O0FBRVosZ0JBQWdCLG1CQUFPLENBQUMsa0dBQVc7QUFDbkM7QUFDQSIsInNvdXJjZXMiOlsiL1VzZXJzL3h5bS9jb2RlL3Rib29rL29udG9uX2NjL29uaW9uLWxhdW5jaC9mcm9udGVuZC9ub2RlX21vZHVsZXMvQGxlZGdlcmhxL2RldmljZXMvbm9kZV9tb2R1bGVzL3NlbXZlci9mdW5jdGlvbnMvcmNvbXBhcmUuanMiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnXG5cbmNvbnN0IGNvbXBhcmUgPSByZXF1aXJlKCcuL2NvbXBhcmUnKVxuY29uc3QgcmNvbXBhcmUgPSAoYSwgYiwgbG9vc2UpID0+IGNvbXBhcmUoYiwgYSwgbG9vc2UpXG5tb2R1bGUuZXhwb3J0cyA9IHJjb21wYXJlXG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/functions/rcompare.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/functions/rsort.js":
/*!*******************************************************************************!*\
  !*** ./node_modules/@ledgerhq/devices/node_modules/semver/functions/rsort.js ***!
  \*******************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst compareBuild = __webpack_require__(/*! ./compare-build */ \"(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/functions/compare-build.js\")\nconst rsort = (list, loose) => list.sort((a, b) => compareBuild(b, a, loose))\nmodule.exports = rsort\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQGxlZGdlcmhxL2RldmljZXMvbm9kZV9tb2R1bGVzL3NlbXZlci9mdW5jdGlvbnMvcnNvcnQuanMiLCJtYXBwaW5ncyI6IkFBQVk7O0FBRVoscUJBQXFCLG1CQUFPLENBQUMsOEdBQWlCO0FBQzlDO0FBQ0EiLCJzb3VyY2VzIjpbIi9Vc2Vycy94eW0vY29kZS90Ym9vay9vbnRvbl9jYy9vbmlvbi1sYXVuY2gvZnJvbnRlbmQvbm9kZV9tb2R1bGVzL0BsZWRnZXJocS9kZXZpY2VzL25vZGVfbW9kdWxlcy9zZW12ZXIvZnVuY3Rpb25zL3Jzb3J0LmpzIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0J1xuXG5jb25zdCBjb21wYXJlQnVpbGQgPSByZXF1aXJlKCcuL2NvbXBhcmUtYnVpbGQnKVxuY29uc3QgcnNvcnQgPSAobGlzdCwgbG9vc2UpID0+IGxpc3Quc29ydCgoYSwgYikgPT4gY29tcGFyZUJ1aWxkKGIsIGEsIGxvb3NlKSlcbm1vZHVsZS5leHBvcnRzID0gcnNvcnRcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/functions/rsort.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/functions/satisfies.js":
/*!***********************************************************************************!*\
  !*** ./node_modules/@ledgerhq/devices/node_modules/semver/functions/satisfies.js ***!
  \***********************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst Range = __webpack_require__(/*! ../classes/range */ \"(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/classes/range.js\")\nconst satisfies = (version, range, options) => {\n  try {\n    range = new Range(range, options)\n  } catch (er) {\n    return false\n  }\n  return range.test(version)\n}\nmodule.exports = satisfies\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQGxlZGdlcmhxL2RldmljZXMvbm9kZV9tb2R1bGVzL3NlbXZlci9mdW5jdGlvbnMvc2F0aXNmaWVzLmpzIiwibWFwcGluZ3MiOiJBQUFZOztBQUVaLGNBQWMsbUJBQU8sQ0FBQyxxR0FBa0I7QUFDeEM7QUFDQTtBQUNBO0FBQ0EsSUFBSTtBQUNKO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIi9Vc2Vycy94eW0vY29kZS90Ym9vay9vbnRvbl9jYy9vbmlvbi1sYXVuY2gvZnJvbnRlbmQvbm9kZV9tb2R1bGVzL0BsZWRnZXJocS9kZXZpY2VzL25vZGVfbW9kdWxlcy9zZW12ZXIvZnVuY3Rpb25zL3NhdGlzZmllcy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCdcblxuY29uc3QgUmFuZ2UgPSByZXF1aXJlKCcuLi9jbGFzc2VzL3JhbmdlJylcbmNvbnN0IHNhdGlzZmllcyA9ICh2ZXJzaW9uLCByYW5nZSwgb3B0aW9ucykgPT4ge1xuICB0cnkge1xuICAgIHJhbmdlID0gbmV3IFJhbmdlKHJhbmdlLCBvcHRpb25zKVxuICB9IGNhdGNoIChlcikge1xuICAgIHJldHVybiBmYWxzZVxuICB9XG4gIHJldHVybiByYW5nZS50ZXN0KHZlcnNpb24pXG59XG5tb2R1bGUuZXhwb3J0cyA9IHNhdGlzZmllc1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/functions/satisfies.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/functions/sort.js":
/*!******************************************************************************!*\
  !*** ./node_modules/@ledgerhq/devices/node_modules/semver/functions/sort.js ***!
  \******************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst compareBuild = __webpack_require__(/*! ./compare-build */ \"(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/functions/compare-build.js\")\nconst sort = (list, loose) => list.sort((a, b) => compareBuild(a, b, loose))\nmodule.exports = sort\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQGxlZGdlcmhxL2RldmljZXMvbm9kZV9tb2R1bGVzL3NlbXZlci9mdW5jdGlvbnMvc29ydC5qcyIsIm1hcHBpbmdzIjoiQUFBWTs7QUFFWixxQkFBcUIsbUJBQU8sQ0FBQyw4R0FBaUI7QUFDOUM7QUFDQSIsInNvdXJjZXMiOlsiL1VzZXJzL3h5bS9jb2RlL3Rib29rL29udG9uX2NjL29uaW9uLWxhdW5jaC9mcm9udGVuZC9ub2RlX21vZHVsZXMvQGxlZGdlcmhxL2RldmljZXMvbm9kZV9tb2R1bGVzL3NlbXZlci9mdW5jdGlvbnMvc29ydC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCdcblxuY29uc3QgY29tcGFyZUJ1aWxkID0gcmVxdWlyZSgnLi9jb21wYXJlLWJ1aWxkJylcbmNvbnN0IHNvcnQgPSAobGlzdCwgbG9vc2UpID0+IGxpc3Quc29ydCgoYSwgYikgPT4gY29tcGFyZUJ1aWxkKGEsIGIsIGxvb3NlKSlcbm1vZHVsZS5leHBvcnRzID0gc29ydFxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/functions/sort.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/functions/valid.js":
/*!*******************************************************************************!*\
  !*** ./node_modules/@ledgerhq/devices/node_modules/semver/functions/valid.js ***!
  \*******************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst parse = __webpack_require__(/*! ./parse */ \"(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/functions/parse.js\")\nconst valid = (version, options) => {\n  const v = parse(version, options)\n  return v ? v.version : null\n}\nmodule.exports = valid\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQGxlZGdlcmhxL2RldmljZXMvbm9kZV9tb2R1bGVzL3NlbXZlci9mdW5jdGlvbnMvdmFsaWQuanMiLCJtYXBwaW5ncyI6IkFBQVk7O0FBRVosY0FBYyxtQkFBTyxDQUFDLDhGQUFTO0FBQy9CO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIi9Vc2Vycy94eW0vY29kZS90Ym9vay9vbnRvbl9jYy9vbmlvbi1sYXVuY2gvZnJvbnRlbmQvbm9kZV9tb2R1bGVzL0BsZWRnZXJocS9kZXZpY2VzL25vZGVfbW9kdWxlcy9zZW12ZXIvZnVuY3Rpb25zL3ZhbGlkLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0J1xuXG5jb25zdCBwYXJzZSA9IHJlcXVpcmUoJy4vcGFyc2UnKVxuY29uc3QgdmFsaWQgPSAodmVyc2lvbiwgb3B0aW9ucykgPT4ge1xuICBjb25zdCB2ID0gcGFyc2UodmVyc2lvbiwgb3B0aW9ucylcbiAgcmV0dXJuIHYgPyB2LnZlcnNpb24gOiBudWxsXG59XG5tb2R1bGUuZXhwb3J0cyA9IHZhbGlkXG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/functions/valid.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/index.js":
/*!*********************************************************************!*\
  !*** ./node_modules/@ledgerhq/devices/node_modules/semver/index.js ***!
  \*********************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\n// just pre-load all the stuff that index.js lazily exports\nconst internalRe = __webpack_require__(/*! ./internal/re */ \"(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/internal/re.js\")\nconst constants = __webpack_require__(/*! ./internal/constants */ \"(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/internal/constants.js\")\nconst SemVer = __webpack_require__(/*! ./classes/semver */ \"(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/classes/semver.js\")\nconst identifiers = __webpack_require__(/*! ./internal/identifiers */ \"(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/internal/identifiers.js\")\nconst parse = __webpack_require__(/*! ./functions/parse */ \"(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/functions/parse.js\")\nconst valid = __webpack_require__(/*! ./functions/valid */ \"(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/functions/valid.js\")\nconst clean = __webpack_require__(/*! ./functions/clean */ \"(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/functions/clean.js\")\nconst inc = __webpack_require__(/*! ./functions/inc */ \"(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/functions/inc.js\")\nconst diff = __webpack_require__(/*! ./functions/diff */ \"(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/functions/diff.js\")\nconst major = __webpack_require__(/*! ./functions/major */ \"(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/functions/major.js\")\nconst minor = __webpack_require__(/*! ./functions/minor */ \"(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/functions/minor.js\")\nconst patch = __webpack_require__(/*! ./functions/patch */ \"(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/functions/patch.js\")\nconst prerelease = __webpack_require__(/*! ./functions/prerelease */ \"(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/functions/prerelease.js\")\nconst compare = __webpack_require__(/*! ./functions/compare */ \"(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/functions/compare.js\")\nconst rcompare = __webpack_require__(/*! ./functions/rcompare */ \"(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/functions/rcompare.js\")\nconst compareLoose = __webpack_require__(/*! ./functions/compare-loose */ \"(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/functions/compare-loose.js\")\nconst compareBuild = __webpack_require__(/*! ./functions/compare-build */ \"(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/functions/compare-build.js\")\nconst sort = __webpack_require__(/*! ./functions/sort */ \"(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/functions/sort.js\")\nconst rsort = __webpack_require__(/*! ./functions/rsort */ \"(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/functions/rsort.js\")\nconst gt = __webpack_require__(/*! ./functions/gt */ \"(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/functions/gt.js\")\nconst lt = __webpack_require__(/*! ./functions/lt */ \"(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/functions/lt.js\")\nconst eq = __webpack_require__(/*! ./functions/eq */ \"(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/functions/eq.js\")\nconst neq = __webpack_require__(/*! ./functions/neq */ \"(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/functions/neq.js\")\nconst gte = __webpack_require__(/*! ./functions/gte */ \"(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/functions/gte.js\")\nconst lte = __webpack_require__(/*! ./functions/lte */ \"(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/functions/lte.js\")\nconst cmp = __webpack_require__(/*! ./functions/cmp */ \"(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/functions/cmp.js\")\nconst coerce = __webpack_require__(/*! ./functions/coerce */ \"(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/functions/coerce.js\")\nconst Comparator = __webpack_require__(/*! ./classes/comparator */ \"(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/classes/comparator.js\")\nconst Range = __webpack_require__(/*! ./classes/range */ \"(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/classes/range.js\")\nconst satisfies = __webpack_require__(/*! ./functions/satisfies */ \"(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/functions/satisfies.js\")\nconst toComparators = __webpack_require__(/*! ./ranges/to-comparators */ \"(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/ranges/to-comparators.js\")\nconst maxSatisfying = __webpack_require__(/*! ./ranges/max-satisfying */ \"(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/ranges/max-satisfying.js\")\nconst minSatisfying = __webpack_require__(/*! ./ranges/min-satisfying */ \"(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/ranges/min-satisfying.js\")\nconst minVersion = __webpack_require__(/*! ./ranges/min-version */ \"(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/ranges/min-version.js\")\nconst validRange = __webpack_require__(/*! ./ranges/valid */ \"(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/ranges/valid.js\")\nconst outside = __webpack_require__(/*! ./ranges/outside */ \"(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/ranges/outside.js\")\nconst gtr = __webpack_require__(/*! ./ranges/gtr */ \"(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/ranges/gtr.js\")\nconst ltr = __webpack_require__(/*! ./ranges/ltr */ \"(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/ranges/ltr.js\")\nconst intersects = __webpack_require__(/*! ./ranges/intersects */ \"(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/ranges/intersects.js\")\nconst simplifyRange = __webpack_require__(/*! ./ranges/simplify */ \"(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/ranges/simplify.js\")\nconst subset = __webpack_require__(/*! ./ranges/subset */ \"(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/ranges/subset.js\")\nmodule.exports = {\n  parse,\n  valid,\n  clean,\n  inc,\n  diff,\n  major,\n  minor,\n  patch,\n  prerelease,\n  compare,\n  rcompare,\n  compareLoose,\n  compareBuild,\n  sort,\n  rsort,\n  gt,\n  lt,\n  eq,\n  neq,\n  gte,\n  lte,\n  cmp,\n  coerce,\n  Comparator,\n  Range,\n  satisfies,\n  toComparators,\n  maxSatisfying,\n  minSatisfying,\n  minVersion,\n  validRange,\n  outside,\n  gtr,\n  ltr,\n  intersects,\n  simplifyRange,\n  subset,\n  SemVer,\n  re: internalRe.re,\n  src: internalRe.src,\n  tokens: internalRe.t,\n  SEMVER_SPEC_VERSION: constants.SEMVER_SPEC_VERSION,\n  RELEASE_TYPES: constants.RELEASE_TYPES,\n  compareIdentifiers: identifiers.compareIdentifiers,\n  rcompareIdentifiers: identifiers.rcompareIdentifiers,\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/internal/constants.js":
/*!**********************************************************************************!*\
  !*** ./node_modules/@ledgerhq/devices/node_modules/semver/internal/constants.js ***!
  \**********************************************************************************/
/***/ ((module) => {

eval("\n\n// Note: this is the semver.org version of the spec that it implements\n// Not necessarily the package version of this code.\nconst SEMVER_SPEC_VERSION = '2.0.0'\n\nconst MAX_LENGTH = 256\nconst MAX_SAFE_INTEGER = Number.MAX_SAFE_INTEGER ||\n/* istanbul ignore next */ 9007199254740991\n\n// Max safe segment length for coercion.\nconst MAX_SAFE_COMPONENT_LENGTH = 16\n\n// Max safe length for a build identifier. The max length minus 6 characters for\n// the shortest version with a build 0.0.0+BUILD.\nconst MAX_SAFE_BUILD_LENGTH = MAX_LENGTH - 6\n\nconst RELEASE_TYPES = [\n  'major',\n  'premajor',\n  'minor',\n  'preminor',\n  'patch',\n  'prepatch',\n  'prerelease',\n]\n\nmodule.exports = {\n  MAX_LENGTH,\n  MAX_SAFE_COMPONENT_LENGTH,\n  MAX_SAFE_BUILD_LENGTH,\n  MAX_SAFE_INTEGER,\n  RELEASE_TYPES,\n  SEMVER_SPEC_VERSION,\n  FLAG_INCLUDE_PRERELEASE: 0b001,\n  FLAG_LOOSE: 0b010,\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQGxlZGdlcmhxL2RldmljZXMvbm9kZV9tb2R1bGVzL3NlbXZlci9pbnRlcm5hbC9jb25zdGFudHMuanMiLCJtYXBwaW5ncyI6IkFBQVk7O0FBRVo7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIi9Vc2Vycy94eW0vY29kZS90Ym9vay9vbnRvbl9jYy9vbmlvbi1sYXVuY2gvZnJvbnRlbmQvbm9kZV9tb2R1bGVzL0BsZWRnZXJocS9kZXZpY2VzL25vZGVfbW9kdWxlcy9zZW12ZXIvaW50ZXJuYWwvY29uc3RhbnRzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0J1xuXG4vLyBOb3RlOiB0aGlzIGlzIHRoZSBzZW12ZXIub3JnIHZlcnNpb24gb2YgdGhlIHNwZWMgdGhhdCBpdCBpbXBsZW1lbnRzXG4vLyBOb3QgbmVjZXNzYXJpbHkgdGhlIHBhY2thZ2UgdmVyc2lvbiBvZiB0aGlzIGNvZGUuXG5jb25zdCBTRU1WRVJfU1BFQ19WRVJTSU9OID0gJzIuMC4wJ1xuXG5jb25zdCBNQVhfTEVOR1RIID0gMjU2XG5jb25zdCBNQVhfU0FGRV9JTlRFR0VSID0gTnVtYmVyLk1BWF9TQUZFX0lOVEVHRVIgfHxcbi8qIGlzdGFuYnVsIGlnbm9yZSBuZXh0ICovIDkwMDcxOTkyNTQ3NDA5OTFcblxuLy8gTWF4IHNhZmUgc2VnbWVudCBsZW5ndGggZm9yIGNvZXJjaW9uLlxuY29uc3QgTUFYX1NBRkVfQ09NUE9ORU5UX0xFTkdUSCA9IDE2XG5cbi8vIE1heCBzYWZlIGxlbmd0aCBmb3IgYSBidWlsZCBpZGVudGlmaWVyLiBUaGUgbWF4IGxlbmd0aCBtaW51cyA2IGNoYXJhY3RlcnMgZm9yXG4vLyB0aGUgc2hvcnRlc3QgdmVyc2lvbiB3aXRoIGEgYnVpbGQgMC4wLjArQlVJTEQuXG5jb25zdCBNQVhfU0FGRV9CVUlMRF9MRU5HVEggPSBNQVhfTEVOR1RIIC0gNlxuXG5jb25zdCBSRUxFQVNFX1RZUEVTID0gW1xuICAnbWFqb3InLFxuICAncHJlbWFqb3InLFxuICAnbWlub3InLFxuICAncHJlbWlub3InLFxuICAncGF0Y2gnLFxuICAncHJlcGF0Y2gnLFxuICAncHJlcmVsZWFzZScsXG5dXG5cbm1vZHVsZS5leHBvcnRzID0ge1xuICBNQVhfTEVOR1RILFxuICBNQVhfU0FGRV9DT01QT05FTlRfTEVOR1RILFxuICBNQVhfU0FGRV9CVUlMRF9MRU5HVEgsXG4gIE1BWF9TQUZFX0lOVEVHRVIsXG4gIFJFTEVBU0VfVFlQRVMsXG4gIFNFTVZFUl9TUEVDX1ZFUlNJT04sXG4gIEZMQUdfSU5DTFVERV9QUkVSRUxFQVNFOiAwYjAwMSxcbiAgRkxBR19MT09TRTogMGIwMTAsXG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/internal/constants.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/internal/debug.js":
/*!******************************************************************************!*\
  !*** ./node_modules/@ledgerhq/devices/node_modules/semver/internal/debug.js ***!
  \******************************************************************************/
/***/ ((module) => {

eval("\n\nconst debug = (\n  typeof process === 'object' &&\n  process.env &&\n  process.env.NODE_DEBUG &&\n  /\\bsemver\\b/i.test(process.env.NODE_DEBUG)\n) ? (...args) => console.error('SEMVER', ...args)\n  : () => {}\n\nmodule.exports = debug\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQGxlZGdlcmhxL2RldmljZXMvbm9kZV9tb2R1bGVzL3NlbXZlci9pbnRlcm5hbC9kZWJ1Zy5qcyIsIm1hcHBpbmdzIjoiQUFBWTs7QUFFWjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQSIsInNvdXJjZXMiOlsiL1VzZXJzL3h5bS9jb2RlL3Rib29rL29udG9uX2NjL29uaW9uLWxhdW5jaC9mcm9udGVuZC9ub2RlX21vZHVsZXMvQGxlZGdlcmhxL2RldmljZXMvbm9kZV9tb2R1bGVzL3NlbXZlci9pbnRlcm5hbC9kZWJ1Zy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCdcblxuY29uc3QgZGVidWcgPSAoXG4gIHR5cGVvZiBwcm9jZXNzID09PSAnb2JqZWN0JyAmJlxuICBwcm9jZXNzLmVudiAmJlxuICBwcm9jZXNzLmVudi5OT0RFX0RFQlVHICYmXG4gIC9cXGJzZW12ZXJcXGIvaS50ZXN0KHByb2Nlc3MuZW52Lk5PREVfREVCVUcpXG4pID8gKC4uLmFyZ3MpID0+IGNvbnNvbGUuZXJyb3IoJ1NFTVZFUicsIC4uLmFyZ3MpXG4gIDogKCkgPT4ge31cblxubW9kdWxlLmV4cG9ydHMgPSBkZWJ1Z1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/internal/debug.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/internal/identifiers.js":
/*!************************************************************************************!*\
  !*** ./node_modules/@ledgerhq/devices/node_modules/semver/internal/identifiers.js ***!
  \************************************************************************************/
/***/ ((module) => {

eval("\n\nconst numeric = /^[0-9]+$/\nconst compareIdentifiers = (a, b) => {\n  const anum = numeric.test(a)\n  const bnum = numeric.test(b)\n\n  if (anum && bnum) {\n    a = +a\n    b = +b\n  }\n\n  return a === b ? 0\n    : (anum && !bnum) ? -1\n    : (bnum && !anum) ? 1\n    : a < b ? -1\n    : 1\n}\n\nconst rcompareIdentifiers = (a, b) => compareIdentifiers(b, a)\n\nmodule.exports = {\n  compareIdentifiers,\n  rcompareIdentifiers,\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQGxlZGdlcmhxL2RldmljZXMvbm9kZV9tb2R1bGVzL3NlbXZlci9pbnRlcm5hbC9pZGVudGlmaWVycy5qcyIsIm1hcHBpbmdzIjoiQUFBWTs7QUFFWjtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7O0FBRUE7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIi9Vc2Vycy94eW0vY29kZS90Ym9vay9vbnRvbl9jYy9vbmlvbi1sYXVuY2gvZnJvbnRlbmQvbm9kZV9tb2R1bGVzL0BsZWRnZXJocS9kZXZpY2VzL25vZGVfbW9kdWxlcy9zZW12ZXIvaW50ZXJuYWwvaWRlbnRpZmllcnMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnXG5cbmNvbnN0IG51bWVyaWMgPSAvXlswLTldKyQvXG5jb25zdCBjb21wYXJlSWRlbnRpZmllcnMgPSAoYSwgYikgPT4ge1xuICBjb25zdCBhbnVtID0gbnVtZXJpYy50ZXN0KGEpXG4gIGNvbnN0IGJudW0gPSBudW1lcmljLnRlc3QoYilcblxuICBpZiAoYW51bSAmJiBibnVtKSB7XG4gICAgYSA9ICthXG4gICAgYiA9ICtiXG4gIH1cblxuICByZXR1cm4gYSA9PT0gYiA/IDBcbiAgICA6IChhbnVtICYmICFibnVtKSA/IC0xXG4gICAgOiAoYm51bSAmJiAhYW51bSkgPyAxXG4gICAgOiBhIDwgYiA/IC0xXG4gICAgOiAxXG59XG5cbmNvbnN0IHJjb21wYXJlSWRlbnRpZmllcnMgPSAoYSwgYikgPT4gY29tcGFyZUlkZW50aWZpZXJzKGIsIGEpXG5cbm1vZHVsZS5leHBvcnRzID0ge1xuICBjb21wYXJlSWRlbnRpZmllcnMsXG4gIHJjb21wYXJlSWRlbnRpZmllcnMsXG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/internal/identifiers.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/internal/lrucache.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/@ledgerhq/devices/node_modules/semver/internal/lrucache.js ***!
  \*********************************************************************************/
/***/ ((module) => {

eval("\n\nclass LRUCache {\n  constructor () {\n    this.max = 1000\n    this.map = new Map()\n  }\n\n  get (key) {\n    const value = this.map.get(key)\n    if (value === undefined) {\n      return undefined\n    } else {\n      // Remove the key from the map and add it to the end\n      this.map.delete(key)\n      this.map.set(key, value)\n      return value\n    }\n  }\n\n  delete (key) {\n    return this.map.delete(key)\n  }\n\n  set (key, value) {\n    const deleted = this.delete(key)\n\n    if (!deleted && value !== undefined) {\n      // If cache is full, delete the least recently used item\n      if (this.map.size >= this.max) {\n        const firstKey = this.map.keys().next().value\n        this.delete(firstKey)\n      }\n\n      this.map.set(key, value)\n    }\n\n    return this\n  }\n}\n\nmodule.exports = LRUCache\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQGxlZGdlcmhxL2RldmljZXMvbm9kZV9tb2R1bGVzL3NlbXZlci9pbnRlcm5hbC9scnVjYWNoZS5qcyIsIm1hcHBpbmdzIjoiQUFBWTs7QUFFWjtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsTUFBTTtBQUNOO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUEiLCJzb3VyY2VzIjpbIi9Vc2Vycy94eW0vY29kZS90Ym9vay9vbnRvbl9jYy9vbmlvbi1sYXVuY2gvZnJvbnRlbmQvbm9kZV9tb2R1bGVzL0BsZWRnZXJocS9kZXZpY2VzL25vZGVfbW9kdWxlcy9zZW12ZXIvaW50ZXJuYWwvbHJ1Y2FjaGUuanMiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnXG5cbmNsYXNzIExSVUNhY2hlIHtcbiAgY29uc3RydWN0b3IgKCkge1xuICAgIHRoaXMubWF4ID0gMTAwMFxuICAgIHRoaXMubWFwID0gbmV3IE1hcCgpXG4gIH1cblxuICBnZXQgKGtleSkge1xuICAgIGNvbnN0IHZhbHVlID0gdGhpcy5tYXAuZ2V0KGtleSlcbiAgICBpZiAodmFsdWUgPT09IHVuZGVmaW5lZCkge1xuICAgICAgcmV0dXJuIHVuZGVmaW5lZFxuICAgIH0gZWxzZSB7XG4gICAgICAvLyBSZW1vdmUgdGhlIGtleSBmcm9tIHRoZSBtYXAgYW5kIGFkZCBpdCB0byB0aGUgZW5kXG4gICAgICB0aGlzLm1hcC5kZWxldGUoa2V5KVxuICAgICAgdGhpcy5tYXAuc2V0KGtleSwgdmFsdWUpXG4gICAgICByZXR1cm4gdmFsdWVcbiAgICB9XG4gIH1cblxuICBkZWxldGUgKGtleSkge1xuICAgIHJldHVybiB0aGlzLm1hcC5kZWxldGUoa2V5KVxuICB9XG5cbiAgc2V0IChrZXksIHZhbHVlKSB7XG4gICAgY29uc3QgZGVsZXRlZCA9IHRoaXMuZGVsZXRlKGtleSlcblxuICAgIGlmICghZGVsZXRlZCAmJiB2YWx1ZSAhPT0gdW5kZWZpbmVkKSB7XG4gICAgICAvLyBJZiBjYWNoZSBpcyBmdWxsLCBkZWxldGUgdGhlIGxlYXN0IHJlY2VudGx5IHVzZWQgaXRlbVxuICAgICAgaWYgKHRoaXMubWFwLnNpemUgPj0gdGhpcy5tYXgpIHtcbiAgICAgICAgY29uc3QgZmlyc3RLZXkgPSB0aGlzLm1hcC5rZXlzKCkubmV4dCgpLnZhbHVlXG4gICAgICAgIHRoaXMuZGVsZXRlKGZpcnN0S2V5KVxuICAgICAgfVxuXG4gICAgICB0aGlzLm1hcC5zZXQoa2V5LCB2YWx1ZSlcbiAgICB9XG5cbiAgICByZXR1cm4gdGhpc1xuICB9XG59XG5cbm1vZHVsZS5leHBvcnRzID0gTFJVQ2FjaGVcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/internal/lrucache.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/internal/parse-options.js":
/*!**************************************************************************************!*\
  !*** ./node_modules/@ledgerhq/devices/node_modules/semver/internal/parse-options.js ***!
  \**************************************************************************************/
/***/ ((module) => {

eval("\n\n// parse out just the options we care about\nconst looseOption = Object.freeze({ loose: true })\nconst emptyOpts = Object.freeze({ })\nconst parseOptions = options => {\n  if (!options) {\n    return emptyOpts\n  }\n\n  if (typeof options !== 'object') {\n    return looseOption\n  }\n\n  return options\n}\nmodule.exports = parseOptions\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQGxlZGdlcmhxL2RldmljZXMvbm9kZV9tb2R1bGVzL3NlbXZlci9pbnRlcm5hbC9wYXJzZS1vcHRpb25zLmpzIiwibWFwcGluZ3MiOiJBQUFZOztBQUVaO0FBQ0Esb0NBQW9DLGFBQWE7QUFDakQsbUNBQW1DO0FBQ25DO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIi9Vc2Vycy94eW0vY29kZS90Ym9vay9vbnRvbl9jYy9vbmlvbi1sYXVuY2gvZnJvbnRlbmQvbm9kZV9tb2R1bGVzL0BsZWRnZXJocS9kZXZpY2VzL25vZGVfbW9kdWxlcy9zZW12ZXIvaW50ZXJuYWwvcGFyc2Utb3B0aW9ucy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCdcblxuLy8gcGFyc2Ugb3V0IGp1c3QgdGhlIG9wdGlvbnMgd2UgY2FyZSBhYm91dFxuY29uc3QgbG9vc2VPcHRpb24gPSBPYmplY3QuZnJlZXplKHsgbG9vc2U6IHRydWUgfSlcbmNvbnN0IGVtcHR5T3B0cyA9IE9iamVjdC5mcmVlemUoeyB9KVxuY29uc3QgcGFyc2VPcHRpb25zID0gb3B0aW9ucyA9PiB7XG4gIGlmICghb3B0aW9ucykge1xuICAgIHJldHVybiBlbXB0eU9wdHNcbiAgfVxuXG4gIGlmICh0eXBlb2Ygb3B0aW9ucyAhPT0gJ29iamVjdCcpIHtcbiAgICByZXR1cm4gbG9vc2VPcHRpb25cbiAgfVxuXG4gIHJldHVybiBvcHRpb25zXG59XG5tb2R1bGUuZXhwb3J0cyA9IHBhcnNlT3B0aW9uc1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/internal/parse-options.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/internal/re.js":
/*!***************************************************************************!*\
  !*** ./node_modules/@ledgerhq/devices/node_modules/semver/internal/re.js ***!
  \***************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval("\n\nconst {\n  MAX_SAFE_COMPONENT_LENGTH,\n  MAX_SAFE_BUILD_LENGTH,\n  MAX_LENGTH,\n} = __webpack_require__(/*! ./constants */ \"(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/internal/constants.js\")\nconst debug = __webpack_require__(/*! ./debug */ \"(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/internal/debug.js\")\nexports = module.exports = {}\n\n// The actual regexps go on exports.re\nconst re = exports.re = []\nconst safeRe = exports.safeRe = []\nconst src = exports.src = []\nconst safeSrc = exports.safeSrc = []\nconst t = exports.t = {}\nlet R = 0\n\nconst LETTERDASHNUMBER = '[a-zA-Z0-9-]'\n\n// Replace some greedy regex tokens to prevent regex dos issues. These regex are\n// used internally via the safeRe object since all inputs in this library get\n// normalized first to trim and collapse all extra whitespace. The original\n// regexes are exported for userland consumption and lower level usage. A\n// future breaking change could export the safer regex only with a note that\n// all input should have extra whitespace removed.\nconst safeRegexReplacements = [\n  ['\\\\s', 1],\n  ['\\\\d', MAX_LENGTH],\n  [LETTERDASHNUMBER, MAX_SAFE_BUILD_LENGTH],\n]\n\nconst makeSafeRegex = (value) => {\n  for (const [token, max] of safeRegexReplacements) {\n    value = value\n      .split(`${token}*`).join(`${token}{0,${max}}`)\n      .split(`${token}+`).join(`${token}{1,${max}}`)\n  }\n  return value\n}\n\nconst createToken = (name, value, isGlobal) => {\n  const safe = makeSafeRegex(value)\n  const index = R++\n  debug(name, index, value)\n  t[name] = index\n  src[index] = value\n  safeSrc[index] = safe\n  re[index] = new RegExp(value, isGlobal ? 'g' : undefined)\n  safeRe[index] = new RegExp(safe, isGlobal ? 'g' : undefined)\n}\n\n// The following Regular Expressions can be used for tokenizing,\n// validating, and parsing SemVer version strings.\n\n// ## Numeric Identifier\n// A single `0`, or a non-zero digit followed by zero or more digits.\n\ncreateToken('NUMERICIDENTIFIER', '0|[1-9]\\\\d*')\ncreateToken('NUMERICIDENTIFIERLOOSE', '\\\\d+')\n\n// ## Non-numeric Identifier\n// Zero or more digits, followed by a letter or hyphen, and then zero or\n// more letters, digits, or hyphens.\n\ncreateToken('NONNUMERICIDENTIFIER', `\\\\d*[a-zA-Z-]${LETTERDASHNUMBER}*`)\n\n// ## Main Version\n// Three dot-separated numeric identifiers.\n\ncreateToken('MAINVERSION', `(${src[t.NUMERICIDENTIFIER]})\\\\.` +\n                   `(${src[t.NUMERICIDENTIFIER]})\\\\.` +\n                   `(${src[t.NUMERICIDENTIFIER]})`)\n\ncreateToken('MAINVERSIONLOOSE', `(${src[t.NUMERICIDENTIFIERLOOSE]})\\\\.` +\n                        `(${src[t.NUMERICIDENTIFIERLOOSE]})\\\\.` +\n                        `(${src[t.NUMERICIDENTIFIERLOOSE]})`)\n\n// ## Pre-release Version Identifier\n// A numeric identifier, or a non-numeric identifier.\n// Non-numberic identifiers include numberic identifiers but can be longer.\n// Therefore non-numberic identifiers must go first.\n\ncreateToken('PRERELEASEIDENTIFIER', `(?:${src[t.NONNUMERICIDENTIFIER]\n}|${src[t.NUMERICIDENTIFIER]})`)\n\ncreateToken('PRERELEASEIDENTIFIERLOOSE', `(?:${src[t.NONNUMERICIDENTIFIER]\n}|${src[t.NUMERICIDENTIFIERLOOSE]})`)\n\n// ## Pre-release Version\n// Hyphen, followed by one or more dot-separated pre-release version\n// identifiers.\n\ncreateToken('PRERELEASE', `(?:-(${src[t.PRERELEASEIDENTIFIER]\n}(?:\\\\.${src[t.PRERELEASEIDENTIFIER]})*))`)\n\ncreateToken('PRERELEASELOOSE', `(?:-?(${src[t.PRERELEASEIDENTIFIERLOOSE]\n}(?:\\\\.${src[t.PRERELEASEIDENTIFIERLOOSE]})*))`)\n\n// ## Build Metadata Identifier\n// Any combination of digits, letters, or hyphens.\n\ncreateToken('BUILDIDENTIFIER', `${LETTERDASHNUMBER}+`)\n\n// ## Build Metadata\n// Plus sign, followed by one or more period-separated build metadata\n// identifiers.\n\ncreateToken('BUILD', `(?:\\\\+(${src[t.BUILDIDENTIFIER]\n}(?:\\\\.${src[t.BUILDIDENTIFIER]})*))`)\n\n// ## Full Version String\n// A main version, followed optionally by a pre-release version and\n// build metadata.\n\n// Note that the only major, minor, patch, and pre-release sections of\n// the version string are capturing groups.  The build metadata is not a\n// capturing group, because it should not ever be used in version\n// comparison.\n\ncreateToken('FULLPLAIN', `v?${src[t.MAINVERSION]\n}${src[t.PRERELEASE]}?${\n  src[t.BUILD]}?`)\n\ncreateToken('FULL', `^${src[t.FULLPLAIN]}$`)\n\n// like full, but allows v1.2.3 and =1.2.3, which people do sometimes.\n// also, 1.0.0alpha1 (prerelease without the hyphen) which is pretty\n// common in the npm registry.\ncreateToken('LOOSEPLAIN', `[v=\\\\s]*${src[t.MAINVERSIONLOOSE]\n}${src[t.PRERELEASELOOSE]}?${\n  src[t.BUILD]}?`)\n\ncreateToken('LOOSE', `^${src[t.LOOSEPLAIN]}$`)\n\ncreateToken('GTLT', '((?:<|>)?=?)')\n\n// Something like \"2.*\" or \"1.2.x\".\n// Note that \"x.x\" is a valid xRange identifer, meaning \"any version\"\n// Only the first item is strictly required.\ncreateToken('XRANGEIDENTIFIERLOOSE', `${src[t.NUMERICIDENTIFIERLOOSE]}|x|X|\\\\*`)\ncreateToken('XRANGEIDENTIFIER', `${src[t.NUMERICIDENTIFIER]}|x|X|\\\\*`)\n\ncreateToken('XRANGEPLAIN', `[v=\\\\s]*(${src[t.XRANGEIDENTIFIER]})` +\n                   `(?:\\\\.(${src[t.XRANGEIDENTIFIER]})` +\n                   `(?:\\\\.(${src[t.XRANGEIDENTIFIER]})` +\n                   `(?:${src[t.PRERELEASE]})?${\n                     src[t.BUILD]}?` +\n                   `)?)?`)\n\ncreateToken('XRANGEPLAINLOOSE', `[v=\\\\s]*(${src[t.XRANGEIDENTIFIERLOOSE]})` +\n                        `(?:\\\\.(${src[t.XRANGEIDENTIFIERLOOSE]})` +\n                        `(?:\\\\.(${src[t.XRANGEIDENTIFIERLOOSE]})` +\n                        `(?:${src[t.PRERELEASELOOSE]})?${\n                          src[t.BUILD]}?` +\n                        `)?)?`)\n\ncreateToken('XRANGE', `^${src[t.GTLT]}\\\\s*${src[t.XRANGEPLAIN]}$`)\ncreateToken('XRANGELOOSE', `^${src[t.GTLT]}\\\\s*${src[t.XRANGEPLAINLOOSE]}$`)\n\n// Coercion.\n// Extract anything that could conceivably be a part of a valid semver\ncreateToken('COERCEPLAIN', `${'(^|[^\\\\d])' +\n              '(\\\\d{1,'}${MAX_SAFE_COMPONENT_LENGTH}})` +\n              `(?:\\\\.(\\\\d{1,${MAX_SAFE_COMPONENT_LENGTH}}))?` +\n              `(?:\\\\.(\\\\d{1,${MAX_SAFE_COMPONENT_LENGTH}}))?`)\ncreateToken('COERCE', `${src[t.COERCEPLAIN]}(?:$|[^\\\\d])`)\ncreateToken('COERCEFULL', src[t.COERCEPLAIN] +\n              `(?:${src[t.PRERELEASE]})?` +\n              `(?:${src[t.BUILD]})?` +\n              `(?:$|[^\\\\d])`)\ncreateToken('COERCERTL', src[t.COERCE], true)\ncreateToken('COERCERTLFULL', src[t.COERCEFULL], true)\n\n// Tilde ranges.\n// Meaning is \"reasonably at or greater than\"\ncreateToken('LONETILDE', '(?:~>?)')\n\ncreateToken('TILDETRIM', `(\\\\s*)${src[t.LONETILDE]}\\\\s+`, true)\nexports.tildeTrimReplace = '$1~'\n\ncreateToken('TILDE', `^${src[t.LONETILDE]}${src[t.XRANGEPLAIN]}$`)\ncreateToken('TILDELOOSE', `^${src[t.LONETILDE]}${src[t.XRANGEPLAINLOOSE]}$`)\n\n// Caret ranges.\n// Meaning is \"at least and backwards compatible with\"\ncreateToken('LONECARET', '(?:\\\\^)')\n\ncreateToken('CARETTRIM', `(\\\\s*)${src[t.LONECARET]}\\\\s+`, true)\nexports.caretTrimReplace = '$1^'\n\ncreateToken('CARET', `^${src[t.LONECARET]}${src[t.XRANGEPLAIN]}$`)\ncreateToken('CARETLOOSE', `^${src[t.LONECARET]}${src[t.XRANGEPLAINLOOSE]}$`)\n\n// A simple gt/lt/eq thing, or just \"\" to indicate \"any version\"\ncreateToken('COMPARATORLOOSE', `^${src[t.GTLT]}\\\\s*(${src[t.LOOSEPLAIN]})$|^$`)\ncreateToken('COMPARATOR', `^${src[t.GTLT]}\\\\s*(${src[t.FULLPLAIN]})$|^$`)\n\n// An expression to strip any whitespace between the gtlt and the thing\n// it modifies, so that `> 1.2.3` ==> `>1.2.3`\ncreateToken('COMPARATORTRIM', `(\\\\s*)${src[t.GTLT]\n}\\\\s*(${src[t.LOOSEPLAIN]}|${src[t.XRANGEPLAIN]})`, true)\nexports.comparatorTrimReplace = '$1$2$3'\n\n// Something like `1.2.3 - 1.2.4`\n// Note that these all use the loose form, because they'll be\n// checked against either the strict or loose comparator form\n// later.\ncreateToken('HYPHENRANGE', `^\\\\s*(${src[t.XRANGEPLAIN]})` +\n                   `\\\\s+-\\\\s+` +\n                   `(${src[t.XRANGEPLAIN]})` +\n                   `\\\\s*$`)\n\ncreateToken('HYPHENRANGELOOSE', `^\\\\s*(${src[t.XRANGEPLAINLOOSE]})` +\n                        `\\\\s+-\\\\s+` +\n                        `(${src[t.XRANGEPLAINLOOSE]})` +\n                        `\\\\s*$`)\n\n// Star ranges basically just allow anything at all.\ncreateToken('STAR', '(<|>)?=?\\\\s*\\\\*')\n// >=0.0.0 is like a star\ncreateToken('GTE0', '^\\\\s*>=\\\\s*0\\\\.0\\\\.0\\\\s*$')\ncreateToken('GTE0PRE', '^\\\\s*>=\\\\s*0\\\\.0\\\\.0-0\\\\s*$')\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/internal/re.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/ranges/gtr.js":
/*!**************************************************************************!*\
  !*** ./node_modules/@ledgerhq/devices/node_modules/semver/ranges/gtr.js ***!
  \**************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\n// Determine if version is greater than all the versions possible in the range.\nconst outside = __webpack_require__(/*! ./outside */ \"(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/ranges/outside.js\")\nconst gtr = (version, range, options) => outside(version, range, '>', options)\nmodule.exports = gtr\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQGxlZGdlcmhxL2RldmljZXMvbm9kZV9tb2R1bGVzL3NlbXZlci9yYW5nZXMvZ3RyLmpzIiwibWFwcGluZ3MiOiJBQUFZOztBQUVaO0FBQ0EsZ0JBQWdCLG1CQUFPLENBQUMsK0ZBQVc7QUFDbkM7QUFDQSIsInNvdXJjZXMiOlsiL1VzZXJzL3h5bS9jb2RlL3Rib29rL29udG9uX2NjL29uaW9uLWxhdW5jaC9mcm9udGVuZC9ub2RlX21vZHVsZXMvQGxlZGdlcmhxL2RldmljZXMvbm9kZV9tb2R1bGVzL3NlbXZlci9yYW5nZXMvZ3RyLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0J1xuXG4vLyBEZXRlcm1pbmUgaWYgdmVyc2lvbiBpcyBncmVhdGVyIHRoYW4gYWxsIHRoZSB2ZXJzaW9ucyBwb3NzaWJsZSBpbiB0aGUgcmFuZ2UuXG5jb25zdCBvdXRzaWRlID0gcmVxdWlyZSgnLi9vdXRzaWRlJylcbmNvbnN0IGd0ciA9ICh2ZXJzaW9uLCByYW5nZSwgb3B0aW9ucykgPT4gb3V0c2lkZSh2ZXJzaW9uLCByYW5nZSwgJz4nLCBvcHRpb25zKVxubW9kdWxlLmV4cG9ydHMgPSBndHJcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/ranges/gtr.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/ranges/intersects.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/@ledgerhq/devices/node_modules/semver/ranges/intersects.js ***!
  \*********************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst Range = __webpack_require__(/*! ../classes/range */ \"(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/classes/range.js\")\nconst intersects = (r1, r2, options) => {\n  r1 = new Range(r1, options)\n  r2 = new Range(r2, options)\n  return r1.intersects(r2, options)\n}\nmodule.exports = intersects\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQGxlZGdlcmhxL2RldmljZXMvbm9kZV9tb2R1bGVzL3NlbXZlci9yYW5nZXMvaW50ZXJzZWN0cy5qcyIsIm1hcHBpbmdzIjoiQUFBWTs7QUFFWixjQUFjLG1CQUFPLENBQUMscUdBQWtCO0FBQ3hDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiL1VzZXJzL3h5bS9jb2RlL3Rib29rL29udG9uX2NjL29uaW9uLWxhdW5jaC9mcm9udGVuZC9ub2RlX21vZHVsZXMvQGxlZGdlcmhxL2RldmljZXMvbm9kZV9tb2R1bGVzL3NlbXZlci9yYW5nZXMvaW50ZXJzZWN0cy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCdcblxuY29uc3QgUmFuZ2UgPSByZXF1aXJlKCcuLi9jbGFzc2VzL3JhbmdlJylcbmNvbnN0IGludGVyc2VjdHMgPSAocjEsIHIyLCBvcHRpb25zKSA9PiB7XG4gIHIxID0gbmV3IFJhbmdlKHIxLCBvcHRpb25zKVxuICByMiA9IG5ldyBSYW5nZShyMiwgb3B0aW9ucylcbiAgcmV0dXJuIHIxLmludGVyc2VjdHMocjIsIG9wdGlvbnMpXG59XG5tb2R1bGUuZXhwb3J0cyA9IGludGVyc2VjdHNcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/ranges/intersects.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/ranges/ltr.js":
/*!**************************************************************************!*\
  !*** ./node_modules/@ledgerhq/devices/node_modules/semver/ranges/ltr.js ***!
  \**************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst outside = __webpack_require__(/*! ./outside */ \"(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/ranges/outside.js\")\n// Determine if version is less than all the versions possible in the range\nconst ltr = (version, range, options) => outside(version, range, '<', options)\nmodule.exports = ltr\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQGxlZGdlcmhxL2RldmljZXMvbm9kZV9tb2R1bGVzL3NlbXZlci9yYW5nZXMvbHRyLmpzIiwibWFwcGluZ3MiOiJBQUFZOztBQUVaLGdCQUFnQixtQkFBTyxDQUFDLCtGQUFXO0FBQ25DO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiL1VzZXJzL3h5bS9jb2RlL3Rib29rL29udG9uX2NjL29uaW9uLWxhdW5jaC9mcm9udGVuZC9ub2RlX21vZHVsZXMvQGxlZGdlcmhxL2RldmljZXMvbm9kZV9tb2R1bGVzL3NlbXZlci9yYW5nZXMvbHRyLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0J1xuXG5jb25zdCBvdXRzaWRlID0gcmVxdWlyZSgnLi9vdXRzaWRlJylcbi8vIERldGVybWluZSBpZiB2ZXJzaW9uIGlzIGxlc3MgdGhhbiBhbGwgdGhlIHZlcnNpb25zIHBvc3NpYmxlIGluIHRoZSByYW5nZVxuY29uc3QgbHRyID0gKHZlcnNpb24sIHJhbmdlLCBvcHRpb25zKSA9PiBvdXRzaWRlKHZlcnNpb24sIHJhbmdlLCAnPCcsIG9wdGlvbnMpXG5tb2R1bGUuZXhwb3J0cyA9IGx0clxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/ranges/ltr.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/ranges/max-satisfying.js":
/*!*************************************************************************************!*\
  !*** ./node_modules/@ledgerhq/devices/node_modules/semver/ranges/max-satisfying.js ***!
  \*************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst SemVer = __webpack_require__(/*! ../classes/semver */ \"(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/classes/semver.js\")\nconst Range = __webpack_require__(/*! ../classes/range */ \"(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/classes/range.js\")\n\nconst maxSatisfying = (versions, range, options) => {\n  let max = null\n  let maxSV = null\n  let rangeObj = null\n  try {\n    rangeObj = new Range(range, options)\n  } catch (er) {\n    return null\n  }\n  versions.forEach((v) => {\n    if (rangeObj.test(v)) {\n      // satisfies(v, range, options)\n      if (!max || maxSV.compare(v) === -1) {\n        // compare(max, v, true)\n        max = v\n        maxSV = new SemVer(max, options)\n      }\n    }\n  })\n  return max\n}\nmodule.exports = maxSatisfying\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQGxlZGdlcmhxL2RldmljZXMvbm9kZV9tb2R1bGVzL3NlbXZlci9yYW5nZXMvbWF4LXNhdGlzZnlpbmcuanMiLCJtYXBwaW5ncyI6IkFBQVk7O0FBRVosZUFBZSxtQkFBTyxDQUFDLHVHQUFtQjtBQUMxQyxjQUFjLG1CQUFPLENBQUMscUdBQWtCOztBQUV4QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxJQUFJO0FBQ0o7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIi9Vc2Vycy94eW0vY29kZS90Ym9vay9vbnRvbl9jYy9vbmlvbi1sYXVuY2gvZnJvbnRlbmQvbm9kZV9tb2R1bGVzL0BsZWRnZXJocS9kZXZpY2VzL25vZGVfbW9kdWxlcy9zZW12ZXIvcmFuZ2VzL21heC1zYXRpc2Z5aW5nLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0J1xuXG5jb25zdCBTZW1WZXIgPSByZXF1aXJlKCcuLi9jbGFzc2VzL3NlbXZlcicpXG5jb25zdCBSYW5nZSA9IHJlcXVpcmUoJy4uL2NsYXNzZXMvcmFuZ2UnKVxuXG5jb25zdCBtYXhTYXRpc2Z5aW5nID0gKHZlcnNpb25zLCByYW5nZSwgb3B0aW9ucykgPT4ge1xuICBsZXQgbWF4ID0gbnVsbFxuICBsZXQgbWF4U1YgPSBudWxsXG4gIGxldCByYW5nZU9iaiA9IG51bGxcbiAgdHJ5IHtcbiAgICByYW5nZU9iaiA9IG5ldyBSYW5nZShyYW5nZSwgb3B0aW9ucylcbiAgfSBjYXRjaCAoZXIpIHtcbiAgICByZXR1cm4gbnVsbFxuICB9XG4gIHZlcnNpb25zLmZvckVhY2goKHYpID0+IHtcbiAgICBpZiAocmFuZ2VPYmoudGVzdCh2KSkge1xuICAgICAgLy8gc2F0aXNmaWVzKHYsIHJhbmdlLCBvcHRpb25zKVxuICAgICAgaWYgKCFtYXggfHwgbWF4U1YuY29tcGFyZSh2KSA9PT0gLTEpIHtcbiAgICAgICAgLy8gY29tcGFyZShtYXgsIHYsIHRydWUpXG4gICAgICAgIG1heCA9IHZcbiAgICAgICAgbWF4U1YgPSBuZXcgU2VtVmVyKG1heCwgb3B0aW9ucylcbiAgICAgIH1cbiAgICB9XG4gIH0pXG4gIHJldHVybiBtYXhcbn1cbm1vZHVsZS5leHBvcnRzID0gbWF4U2F0aXNmeWluZ1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/ranges/max-satisfying.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/ranges/min-satisfying.js":
/*!*************************************************************************************!*\
  !*** ./node_modules/@ledgerhq/devices/node_modules/semver/ranges/min-satisfying.js ***!
  \*************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst SemVer = __webpack_require__(/*! ../classes/semver */ \"(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/classes/semver.js\")\nconst Range = __webpack_require__(/*! ../classes/range */ \"(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/classes/range.js\")\nconst minSatisfying = (versions, range, options) => {\n  let min = null\n  let minSV = null\n  let rangeObj = null\n  try {\n    rangeObj = new Range(range, options)\n  } catch (er) {\n    return null\n  }\n  versions.forEach((v) => {\n    if (rangeObj.test(v)) {\n      // satisfies(v, range, options)\n      if (!min || minSV.compare(v) === 1) {\n        // compare(min, v, true)\n        min = v\n        minSV = new SemVer(min, options)\n      }\n    }\n  })\n  return min\n}\nmodule.exports = minSatisfying\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQGxlZGdlcmhxL2RldmljZXMvbm9kZV9tb2R1bGVzL3NlbXZlci9yYW5nZXMvbWluLXNhdGlzZnlpbmcuanMiLCJtYXBwaW5ncyI6IkFBQVk7O0FBRVosZUFBZSxtQkFBTyxDQUFDLHVHQUFtQjtBQUMxQyxjQUFjLG1CQUFPLENBQUMscUdBQWtCO0FBQ3hDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiL1VzZXJzL3h5bS9jb2RlL3Rib29rL29udG9uX2NjL29uaW9uLWxhdW5jaC9mcm9udGVuZC9ub2RlX21vZHVsZXMvQGxlZGdlcmhxL2RldmljZXMvbm9kZV9tb2R1bGVzL3NlbXZlci9yYW5nZXMvbWluLXNhdGlzZnlpbmcuanMiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnXG5cbmNvbnN0IFNlbVZlciA9IHJlcXVpcmUoJy4uL2NsYXNzZXMvc2VtdmVyJylcbmNvbnN0IFJhbmdlID0gcmVxdWlyZSgnLi4vY2xhc3Nlcy9yYW5nZScpXG5jb25zdCBtaW5TYXRpc2Z5aW5nID0gKHZlcnNpb25zLCByYW5nZSwgb3B0aW9ucykgPT4ge1xuICBsZXQgbWluID0gbnVsbFxuICBsZXQgbWluU1YgPSBudWxsXG4gIGxldCByYW5nZU9iaiA9IG51bGxcbiAgdHJ5IHtcbiAgICByYW5nZU9iaiA9IG5ldyBSYW5nZShyYW5nZSwgb3B0aW9ucylcbiAgfSBjYXRjaCAoZXIpIHtcbiAgICByZXR1cm4gbnVsbFxuICB9XG4gIHZlcnNpb25zLmZvckVhY2goKHYpID0+IHtcbiAgICBpZiAocmFuZ2VPYmoudGVzdCh2KSkge1xuICAgICAgLy8gc2F0aXNmaWVzKHYsIHJhbmdlLCBvcHRpb25zKVxuICAgICAgaWYgKCFtaW4gfHwgbWluU1YuY29tcGFyZSh2KSA9PT0gMSkge1xuICAgICAgICAvLyBjb21wYXJlKG1pbiwgdiwgdHJ1ZSlcbiAgICAgICAgbWluID0gdlxuICAgICAgICBtaW5TViA9IG5ldyBTZW1WZXIobWluLCBvcHRpb25zKVxuICAgICAgfVxuICAgIH1cbiAgfSlcbiAgcmV0dXJuIG1pblxufVxubW9kdWxlLmV4cG9ydHMgPSBtaW5TYXRpc2Z5aW5nXG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/ranges/min-satisfying.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/ranges/min-version.js":
/*!**********************************************************************************!*\
  !*** ./node_modules/@ledgerhq/devices/node_modules/semver/ranges/min-version.js ***!
  \**********************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst SemVer = __webpack_require__(/*! ../classes/semver */ \"(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/classes/semver.js\")\nconst Range = __webpack_require__(/*! ../classes/range */ \"(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/classes/range.js\")\nconst gt = __webpack_require__(/*! ../functions/gt */ \"(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/functions/gt.js\")\n\nconst minVersion = (range, loose) => {\n  range = new Range(range, loose)\n\n  let minver = new SemVer('0.0.0')\n  if (range.test(minver)) {\n    return minver\n  }\n\n  minver = new SemVer('0.0.0-0')\n  if (range.test(minver)) {\n    return minver\n  }\n\n  minver = null\n  for (let i = 0; i < range.set.length; ++i) {\n    const comparators = range.set[i]\n\n    let setMin = null\n    comparators.forEach((comparator) => {\n      // Clone to avoid manipulating the comparator's semver object.\n      const compver = new SemVer(comparator.semver.version)\n      switch (comparator.operator) {\n        case '>':\n          if (compver.prerelease.length === 0) {\n            compver.patch++\n          } else {\n            compver.prerelease.push(0)\n          }\n          compver.raw = compver.format()\n          /* fallthrough */\n        case '':\n        case '>=':\n          if (!setMin || gt(compver, setMin)) {\n            setMin = compver\n          }\n          break\n        case '<':\n        case '<=':\n          /* Ignore maximum versions */\n          break\n        /* istanbul ignore next */\n        default:\n          throw new Error(`Unexpected operation: ${comparator.operator}`)\n      }\n    })\n    if (setMin && (!minver || gt(minver, setMin))) {\n      minver = setMin\n    }\n  }\n\n  if (minver && range.test(minver)) {\n    return minver\n  }\n\n  return null\n}\nmodule.exports = minVersion\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/ranges/min-version.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/ranges/outside.js":
/*!******************************************************************************!*\
  !*** ./node_modules/@ledgerhq/devices/node_modules/semver/ranges/outside.js ***!
  \******************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst SemVer = __webpack_require__(/*! ../classes/semver */ \"(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/classes/semver.js\")\nconst Comparator = __webpack_require__(/*! ../classes/comparator */ \"(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/classes/comparator.js\")\nconst { ANY } = Comparator\nconst Range = __webpack_require__(/*! ../classes/range */ \"(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/classes/range.js\")\nconst satisfies = __webpack_require__(/*! ../functions/satisfies */ \"(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/functions/satisfies.js\")\nconst gt = __webpack_require__(/*! ../functions/gt */ \"(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/functions/gt.js\")\nconst lt = __webpack_require__(/*! ../functions/lt */ \"(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/functions/lt.js\")\nconst lte = __webpack_require__(/*! ../functions/lte */ \"(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/functions/lte.js\")\nconst gte = __webpack_require__(/*! ../functions/gte */ \"(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/functions/gte.js\")\n\nconst outside = (version, range, hilo, options) => {\n  version = new SemVer(version, options)\n  range = new Range(range, options)\n\n  let gtfn, ltefn, ltfn, comp, ecomp\n  switch (hilo) {\n    case '>':\n      gtfn = gt\n      ltefn = lte\n      ltfn = lt\n      comp = '>'\n      ecomp = '>='\n      break\n    case '<':\n      gtfn = lt\n      ltefn = gte\n      ltfn = gt\n      comp = '<'\n      ecomp = '<='\n      break\n    default:\n      throw new TypeError('Must provide a hilo val of \"<\" or \">\"')\n  }\n\n  // If it satisfies the range it is not outside\n  if (satisfies(version, range, options)) {\n    return false\n  }\n\n  // From now on, variable terms are as if we're in \"gtr\" mode.\n  // but note that everything is flipped for the \"ltr\" function.\n\n  for (let i = 0; i < range.set.length; ++i) {\n    const comparators = range.set[i]\n\n    let high = null\n    let low = null\n\n    comparators.forEach((comparator) => {\n      if (comparator.semver === ANY) {\n        comparator = new Comparator('>=0.0.0')\n      }\n      high = high || comparator\n      low = low || comparator\n      if (gtfn(comparator.semver, high.semver, options)) {\n        high = comparator\n      } else if (ltfn(comparator.semver, low.semver, options)) {\n        low = comparator\n      }\n    })\n\n    // If the edge version comparator has a operator then our version\n    // isn't outside it\n    if (high.operator === comp || high.operator === ecomp) {\n      return false\n    }\n\n    // If the lowest version comparator has an operator and our version\n    // is less than it then it isn't higher than the range\n    if ((!low.operator || low.operator === comp) &&\n        ltefn(version, low.semver)) {\n      return false\n    } else if (low.operator === ecomp && ltfn(version, low.semver)) {\n      return false\n    }\n  }\n  return true\n}\n\nmodule.exports = outside\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/ranges/outside.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/ranges/simplify.js":
/*!*******************************************************************************!*\
  !*** ./node_modules/@ledgerhq/devices/node_modules/semver/ranges/simplify.js ***!
  \*******************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\n// given a set of versions and a range, create a \"simplified\" range\n// that includes the same versions that the original range does\n// If the original range is shorter than the simplified one, return that.\nconst satisfies = __webpack_require__(/*! ../functions/satisfies.js */ \"(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/functions/satisfies.js\")\nconst compare = __webpack_require__(/*! ../functions/compare.js */ \"(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/functions/compare.js\")\nmodule.exports = (versions, range, options) => {\n  const set = []\n  let first = null\n  let prev = null\n  const v = versions.sort((a, b) => compare(a, b, options))\n  for (const version of v) {\n    const included = satisfies(version, range, options)\n    if (included) {\n      prev = version\n      if (!first) {\n        first = version\n      }\n    } else {\n      if (prev) {\n        set.push([first, prev])\n      }\n      prev = null\n      first = null\n    }\n  }\n  if (first) {\n    set.push([first, null])\n  }\n\n  const ranges = []\n  for (const [min, max] of set) {\n    if (min === max) {\n      ranges.push(min)\n    } else if (!max && min === v[0]) {\n      ranges.push('*')\n    } else if (!max) {\n      ranges.push(`>=${min}`)\n    } else if (min === v[0]) {\n      ranges.push(`<=${max}`)\n    } else {\n      ranges.push(`${min} - ${max}`)\n    }\n  }\n  const simplified = ranges.join(' || ')\n  const original = typeof range.raw === 'string' ? range.raw : String(range)\n  return simplified.length < original.length ? simplified : range\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/ranges/simplify.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/ranges/subset.js":
/*!*****************************************************************************!*\
  !*** ./node_modules/@ledgerhq/devices/node_modules/semver/ranges/subset.js ***!
  \*****************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst Range = __webpack_require__(/*! ../classes/range.js */ \"(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/classes/range.js\")\nconst Comparator = __webpack_require__(/*! ../classes/comparator.js */ \"(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/classes/comparator.js\")\nconst { ANY } = Comparator\nconst satisfies = __webpack_require__(/*! ../functions/satisfies.js */ \"(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/functions/satisfies.js\")\nconst compare = __webpack_require__(/*! ../functions/compare.js */ \"(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/functions/compare.js\")\n\n// Complex range `r1 || r2 || ...` is a subset of `R1 || R2 || ...` iff:\n// - Every simple range `r1, r2, ...` is a null set, OR\n// - Every simple range `r1, r2, ...` which is not a null set is a subset of\n//   some `R1, R2, ...`\n//\n// Simple range `c1 c2 ...` is a subset of simple range `C1 C2 ...` iff:\n// - If c is only the ANY comparator\n//   - If C is only the ANY comparator, return true\n//   - Else if in prerelease mode, return false\n//   - else replace c with `[>=0.0.0]`\n// - If C is only the ANY comparator\n//   - if in prerelease mode, return true\n//   - else replace C with `[>=0.0.0]`\n// - Let EQ be the set of = comparators in c\n// - If EQ is more than one, return true (null set)\n// - Let GT be the highest > or >= comparator in c\n// - Let LT be the lowest < or <= comparator in c\n// - If GT and LT, and GT.semver > LT.semver, return true (null set)\n// - If any C is a = range, and GT or LT are set, return false\n// - If EQ\n//   - If GT, and EQ does not satisfy GT, return true (null set)\n//   - If LT, and EQ does not satisfy LT, return true (null set)\n//   - If EQ satisfies every C, return true\n//   - Else return false\n// - If GT\n//   - If GT.semver is lower than any > or >= comp in C, return false\n//   - If GT is >=, and GT.semver does not satisfy every C, return false\n//   - If GT.semver has a prerelease, and not in prerelease mode\n//     - If no C has a prerelease and the GT.semver tuple, return false\n// - If LT\n//   - If LT.semver is greater than any < or <= comp in C, return false\n//   - If LT is <=, and LT.semver does not satisfy every C, return false\n//   - If GT.semver has a prerelease, and not in prerelease mode\n//     - If no C has a prerelease and the LT.semver tuple, return false\n// - Else return true\n\nconst subset = (sub, dom, options = {}) => {\n  if (sub === dom) {\n    return true\n  }\n\n  sub = new Range(sub, options)\n  dom = new Range(dom, options)\n  let sawNonNull = false\n\n  OUTER: for (const simpleSub of sub.set) {\n    for (const simpleDom of dom.set) {\n      const isSub = simpleSubset(simpleSub, simpleDom, options)\n      sawNonNull = sawNonNull || isSub !== null\n      if (isSub) {\n        continue OUTER\n      }\n    }\n    // the null set is a subset of everything, but null simple ranges in\n    // a complex range should be ignored.  so if we saw a non-null range,\n    // then we know this isn't a subset, but if EVERY simple range was null,\n    // then it is a subset.\n    if (sawNonNull) {\n      return false\n    }\n  }\n  return true\n}\n\nconst minimumVersionWithPreRelease = [new Comparator('>=0.0.0-0')]\nconst minimumVersion = [new Comparator('>=0.0.0')]\n\nconst simpleSubset = (sub, dom, options) => {\n  if (sub === dom) {\n    return true\n  }\n\n  if (sub.length === 1 && sub[0].semver === ANY) {\n    if (dom.length === 1 && dom[0].semver === ANY) {\n      return true\n    } else if (options.includePrerelease) {\n      sub = minimumVersionWithPreRelease\n    } else {\n      sub = minimumVersion\n    }\n  }\n\n  if (dom.length === 1 && dom[0].semver === ANY) {\n    if (options.includePrerelease) {\n      return true\n    } else {\n      dom = minimumVersion\n    }\n  }\n\n  const eqSet = new Set()\n  let gt, lt\n  for (const c of sub) {\n    if (c.operator === '>' || c.operator === '>=') {\n      gt = higherGT(gt, c, options)\n    } else if (c.operator === '<' || c.operator === '<=') {\n      lt = lowerLT(lt, c, options)\n    } else {\n      eqSet.add(c.semver)\n    }\n  }\n\n  if (eqSet.size > 1) {\n    return null\n  }\n\n  let gtltComp\n  if (gt && lt) {\n    gtltComp = compare(gt.semver, lt.semver, options)\n    if (gtltComp > 0) {\n      return null\n    } else if (gtltComp === 0 && (gt.operator !== '>=' || lt.operator !== '<=')) {\n      return null\n    }\n  }\n\n  // will iterate one or zero times\n  for (const eq of eqSet) {\n    if (gt && !satisfies(eq, String(gt), options)) {\n      return null\n    }\n\n    if (lt && !satisfies(eq, String(lt), options)) {\n      return null\n    }\n\n    for (const c of dom) {\n      if (!satisfies(eq, String(c), options)) {\n        return false\n      }\n    }\n\n    return true\n  }\n\n  let higher, lower\n  let hasDomLT, hasDomGT\n  // if the subset has a prerelease, we need a comparator in the superset\n  // with the same tuple and a prerelease, or it's not a subset\n  let needDomLTPre = lt &&\n    !options.includePrerelease &&\n    lt.semver.prerelease.length ? lt.semver : false\n  let needDomGTPre = gt &&\n    !options.includePrerelease &&\n    gt.semver.prerelease.length ? gt.semver : false\n  // exception: <1.2.3-0 is the same as <1.2.3\n  if (needDomLTPre && needDomLTPre.prerelease.length === 1 &&\n      lt.operator === '<' && needDomLTPre.prerelease[0] === 0) {\n    needDomLTPre = false\n  }\n\n  for (const c of dom) {\n    hasDomGT = hasDomGT || c.operator === '>' || c.operator === '>='\n    hasDomLT = hasDomLT || c.operator === '<' || c.operator === '<='\n    if (gt) {\n      if (needDomGTPre) {\n        if (c.semver.prerelease && c.semver.prerelease.length &&\n            c.semver.major === needDomGTPre.major &&\n            c.semver.minor === needDomGTPre.minor &&\n            c.semver.patch === needDomGTPre.patch) {\n          needDomGTPre = false\n        }\n      }\n      if (c.operator === '>' || c.operator === '>=') {\n        higher = higherGT(gt, c, options)\n        if (higher === c && higher !== gt) {\n          return false\n        }\n      } else if (gt.operator === '>=' && !satisfies(gt.semver, String(c), options)) {\n        return false\n      }\n    }\n    if (lt) {\n      if (needDomLTPre) {\n        if (c.semver.prerelease && c.semver.prerelease.length &&\n            c.semver.major === needDomLTPre.major &&\n            c.semver.minor === needDomLTPre.minor &&\n            c.semver.patch === needDomLTPre.patch) {\n          needDomLTPre = false\n        }\n      }\n      if (c.operator === '<' || c.operator === '<=') {\n        lower = lowerLT(lt, c, options)\n        if (lower === c && lower !== lt) {\n          return false\n        }\n      } else if (lt.operator === '<=' && !satisfies(lt.semver, String(c), options)) {\n        return false\n      }\n    }\n    if (!c.operator && (lt || gt) && gtltComp !== 0) {\n      return false\n    }\n  }\n\n  // if there was a < or >, and nothing in the dom, then must be false\n  // UNLESS it was limited by another range in the other direction.\n  // Eg, >1.0.0 <1.0.1 is still a subset of <2.0.0\n  if (gt && hasDomLT && !lt && gtltComp !== 0) {\n    return false\n  }\n\n  if (lt && hasDomGT && !gt && gtltComp !== 0) {\n    return false\n  }\n\n  // we needed a prerelease range in a specific tuple, but didn't get one\n  // then this isn't a subset.  eg >=1.2.3-pre is not a subset of >=1.0.0,\n  // because it includes prereleases in the 1.2.3 tuple\n  if (needDomGTPre || needDomLTPre) {\n    return false\n  }\n\n  return true\n}\n\n// >=1.2.3 is lower than >1.2.3\nconst higherGT = (a, b, options) => {\n  if (!a) {\n    return b\n  }\n  const comp = compare(a.semver, b.semver, options)\n  return comp > 0 ? a\n    : comp < 0 ? b\n    : b.operator === '>' && a.operator === '>=' ? b\n    : a\n}\n\n// <=1.2.3 is higher than <1.2.3\nconst lowerLT = (a, b, options) => {\n  if (!a) {\n    return b\n  }\n  const comp = compare(a.semver, b.semver, options)\n  return comp < 0 ? a\n    : comp > 0 ? b\n    : b.operator === '<' && a.operator === '<=' ? b\n    : a\n}\n\nmodule.exports = subset\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/ranges/subset.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/ranges/to-comparators.js":
/*!*************************************************************************************!*\
  !*** ./node_modules/@ledgerhq/devices/node_modules/semver/ranges/to-comparators.js ***!
  \*************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst Range = __webpack_require__(/*! ../classes/range */ \"(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/classes/range.js\")\n\n// Mostly just for testing and legacy API reasons\nconst toComparators = (range, options) =>\n  new Range(range, options).set\n    .map(comp => comp.map(c => c.value).join(' ').trim().split(' '))\n\nmodule.exports = toComparators\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQGxlZGdlcmhxL2RldmljZXMvbm9kZV9tb2R1bGVzL3NlbXZlci9yYW5nZXMvdG8tY29tcGFyYXRvcnMuanMiLCJtYXBwaW5ncyI6IkFBQVk7O0FBRVosY0FBYyxtQkFBTyxDQUFDLHFHQUFrQjs7QUFFeEM7QUFDQTtBQUNBO0FBQ0E7O0FBRUEiLCJzb3VyY2VzIjpbIi9Vc2Vycy94eW0vY29kZS90Ym9vay9vbnRvbl9jYy9vbmlvbi1sYXVuY2gvZnJvbnRlbmQvbm9kZV9tb2R1bGVzL0BsZWRnZXJocS9kZXZpY2VzL25vZGVfbW9kdWxlcy9zZW12ZXIvcmFuZ2VzL3RvLWNvbXBhcmF0b3JzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0J1xuXG5jb25zdCBSYW5nZSA9IHJlcXVpcmUoJy4uL2NsYXNzZXMvcmFuZ2UnKVxuXG4vLyBNb3N0bHkganVzdCBmb3IgdGVzdGluZyBhbmQgbGVnYWN5IEFQSSByZWFzb25zXG5jb25zdCB0b0NvbXBhcmF0b3JzID0gKHJhbmdlLCBvcHRpb25zKSA9PlxuICBuZXcgUmFuZ2UocmFuZ2UsIG9wdGlvbnMpLnNldFxuICAgIC5tYXAoY29tcCA9PiBjb21wLm1hcChjID0+IGMudmFsdWUpLmpvaW4oJyAnKS50cmltKCkuc3BsaXQoJyAnKSlcblxubW9kdWxlLmV4cG9ydHMgPSB0b0NvbXBhcmF0b3JzXG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/ranges/to-comparators.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/ranges/valid.js":
/*!****************************************************************************!*\
  !*** ./node_modules/@ledgerhq/devices/node_modules/semver/ranges/valid.js ***!
  \****************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst Range = __webpack_require__(/*! ../classes/range */ \"(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/classes/range.js\")\nconst validRange = (range, options) => {\n  try {\n    // Return '*' instead of '' so that truthiness works.\n    // This will throw if it's invalid anyway\n    return new Range(range, options).range || '*'\n  } catch (er) {\n    return null\n  }\n}\nmodule.exports = validRange\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQGxlZGdlcmhxL2RldmljZXMvbm9kZV9tb2R1bGVzL3NlbXZlci9yYW5nZXMvdmFsaWQuanMiLCJtYXBwaW5ncyI6IkFBQVk7O0FBRVosY0FBYyxtQkFBTyxDQUFDLHFHQUFrQjtBQUN4QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsSUFBSTtBQUNKO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyIvVXNlcnMveHltL2NvZGUvdGJvb2svb250b25fY2Mvb25pb24tbGF1bmNoL2Zyb250ZW5kL25vZGVfbW9kdWxlcy9AbGVkZ2VyaHEvZGV2aWNlcy9ub2RlX21vZHVsZXMvc2VtdmVyL3Jhbmdlcy92YWxpZC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCdcblxuY29uc3QgUmFuZ2UgPSByZXF1aXJlKCcuLi9jbGFzc2VzL3JhbmdlJylcbmNvbnN0IHZhbGlkUmFuZ2UgPSAocmFuZ2UsIG9wdGlvbnMpID0+IHtcbiAgdHJ5IHtcbiAgICAvLyBSZXR1cm4gJyonIGluc3RlYWQgb2YgJycgc28gdGhhdCB0cnV0aGluZXNzIHdvcmtzLlxuICAgIC8vIFRoaXMgd2lsbCB0aHJvdyBpZiBpdCdzIGludmFsaWQgYW55d2F5XG4gICAgcmV0dXJuIG5ldyBSYW5nZShyYW5nZSwgb3B0aW9ucykucmFuZ2UgfHwgJyonXG4gIH0gY2F0Y2ggKGVyKSB7XG4gICAgcmV0dXJuIG51bGxcbiAgfVxufVxubW9kdWxlLmV4cG9ydHMgPSB2YWxpZFJhbmdlXG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@ledgerhq/devices/node_modules/semver/ranges/valid.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@ledgerhq/errors/dist/index.js":
/*!*****************************************************!*\
  !*** ./node_modules/@ledgerhq/errors/dist/index.js ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AccountNameRequiredError: () => (/* binding */ AccountNameRequiredError),\n/* harmony export */   AccountNotSupported: () => (/* binding */ AccountNotSupported),\n/* harmony export */   AmountRequired: () => (/* binding */ AmountRequired),\n/* harmony export */   BluetoothRequired: () => (/* binding */ BluetoothRequired),\n/* harmony export */   BtcUnmatchedApp: () => (/* binding */ BtcUnmatchedApp),\n/* harmony export */   CantOpenDevice: () => (/* binding */ CantOpenDevice),\n/* harmony export */   CantScanQRCode: () => (/* binding */ CantScanQRCode),\n/* harmony export */   CashAddrNotSupported: () => (/* binding */ CashAddrNotSupported),\n/* harmony export */   CurrencyNotSupported: () => (/* binding */ CurrencyNotSupported),\n/* harmony export */   DBNotReset: () => (/* binding */ DBNotReset),\n/* harmony export */   DBWrongPassword: () => (/* binding */ DBWrongPassword),\n/* harmony export */   DeviceAppVerifyNotSupported: () => (/* binding */ DeviceAppVerifyNotSupported),\n/* harmony export */   DeviceGenuineSocketEarlyClose: () => (/* binding */ DeviceGenuineSocketEarlyClose),\n/* harmony export */   DeviceHalted: () => (/* binding */ DeviceHalted),\n/* harmony export */   DeviceInOSUExpected: () => (/* binding */ DeviceInOSUExpected),\n/* harmony export */   DeviceNameInvalid: () => (/* binding */ DeviceNameInvalid),\n/* harmony export */   DeviceNotGenuineError: () => (/* binding */ DeviceNotGenuineError),\n/* harmony export */   DeviceOnDashboardExpected: () => (/* binding */ DeviceOnDashboardExpected),\n/* harmony export */   DeviceOnDashboardUnexpected: () => (/* binding */ DeviceOnDashboardUnexpected),\n/* harmony export */   DeviceShouldStayInApp: () => (/* binding */ DeviceShouldStayInApp),\n/* harmony export */   DeviceSocketFail: () => (/* binding */ DeviceSocketFail),\n/* harmony export */   DeviceSocketNoBulkStatus: () => (/* binding */ DeviceSocketNoBulkStatus),\n/* harmony export */   DisconnectedDevice: () => (/* binding */ DisconnectedDevice),\n/* harmony export */   DisconnectedDeviceDuringOperation: () => (/* binding */ DisconnectedDeviceDuringOperation),\n/* harmony export */   ETHAddressNonEIP: () => (/* binding */ ETHAddressNonEIP),\n/* harmony export */   EnpointConfigError: () => (/* binding */ EnpointConfigError),\n/* harmony export */   EthAppPleaseEnableContractData: () => (/* binding */ EthAppPleaseEnableContractData),\n/* harmony export */   FeeEstimationFailed: () => (/* binding */ FeeEstimationFailed),\n/* harmony export */   FeeNotLoaded: () => (/* binding */ FeeNotLoaded),\n/* harmony export */   FeeRequired: () => (/* binding */ FeeRequired),\n/* harmony export */   FeeTooHigh: () => (/* binding */ FeeTooHigh),\n/* harmony export */   FirmwareNotRecognized: () => (/* binding */ FirmwareNotRecognized),\n/* harmony export */   FirmwareOrAppUpdateRequired: () => (/* binding */ FirmwareOrAppUpdateRequired),\n/* harmony export */   GasLessThanEstimate: () => (/* binding */ GasLessThanEstimate),\n/* harmony export */   GenuineCheckFailed: () => (/* binding */ GenuineCheckFailed),\n/* harmony export */   HardResetFail: () => (/* binding */ HardResetFail),\n/* harmony export */   InvalidAddress: () => (/* binding */ InvalidAddress),\n/* harmony export */   InvalidAddressBecauseDestinationIsAlsoSource: () => (/* binding */ InvalidAddressBecauseDestinationIsAlsoSource),\n/* harmony export */   InvalidXRPTag: () => (/* binding */ InvalidXRPTag),\n/* harmony export */   LatestMCUInstalledError: () => (/* binding */ LatestMCUInstalledError),\n/* harmony export */   LedgerAPI4xx: () => (/* binding */ LedgerAPI4xx),\n/* harmony export */   LedgerAPI5xx: () => (/* binding */ LedgerAPI5xx),\n/* harmony export */   LedgerAPIError: () => (/* binding */ LedgerAPIError),\n/* harmony export */   LedgerAPIErrorWithMessage: () => (/* binding */ LedgerAPIErrorWithMessage),\n/* harmony export */   LedgerAPINotAvailable: () => (/* binding */ LedgerAPINotAvailable),\n/* harmony export */   MCUNotGenuineToDashboard: () => (/* binding */ MCUNotGenuineToDashboard),\n/* harmony export */   ManagerAppAlreadyInstalledError: () => (/* binding */ ManagerAppAlreadyInstalledError),\n/* harmony export */   ManagerAppDepInstallRequired: () => (/* binding */ ManagerAppDepInstallRequired),\n/* harmony export */   ManagerAppDepUninstallRequired: () => (/* binding */ ManagerAppDepUninstallRequired),\n/* harmony export */   ManagerAppRelyOnBTCError: () => (/* binding */ ManagerAppRelyOnBTCError),\n/* harmony export */   ManagerDeviceLockedError: () => (/* binding */ ManagerDeviceLockedError),\n/* harmony export */   ManagerFirmwareNotEnoughSpaceError: () => (/* binding */ ManagerFirmwareNotEnoughSpaceError),\n/* harmony export */   ManagerNotEnoughSpaceError: () => (/* binding */ ManagerNotEnoughSpaceError),\n/* harmony export */   ManagerUninstallBTCDep: () => (/* binding */ ManagerUninstallBTCDep),\n/* harmony export */   NetworkDown: () => (/* binding */ NetworkDown),\n/* harmony export */   NoAccessToCamera: () => (/* binding */ NoAccessToCamera),\n/* harmony export */   NoAddressesFound: () => (/* binding */ NoAddressesFound),\n/* harmony export */   NoDBPathGiven: () => (/* binding */ NoDBPathGiven),\n/* harmony export */   NotEnoughBalance: () => (/* binding */ NotEnoughBalance),\n/* harmony export */   NotEnoughBalanceBecauseDestinationNotCreated: () => (/* binding */ NotEnoughBalanceBecauseDestinationNotCreated),\n/* harmony export */   NotEnoughBalanceInParentAccount: () => (/* binding */ NotEnoughBalanceInParentAccount),\n/* harmony export */   NotEnoughBalanceToDelegate: () => (/* binding */ NotEnoughBalanceToDelegate),\n/* harmony export */   NotEnoughGas: () => (/* binding */ NotEnoughGas),\n/* harmony export */   NotEnoughSpendableBalance: () => (/* binding */ NotEnoughSpendableBalance),\n/* harmony export */   NotSupportedLegacyAddress: () => (/* binding */ NotSupportedLegacyAddress),\n/* harmony export */   PairingFailed: () => (/* binding */ PairingFailed),\n/* harmony export */   PasswordIncorrectError: () => (/* binding */ PasswordIncorrectError),\n/* harmony export */   PasswordsDontMatchError: () => (/* binding */ PasswordsDontMatchError),\n/* harmony export */   RecipientRequired: () => (/* binding */ RecipientRequired),\n/* harmony export */   RecommendSubAccountsToEmpty: () => (/* binding */ RecommendSubAccountsToEmpty),\n/* harmony export */   RecommendUndelegation: () => (/* binding */ RecommendUndelegation),\n/* harmony export */   StatusCodes: () => (/* binding */ StatusCodes),\n/* harmony export */   SyncError: () => (/* binding */ SyncError),\n/* harmony export */   TimeoutTagged: () => (/* binding */ TimeoutTagged),\n/* harmony export */   TransportError: () => (/* binding */ TransportError),\n/* harmony export */   TransportInterfaceNotAvailable: () => (/* binding */ TransportInterfaceNotAvailable),\n/* harmony export */   TransportOpenUserCancelled: () => (/* binding */ TransportOpenUserCancelled),\n/* harmony export */   TransportRaceCondition: () => (/* binding */ TransportRaceCondition),\n/* harmony export */   TransportStatusError: () => (/* binding */ TransportStatusError),\n/* harmony export */   TransportWebUSBGestureRequired: () => (/* binding */ TransportWebUSBGestureRequired),\n/* harmony export */   UnavailableTezosOriginatedAccountReceive: () => (/* binding */ UnavailableTezosOriginatedAccountReceive),\n/* harmony export */   UnavailableTezosOriginatedAccountSend: () => (/* binding */ UnavailableTezosOriginatedAccountSend),\n/* harmony export */   UnexpectedBootloader: () => (/* binding */ UnexpectedBootloader),\n/* harmony export */   UnknownMCU: () => (/* binding */ UnknownMCU),\n/* harmony export */   UpdateFetchFileFail: () => (/* binding */ UpdateFetchFileFail),\n/* harmony export */   UpdateIncorrectHash: () => (/* binding */ UpdateIncorrectHash),\n/* harmony export */   UpdateIncorrectSig: () => (/* binding */ UpdateIncorrectSig),\n/* harmony export */   UpdateYourApp: () => (/* binding */ UpdateYourApp),\n/* harmony export */   UserRefusedAddress: () => (/* binding */ UserRefusedAddress),\n/* harmony export */   UserRefusedAllowManager: () => (/* binding */ UserRefusedAllowManager),\n/* harmony export */   UserRefusedDeviceNameChange: () => (/* binding */ UserRefusedDeviceNameChange),\n/* harmony export */   UserRefusedFirmwareUpdate: () => (/* binding */ UserRefusedFirmwareUpdate),\n/* harmony export */   UserRefusedOnDevice: () => (/* binding */ UserRefusedOnDevice),\n/* harmony export */   WebsocketConnectionError: () => (/* binding */ WebsocketConnectionError),\n/* harmony export */   WebsocketConnectionFailed: () => (/* binding */ WebsocketConnectionFailed),\n/* harmony export */   WrongAppForCurrency: () => (/* binding */ WrongAppForCurrency),\n/* harmony export */   WrongDeviceForAccount: () => (/* binding */ WrongDeviceForAccount),\n/* harmony export */   addCustomErrorDeserializer: () => (/* binding */ addCustomErrorDeserializer),\n/* harmony export */   createCustomErrorClass: () => (/* binding */ createCustomErrorClass),\n/* harmony export */   deserializeError: () => (/* binding */ deserializeError),\n/* harmony export */   getAltStatusMessage: () => (/* binding */ getAltStatusMessage),\n/* harmony export */   serializeError: () => (/* binding */ serializeError)\n/* harmony export */ });\n/* eslint-disable no-continue */\r\n/* eslint-disable no-unused-vars */\r\n/* eslint-disable no-param-reassign */\r\n/* eslint-disable no-prototype-builtins */\r\nvar errorClasses = {};\r\nvar deserializers = {};\r\nvar addCustomErrorDeserializer = function (name, deserializer) {\r\n    deserializers[name] = deserializer;\r\n};\r\nvar createCustomErrorClass = function (name) {\r\n    var C = function CustomError(message, fields) {\r\n        Object.assign(this, fields);\r\n        this.name = name;\r\n        this.message = message || name;\r\n        this.stack = new Error().stack;\r\n    };\r\n    C.prototype = new Error();\r\n    errorClasses[name] = C;\r\n    return C;\r\n};\r\n// inspired from https://github.com/programble/errio/blob/master/index.js\r\nvar deserializeError = function (object) {\r\n    if (typeof object === \"object\" && object) {\r\n        try {\r\n            // $FlowFixMe FIXME HACK\r\n            var msg = JSON.parse(object.message);\r\n            if (msg.message && msg.name) {\r\n                object = msg;\r\n            }\r\n        }\r\n        catch (e) {\r\n            // nothing\r\n        }\r\n        var error = void 0;\r\n        if (typeof object.name === \"string\") {\r\n            var name_1 = object.name;\r\n            var des = deserializers[name_1];\r\n            if (des) {\r\n                error = des(object);\r\n            }\r\n            else {\r\n                var constructor = name_1 === \"Error\" ? Error : errorClasses[name_1];\r\n                if (!constructor) {\r\n                    console.warn(\"deserializing an unknown class '\" + name_1 + \"'\");\r\n                    constructor = createCustomErrorClass(name_1);\r\n                }\r\n                error = Object.create(constructor.prototype);\r\n                try {\r\n                    for (var prop in object) {\r\n                        if (object.hasOwnProperty(prop)) {\r\n                            error[prop] = object[prop];\r\n                        }\r\n                    }\r\n                }\r\n                catch (e) {\r\n                    // sometimes setting a property can fail (e.g. .name)\r\n                }\r\n            }\r\n        }\r\n        else {\r\n            error = new Error(object.message);\r\n        }\r\n        if (!error.stack && Error.captureStackTrace) {\r\n            Error.captureStackTrace(error, deserializeError);\r\n        }\r\n        return error;\r\n    }\r\n    return new Error(String(object));\r\n};\r\n// inspired from https://github.com/sindresorhus/serialize-error/blob/master/index.js\r\nvar serializeError = function (value) {\r\n    if (!value)\r\n        return value;\r\n    if (typeof value === \"object\") {\r\n        return destroyCircular(value, []);\r\n    }\r\n    if (typeof value === \"function\") {\r\n        return \"[Function: \" + (value.name || \"anonymous\") + \"]\";\r\n    }\r\n    return value;\r\n};\r\n// https://www.npmjs.com/package/destroy-circular\r\nfunction destroyCircular(from, seen) {\r\n    var to = {};\r\n    seen.push(from);\r\n    for (var _i = 0, _a = Object.keys(from); _i < _a.length; _i++) {\r\n        var key = _a[_i];\r\n        var value = from[key];\r\n        if (typeof value === \"function\") {\r\n            continue;\r\n        }\r\n        if (!value || typeof value !== \"object\") {\r\n            to[key] = value;\r\n            continue;\r\n        }\r\n        if (seen.indexOf(from[key]) === -1) {\r\n            to[key] = destroyCircular(from[key], seen.slice(0));\r\n            continue;\r\n        }\r\n        to[key] = \"[Circular]\";\r\n    }\r\n    if (typeof from.name === \"string\") {\r\n        to.name = from.name;\r\n    }\r\n    if (typeof from.message === \"string\") {\r\n        to.message = from.message;\r\n    }\r\n    if (typeof from.stack === \"string\") {\r\n        to.stack = from.stack;\r\n    }\r\n    return to;\r\n}\n\nvar AccountNameRequiredError = createCustomErrorClass(\"AccountNameRequired\");\r\nvar AccountNotSupported = createCustomErrorClass(\"AccountNotSupported\");\r\nvar AmountRequired = createCustomErrorClass(\"AmountRequired\");\r\nvar BluetoothRequired = createCustomErrorClass(\"BluetoothRequired\");\r\nvar BtcUnmatchedApp = createCustomErrorClass(\"BtcUnmatchedApp\");\r\nvar CantOpenDevice = createCustomErrorClass(\"CantOpenDevice\");\r\nvar CashAddrNotSupported = createCustomErrorClass(\"CashAddrNotSupported\");\r\nvar CurrencyNotSupported = createCustomErrorClass(\"CurrencyNotSupported\");\r\nvar DeviceAppVerifyNotSupported = createCustomErrorClass(\"DeviceAppVerifyNotSupported\");\r\nvar DeviceGenuineSocketEarlyClose = createCustomErrorClass(\"DeviceGenuineSocketEarlyClose\");\r\nvar DeviceNotGenuineError = createCustomErrorClass(\"DeviceNotGenuine\");\r\nvar DeviceOnDashboardExpected = createCustomErrorClass(\"DeviceOnDashboardExpected\");\r\nvar DeviceOnDashboardUnexpected = createCustomErrorClass(\"DeviceOnDashboardUnexpected\");\r\nvar DeviceInOSUExpected = createCustomErrorClass(\"DeviceInOSUExpected\");\r\nvar DeviceHalted = createCustomErrorClass(\"DeviceHalted\");\r\nvar DeviceNameInvalid = createCustomErrorClass(\"DeviceNameInvalid\");\r\nvar DeviceSocketFail = createCustomErrorClass(\"DeviceSocketFail\");\r\nvar DeviceSocketNoBulkStatus = createCustomErrorClass(\"DeviceSocketNoBulkStatus\");\r\nvar DisconnectedDevice = createCustomErrorClass(\"DisconnectedDevice\");\r\nvar DisconnectedDeviceDuringOperation = createCustomErrorClass(\"DisconnectedDeviceDuringOperation\");\r\nvar EnpointConfigError = createCustomErrorClass(\"EnpointConfig\");\r\nvar EthAppPleaseEnableContractData = createCustomErrorClass(\"EthAppPleaseEnableContractData\");\r\nvar FeeEstimationFailed = createCustomErrorClass(\"FeeEstimationFailed\");\r\nvar FirmwareNotRecognized = createCustomErrorClass(\"FirmwareNotRecognized\");\r\nvar HardResetFail = createCustomErrorClass(\"HardResetFail\");\r\nvar InvalidXRPTag = createCustomErrorClass(\"InvalidXRPTag\");\r\nvar InvalidAddress = createCustomErrorClass(\"InvalidAddress\");\r\nvar InvalidAddressBecauseDestinationIsAlsoSource = createCustomErrorClass(\"InvalidAddressBecauseDestinationIsAlsoSource\");\r\nvar LatestMCUInstalledError = createCustomErrorClass(\"LatestMCUInstalledError\");\r\nvar UnknownMCU = createCustomErrorClass(\"UnknownMCU\");\r\nvar LedgerAPIError = createCustomErrorClass(\"LedgerAPIError\");\r\nvar LedgerAPIErrorWithMessage = createCustomErrorClass(\"LedgerAPIErrorWithMessage\");\r\nvar LedgerAPINotAvailable = createCustomErrorClass(\"LedgerAPINotAvailable\");\r\nvar ManagerAppAlreadyInstalledError = createCustomErrorClass(\"ManagerAppAlreadyInstalled\");\r\nvar ManagerAppRelyOnBTCError = createCustomErrorClass(\"ManagerAppRelyOnBTC\");\r\nvar ManagerAppDepInstallRequired = createCustomErrorClass(\"ManagerAppDepInstallRequired\");\r\nvar ManagerAppDepUninstallRequired = createCustomErrorClass(\"ManagerAppDepUninstallRequired\");\r\nvar ManagerDeviceLockedError = createCustomErrorClass(\"ManagerDeviceLocked\");\r\nvar ManagerFirmwareNotEnoughSpaceError = createCustomErrorClass(\"ManagerFirmwareNotEnoughSpace\");\r\nvar ManagerNotEnoughSpaceError = createCustomErrorClass(\"ManagerNotEnoughSpace\");\r\nvar ManagerUninstallBTCDep = createCustomErrorClass(\"ManagerUninstallBTCDep\");\r\nvar NetworkDown = createCustomErrorClass(\"NetworkDown\");\r\nvar NoAddressesFound = createCustomErrorClass(\"NoAddressesFound\");\r\nvar NotEnoughBalance = createCustomErrorClass(\"NotEnoughBalance\");\r\nvar NotEnoughBalanceToDelegate = createCustomErrorClass(\"NotEnoughBalanceToDelegate\");\r\nvar NotEnoughBalanceInParentAccount = createCustomErrorClass(\"NotEnoughBalanceInParentAccount\");\r\nvar NotEnoughSpendableBalance = createCustomErrorClass(\"NotEnoughSpendableBalance\");\r\nvar NotEnoughBalanceBecauseDestinationNotCreated = createCustomErrorClass(\"NotEnoughBalanceBecauseDestinationNotCreated\");\r\nvar NoAccessToCamera = createCustomErrorClass(\"NoAccessToCamera\");\r\nvar NotEnoughGas = createCustomErrorClass(\"NotEnoughGas\");\r\nvar NotSupportedLegacyAddress = createCustomErrorClass(\"NotSupportedLegacyAddress\");\r\nvar GasLessThanEstimate = createCustomErrorClass(\"GasLessThanEstimate\");\r\nvar PasswordsDontMatchError = createCustomErrorClass(\"PasswordsDontMatch\");\r\nvar PasswordIncorrectError = createCustomErrorClass(\"PasswordIncorrect\");\r\nvar RecommendSubAccountsToEmpty = createCustomErrorClass(\"RecommendSubAccountsToEmpty\");\r\nvar RecommendUndelegation = createCustomErrorClass(\"RecommendUndelegation\");\r\nvar TimeoutTagged = createCustomErrorClass(\"TimeoutTagged\");\r\nvar UnexpectedBootloader = createCustomErrorClass(\"UnexpectedBootloader\");\r\nvar MCUNotGenuineToDashboard = createCustomErrorClass(\"MCUNotGenuineToDashboard\");\r\nvar RecipientRequired = createCustomErrorClass(\"RecipientRequired\");\r\nvar UnavailableTezosOriginatedAccountReceive = createCustomErrorClass(\"UnavailableTezosOriginatedAccountReceive\");\r\nvar UnavailableTezosOriginatedAccountSend = createCustomErrorClass(\"UnavailableTezosOriginatedAccountSend\");\r\nvar UpdateFetchFileFail = createCustomErrorClass(\"UpdateFetchFileFail\");\r\nvar UpdateIncorrectHash = createCustomErrorClass(\"UpdateIncorrectHash\");\r\nvar UpdateIncorrectSig = createCustomErrorClass(\"UpdateIncorrectSig\");\r\nvar UpdateYourApp = createCustomErrorClass(\"UpdateYourApp\");\r\nvar UserRefusedDeviceNameChange = createCustomErrorClass(\"UserRefusedDeviceNameChange\");\r\nvar UserRefusedAddress = createCustomErrorClass(\"UserRefusedAddress\");\r\nvar UserRefusedFirmwareUpdate = createCustomErrorClass(\"UserRefusedFirmwareUpdate\");\r\nvar UserRefusedAllowManager = createCustomErrorClass(\"UserRefusedAllowManager\");\r\nvar UserRefusedOnDevice = createCustomErrorClass(\"UserRefusedOnDevice\"); // TODO rename because it's just for transaction refusal\r\nvar TransportOpenUserCancelled = createCustomErrorClass(\"TransportOpenUserCancelled\");\r\nvar TransportInterfaceNotAvailable = createCustomErrorClass(\"TransportInterfaceNotAvailable\");\r\nvar TransportRaceCondition = createCustomErrorClass(\"TransportRaceCondition\");\r\nvar TransportWebUSBGestureRequired = createCustomErrorClass(\"TransportWebUSBGestureRequired\");\r\nvar DeviceShouldStayInApp = createCustomErrorClass(\"DeviceShouldStayInApp\");\r\nvar WebsocketConnectionError = createCustomErrorClass(\"WebsocketConnectionError\");\r\nvar WebsocketConnectionFailed = createCustomErrorClass(\"WebsocketConnectionFailed\");\r\nvar WrongDeviceForAccount = createCustomErrorClass(\"WrongDeviceForAccount\");\r\nvar WrongAppForCurrency = createCustomErrorClass(\"WrongAppForCurrency\");\r\nvar ETHAddressNonEIP = createCustomErrorClass(\"ETHAddressNonEIP\");\r\nvar CantScanQRCode = createCustomErrorClass(\"CantScanQRCode\");\r\nvar FeeNotLoaded = createCustomErrorClass(\"FeeNotLoaded\");\r\nvar FeeRequired = createCustomErrorClass(\"FeeRequired\");\r\nvar FeeTooHigh = createCustomErrorClass(\"FeeTooHigh\");\r\nvar SyncError = createCustomErrorClass(\"SyncError\");\r\nvar PairingFailed = createCustomErrorClass(\"PairingFailed\");\r\nvar GenuineCheckFailed = createCustomErrorClass(\"GenuineCheckFailed\");\r\nvar LedgerAPI4xx = createCustomErrorClass(\"LedgerAPI4xx\");\r\nvar LedgerAPI5xx = createCustomErrorClass(\"LedgerAPI5xx\");\r\nvar FirmwareOrAppUpdateRequired = createCustomErrorClass(\"FirmwareOrAppUpdateRequired\");\r\n// db stuff, no need to translate\r\nvar NoDBPathGiven = createCustomErrorClass(\"NoDBPathGiven\");\r\nvar DBWrongPassword = createCustomErrorClass(\"DBWrongPassword\");\r\nvar DBNotReset = createCustomErrorClass(\"DBNotReset\");\r\n/**\r\n * TransportError is used for any generic transport errors.\r\n * e.g. Error thrown when data received by exchanges are incorrect or if exchanged failed to communicate with the device for various reason.\r\n */\r\nfunction TransportError(message, id) {\r\n    this.name = \"TransportError\";\r\n    this.message = message;\r\n    this.stack = new Error().stack;\r\n    this.id = id;\r\n}\r\nTransportError.prototype = new Error();\r\naddCustomErrorDeserializer(\"TransportError\", function (e) { return new TransportError(e.message, e.id); });\r\nvar StatusCodes = {\r\n    PIN_REMAINING_ATTEMPTS: 0x63c0,\r\n    INCORRECT_LENGTH: 0x6700,\r\n    MISSING_CRITICAL_PARAMETER: 0x6800,\r\n    COMMAND_INCOMPATIBLE_FILE_STRUCTURE: 0x6981,\r\n    SECURITY_STATUS_NOT_SATISFIED: 0x6982,\r\n    CONDITIONS_OF_USE_NOT_SATISFIED: 0x6985,\r\n    INCORRECT_DATA: 0x6a80,\r\n    NOT_ENOUGH_MEMORY_SPACE: 0x6a84,\r\n    REFERENCED_DATA_NOT_FOUND: 0x6a88,\r\n    FILE_ALREADY_EXISTS: 0x6a89,\r\n    INCORRECT_P1_P2: 0x6b00,\r\n    INS_NOT_SUPPORTED: 0x6d00,\r\n    CLA_NOT_SUPPORTED: 0x6e00,\r\n    TECHNICAL_PROBLEM: 0x6f00,\r\n    OK: 0x9000,\r\n    MEMORY_PROBLEM: 0x9240,\r\n    NO_EF_SELECTED: 0x9400,\r\n    INVALID_OFFSET: 0x9402,\r\n    FILE_NOT_FOUND: 0x9404,\r\n    INCONSISTENT_FILE: 0x9408,\r\n    ALGORITHM_NOT_SUPPORTED: 0x9484,\r\n    INVALID_KCV: 0x9485,\r\n    CODE_NOT_INITIALIZED: 0x9802,\r\n    ACCESS_CONDITION_NOT_FULFILLED: 0x9804,\r\n    CONTRADICTION_SECRET_CODE_STATUS: 0x9808,\r\n    CONTRADICTION_INVALIDATION: 0x9810,\r\n    CODE_BLOCKED: 0x9840,\r\n    MAX_VALUE_REACHED: 0x9850,\r\n    GP_AUTH_FAILED: 0x6300,\r\n    LICENSING: 0x6f42,\r\n    HALTED: 0x6faa,\r\n};\r\nfunction getAltStatusMessage(code) {\r\n    switch (code) {\r\n        // improve text of most common errors\r\n        case 0x6700:\r\n            return \"Incorrect length\";\r\n        case 0x6800:\r\n            return \"Missing critical parameter\";\r\n        case 0x6982:\r\n            return \"Security not satisfied (dongle locked or have invalid access rights)\";\r\n        case 0x6985:\r\n            return \"Condition of use not satisfied (denied by the user?)\";\r\n        case 0x6a80:\r\n            return \"Invalid data received\";\r\n        case 0x6b00:\r\n            return \"Invalid parameter received\";\r\n    }\r\n    if (0x6f00 <= code && code <= 0x6fff) {\r\n        return \"Internal error, please report\";\r\n    }\r\n}\r\n/**\r\n * Error thrown when a device returned a non success status.\r\n * the error.statusCode is one of the `StatusCodes` exported by this library.\r\n */\r\nfunction TransportStatusError(statusCode) {\r\n    this.name = \"TransportStatusError\";\r\n    var statusText = Object.keys(StatusCodes).find(function (k) { return StatusCodes[k] === statusCode; }) ||\r\n        \"UNKNOWN_ERROR\";\r\n    var smsg = getAltStatusMessage(statusCode) || statusText;\r\n    var statusCodeStr = statusCode.toString(16);\r\n    this.message = \"Ledger device: \" + smsg + \" (0x\" + statusCodeStr + \")\";\r\n    this.stack = new Error().stack;\r\n    this.statusCode = statusCode;\r\n    this.statusText = statusText;\r\n}\r\nTransportStatusError.prototype = new Error();\r\naddCustomErrorDeserializer(\"TransportStatusError\", function (e) { return new TransportStatusError(e.statusCode); });\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@ledgerhq/errors/dist/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@ledgerhq/hw-transport-web-ble/lib-es/TransportWebBLE.js":
/*!*******************************************************************************!*\
  !*** ./node_modules/@ledgerhq/hw-transport-web-ble/lib-es/TransportWebBLE.js ***!
  \*******************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ BluetoothTransport)\n/* harmony export */ });\n/* harmony import */ var _ledgerhq_hw_transport__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @ledgerhq/hw-transport */ \"(ssr)/./node_modules/@ledgerhq/hw-transport/lib-es/Transport.js\");\n/* harmony import */ var _ledgerhq_errors__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @ledgerhq/errors */ \"(ssr)/./node_modules/@ledgerhq/errors/dist/index.js\");\n/* harmony import */ var _ledgerhq_devices__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @ledgerhq/devices */ \"(ssr)/./node_modules/@ledgerhq/devices/lib-es/index.js\");\n/* harmony import */ var _ledgerhq_devices_lib_ble_sendAPDU__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @ledgerhq/devices/lib/ble/sendAPDU */ \"(ssr)/./node_modules/@ledgerhq/devices/lib/ble/sendAPDU.js\");\n/* harmony import */ var _ledgerhq_devices_lib_ble_receiveAPDU__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @ledgerhq/devices/lib/ble/receiveAPDU */ \"(ssr)/./node_modules/@ledgerhq/devices/lib/ble/receiveAPDU.js\");\n/* harmony import */ var _ledgerhq_logs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @ledgerhq/logs */ \"(ssr)/./node_modules/@ledgerhq/logs/lib-es/index.js\");\n/* harmony import */ var rxjs__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! rxjs */ \"(ssr)/./node_modules/rxjs/_esm5/internal/Observable.js\");\n/* harmony import */ var rxjs__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! rxjs */ \"(ssr)/./node_modules/rxjs/_esm5/internal/observable/merge.js\");\n/* harmony import */ var rxjs__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! rxjs */ \"(ssr)/./node_modules/rxjs/_esm5/internal/observable/defer.js\");\n/* harmony import */ var rxjs__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! rxjs */ \"(ssr)/./node_modules/rxjs/_esm5/internal/observable/from.js\");\n/* harmony import */ var rxjs_operators__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! rxjs/operators */ \"(ssr)/./node_modules/rxjs/_esm5/internal/operators/tap.js\");\n/* harmony import */ var rxjs_operators__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! rxjs/operators */ \"(ssr)/./node_modules/rxjs/_esm5/internal/operators/share.js\");\n/* harmony import */ var rxjs_operators__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! rxjs/operators */ \"(ssr)/./node_modules/rxjs/_esm5/internal/operators/first.js\");\n/* harmony import */ var rxjs_operators__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! rxjs/operators */ \"(ssr)/./node_modules/rxjs/_esm5/internal/operators/map.js\");\n/* harmony import */ var rxjs_operators__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! rxjs/operators */ \"(ssr)/./node_modules/rxjs/_esm5/internal/operators/ignoreElements.js\");\n/* harmony import */ var _monitorCharacteristic__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./monitorCharacteristic */ \"(ssr)/./node_modules/@ledgerhq/hw-transport-web-ble/lib-es/monitorCharacteristic.js\");\n/* eslint-disable prefer-template */\n\n\n\n\n\n\n\n\n\n\nconst requiresBluetooth = () => {\n  // $FlowFixMe\n  const {\n    bluetooth\n  } = navigator;\n\n  if (typeof bluetooth === \"undefined\") {\n    throw new Error(\"web bluetooth not supported\");\n  }\n\n  return bluetooth;\n};\n\nconst availability = () => rxjs__WEBPACK_IMPORTED_MODULE_7__.Observable.create(observer => {\n  const bluetooth = requiresBluetooth();\n\n  const onAvailabilityChanged = e => {\n    observer.next(e.value);\n  };\n\n  bluetooth.addEventListener(\"availabilitychanged\", onAvailabilityChanged);\n  let unsubscribed = false;\n  bluetooth.getAvailability().then(available => {\n    if (!unsubscribed) {\n      observer.next(available);\n    }\n  });\n  return () => {\n    unsubscribed = true;\n    bluetooth.removeEventListener(\"availabilitychanged\", onAvailabilityChanged);\n  };\n});\n\nconst transportsCache = {};\n\nconst requestDeviceParam = () => ({\n  filters: (0,_ledgerhq_devices__WEBPACK_IMPORTED_MODULE_2__.getBluetoothServiceUuids)().map(uuid => ({\n    services: [uuid]\n  }))\n});\n\nconst retrieveService = async device => {\n  if (!device.gatt) throw new Error(\"bluetooth gatt not found\");\n  const [service] = await device.gatt.getPrimaryServices();\n  if (!service) throw new Error(\"bluetooth service not found\");\n  const infos = (0,_ledgerhq_devices__WEBPACK_IMPORTED_MODULE_2__.getInfosForServiceUuid)(service.uuid);\n  if (!infos) throw new Error(\"bluetooth service infos not found\");\n  return [service, infos];\n};\n\nasync function open(deviceOrId, needsReconnect) {\n  let device;\n\n  if (typeof deviceOrId === \"string\") {\n    if (transportsCache[deviceOrId]) {\n      (0,_ledgerhq_logs__WEBPACK_IMPORTED_MODULE_5__.log)(\"ble-verbose\", \"Transport in cache, using that.\");\n      return transportsCache[deviceOrId];\n    }\n\n    const bluetooth = requiresBluetooth(); // TODO instead we should \"query\" the device by its ID\n\n    device = await bluetooth.requestDevice(requestDeviceParam());\n  } else {\n    device = deviceOrId;\n  }\n\n  if (!device.gatt.connected) {\n    (0,_ledgerhq_logs__WEBPACK_IMPORTED_MODULE_5__.log)(\"ble-verbose\", \"not connected. connecting...\");\n    await device.gatt.connect();\n  }\n\n  const [service, infos] = await retrieveService(device);\n  const {\n    deviceModel,\n    writeUuid,\n    notifyUuid\n  } = infos;\n  const [writeC, notifyC] = await Promise.all([service.getCharacteristic(writeUuid), service.getCharacteristic(notifyUuid)]);\n  const notifyObservable = (0,_monitorCharacteristic__WEBPACK_IMPORTED_MODULE_6__.monitorCharacteristic)(notifyC).pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_8__.tap)(value => {\n    (0,_ledgerhq_logs__WEBPACK_IMPORTED_MODULE_5__.log)(\"ble-frame\", \"<= \" + value.toString(\"hex\"));\n  }), (0,rxjs_operators__WEBPACK_IMPORTED_MODULE_9__.share)());\n  const notif = notifyObservable.subscribe();\n  const transport = new BluetoothTransport(device, writeC, notifyObservable, deviceModel);\n\n  if (!device.gatt.connected) {\n    throw new _ledgerhq_errors__WEBPACK_IMPORTED_MODULE_1__.DisconnectedDevice();\n  } // eslint-disable-next-line require-atomic-updates\n\n\n  transportsCache[transport.id] = transport;\n\n  const onDisconnect = e => {\n    console.log(\"onDisconnect!\", e);\n    delete transportsCache[transport.id];\n    transport.notYetDisconnected = false;\n    notif.unsubscribe();\n    device.removeEventListener(\"gattserverdisconnected\", onDisconnect);\n    (0,_ledgerhq_logs__WEBPACK_IMPORTED_MODULE_5__.log)(\"ble-verbose\", `BleTransport(${transport.id}) disconnected`);\n    transport.emit(\"disconnect\", e);\n  };\n\n  device.addEventListener(\"gattserverdisconnected\", onDisconnect);\n  let beforeMTUTime = Date.now();\n\n  try {\n    await transport.inferMTU();\n  } finally {\n    let afterMTUTime = Date.now(); // workaround for #279: we need to open() again if we come the first time here,\n    // to make sure we do a disconnect() after the first pairing time\n    // because of a firmware bug\n\n    if (afterMTUTime - beforeMTUTime < 1000) {\n      needsReconnect = false; // (optim) there is likely no new pairing done because mtu answer was fast.\n    }\n\n    if (needsReconnect) {\n      await device.gatt.disconnect(); // necessary time for the bonding workaround\n\n      await new Promise(s => setTimeout(s, 4000));\n    }\n  }\n\n  if (needsReconnect) {\n    return open(device, false);\n  }\n\n  return transport;\n}\n/**\n * react-native bluetooth BLE implementation\n * @example\n * import BluetoothTransport from \"@ledgerhq/hw-transport-web-ble\";\n */\n\n\nclass BluetoothTransport extends _ledgerhq_hw_transport__WEBPACK_IMPORTED_MODULE_0__[\"default\"] {\n  /**\n   * observe event with { available: bool, type: string }\n   * (available is generic, type is specific)\n   * an event is emit once and then each time it changes\n   */\n\n  /**\n   * Scan for Ledger Bluetooth devices.\n   * On this web implementation, it only emits ONE device, the one that was selected in the UI (if any).\n   */\n  static listen(observer) {\n    (0,_ledgerhq_logs__WEBPACK_IMPORTED_MODULE_5__.log)(\"ble-verbose\", \"listen...\");\n    let unsubscribed;\n    const bluetooth = requiresBluetooth();\n    bluetooth.requestDevice(requestDeviceParam()).then(async device => {\n      if (!unsubscribed) {\n        observer.next({\n          type: \"add\",\n          descriptor: device\n        });\n        observer.complete();\n      }\n    }, error => {\n      observer.error(new _ledgerhq_errors__WEBPACK_IMPORTED_MODULE_1__.TransportOpenUserCancelled(error.message));\n    });\n\n    function unsubscribe() {\n      unsubscribed = true;\n    }\n\n    return {\n      unsubscribe\n    };\n  }\n  /**\n   * open a bluetooth device.\n   */\n\n\n  static async open(deviceOrId) {\n    return open(deviceOrId, true);\n  }\n  /**\n   * globally disconnect a bluetooth device by its id.\n   */\n\n\n  constructor(device, writeCharacteristic, notifyObservable, deviceModel) {\n    super();\n    this.id = void 0;\n    this.device = void 0;\n    this.mtuSize = 20;\n    this.writeCharacteristic = void 0;\n    this.notifyObservable = void 0;\n    this.notYetDisconnected = true;\n    this.deviceModel = void 0;\n\n    this.exchange = apdu => this.exchangeAtomicImpl(async () => {\n      try {\n        const msgIn = apdu.toString(\"hex\");\n        (0,_ledgerhq_logs__WEBPACK_IMPORTED_MODULE_5__.log)(\"apdu\", `=> ${msgIn}`);\n        const data = await (0,rxjs__WEBPACK_IMPORTED_MODULE_10__.merge)(this.notifyObservable.pipe(_ledgerhq_devices_lib_ble_receiveAPDU__WEBPACK_IMPORTED_MODULE_4__.receiveAPDU), (0,_ledgerhq_devices_lib_ble_sendAPDU__WEBPACK_IMPORTED_MODULE_3__.sendAPDU)(this.write, apdu, this.mtuSize)).toPromise();\n        const msgOut = data.toString(\"hex\");\n        (0,_ledgerhq_logs__WEBPACK_IMPORTED_MODULE_5__.log)(\"apdu\", `<= ${msgOut}`);\n        return data;\n      } catch (e) {\n        (0,_ledgerhq_logs__WEBPACK_IMPORTED_MODULE_5__.log)(\"ble-error\", \"exchange got \" + String(e));\n\n        if (this.notYetDisconnected) {\n          // in such case we will always disconnect because something is bad.\n          this.device.gatt.disconnect();\n        }\n\n        throw e;\n      }\n    });\n\n    this.write = async buffer => {\n      (0,_ledgerhq_logs__WEBPACK_IMPORTED_MODULE_5__.log)(\"ble-frame\", \"=> \" + buffer.toString(\"hex\"));\n      await this.writeCharacteristic.writeValue(buffer);\n    };\n\n    this.id = device.id;\n    this.device = device;\n    this.writeCharacteristic = writeCharacteristic;\n    this.notifyObservable = notifyObservable;\n    this.deviceModel = deviceModel;\n    (0,_ledgerhq_logs__WEBPACK_IMPORTED_MODULE_5__.log)(\"ble-verbose\", `BleTransport(${String(this.id)}) new instance`);\n  }\n\n  async inferMTU() {\n    let mtu = 23;\n    await this.exchangeAtomicImpl(async () => {\n      try {\n        mtu = (await (0,rxjs__WEBPACK_IMPORTED_MODULE_10__.merge)(this.notifyObservable.pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_11__.first)(buffer => buffer.readUInt8(0) === 0x08), (0,rxjs_operators__WEBPACK_IMPORTED_MODULE_12__.map)(buffer => buffer.readUInt8(5))), (0,rxjs__WEBPACK_IMPORTED_MODULE_13__.defer)(() => (0,rxjs__WEBPACK_IMPORTED_MODULE_14__.from)(this.write(Buffer.from([0x08, 0, 0, 0, 0])))).pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_15__.ignoreElements)())).toPromise()) + 3;\n      } catch (e) {\n        (0,_ledgerhq_logs__WEBPACK_IMPORTED_MODULE_5__.log)(\"ble-error\", \"inferMTU got \" + String(e));\n        this.device.gatt.disconnect();\n        throw e;\n      }\n    });\n\n    if (mtu > 23) {\n      const mtuSize = mtu - 3;\n      (0,_ledgerhq_logs__WEBPACK_IMPORTED_MODULE_5__.log)(\"ble-verbose\", `BleTransport(${String(this.id)}) mtu set to ${String(mtuSize)}`);\n      this.mtuSize = mtuSize;\n    }\n\n    return this.mtuSize;\n  }\n  /**\n   * Exchange with the device using APDU protocol.\n   * @param apdu\n   * @returns a promise of apdu response\n   */\n\n\n  setScrambleKey() {}\n\n  async close() {\n    if (this.exchangeBusyPromise) {\n      await this.exchangeBusyPromise;\n    }\n  }\n\n}\n\nBluetoothTransport.isSupported = () => Promise.resolve().then(requiresBluetooth).then(() => true, () => false);\n\nBluetoothTransport.observeAvailability = observer => availability.subscribe(observer);\n\nBluetoothTransport.list = () => Promise.resolve([]);\n\nBluetoothTransport.disconnect = async id => {\n  (0,_ledgerhq_logs__WEBPACK_IMPORTED_MODULE_5__.log)(\"ble-verbose\", `user disconnect(${id})`);\n  const transport = transportsCache[id];\n\n  if (transport) {\n    transport.device.gatt.disconnect();\n  }\n};\n//# sourceMappingURL=TransportWebBLE.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@ledgerhq/hw-transport-web-ble/lib-es/TransportWebBLE.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@ledgerhq/hw-transport-web-ble/lib-es/monitorCharacteristic.js":
/*!*************************************************************************************!*\
  !*** ./node_modules/@ledgerhq/hw-transport-web-ble/lib-es/monitorCharacteristic.js ***!
  \*************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   monitorCharacteristic: () => (/* binding */ monitorCharacteristic)\n/* harmony export */ });\n/* harmony import */ var rxjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! rxjs */ \"(ssr)/./node_modules/rxjs/_esm5/internal/Observable.js\");\n/* harmony import */ var _ledgerhq_logs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @ledgerhq/logs */ \"(ssr)/./node_modules/@ledgerhq/logs/lib-es/index.js\");\n\n\nconst monitorCharacteristic = characteristic => rxjs__WEBPACK_IMPORTED_MODULE_1__.Observable.create(o => {\n  (0,_ledgerhq_logs__WEBPACK_IMPORTED_MODULE_0__.log)(\"ble-verbose\", \"start monitor \" + characteristic.uuid);\n\n  function onCharacteristicValueChanged(event) {\n    const characteristic = event.target;\n\n    if (characteristic.value) {\n      o.next(Buffer.from(characteristic.value.buffer));\n    }\n  }\n\n  characteristic.startNotifications().then(() => {\n    characteristic.addEventListener(\"characteristicvaluechanged\", onCharacteristicValueChanged);\n  });\n  return () => {\n    (0,_ledgerhq_logs__WEBPACK_IMPORTED_MODULE_0__.log)(\"ble-verbose\", \"end monitor \" + characteristic.uuid);\n    characteristic.stopNotifications();\n  };\n});\n//# sourceMappingURL=monitorCharacteristic.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQGxlZGdlcmhxL2h3LXRyYW5zcG9ydC13ZWItYmxlL2xpYi1lcy9tb25pdG9yQ2hhcmFjdGVyaXN0aWMuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQWtDO0FBQ0c7QUFDOUIsZ0RBQWdELDRDQUFVO0FBQ2pFLEVBQUUsbURBQUc7O0FBRUw7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0EsSUFBSSxtREFBRztBQUNQO0FBQ0E7QUFDQSxDQUFDO0FBQ0QiLCJzb3VyY2VzIjpbIi9Vc2Vycy94eW0vY29kZS90Ym9vay9vbnRvbl9jYy9vbmlvbi1sYXVuY2gvZnJvbnRlbmQvbm9kZV9tb2R1bGVzL0BsZWRnZXJocS9ody10cmFuc3BvcnQtd2ViLWJsZS9saWItZXMvbW9uaXRvckNoYXJhY3RlcmlzdGljLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IE9ic2VydmFibGUgfSBmcm9tIFwicnhqc1wiO1xuaW1wb3J0IHsgbG9nIH0gZnJvbSBcIkBsZWRnZXJocS9sb2dzXCI7XG5leHBvcnQgY29uc3QgbW9uaXRvckNoYXJhY3RlcmlzdGljID0gY2hhcmFjdGVyaXN0aWMgPT4gT2JzZXJ2YWJsZS5jcmVhdGUobyA9PiB7XG4gIGxvZyhcImJsZS12ZXJib3NlXCIsIFwic3RhcnQgbW9uaXRvciBcIiArIGNoYXJhY3RlcmlzdGljLnV1aWQpO1xuXG4gIGZ1bmN0aW9uIG9uQ2hhcmFjdGVyaXN0aWNWYWx1ZUNoYW5nZWQoZXZlbnQpIHtcbiAgICBjb25zdCBjaGFyYWN0ZXJpc3RpYyA9IGV2ZW50LnRhcmdldDtcblxuICAgIGlmIChjaGFyYWN0ZXJpc3RpYy52YWx1ZSkge1xuICAgICAgby5uZXh0KEJ1ZmZlci5mcm9tKGNoYXJhY3RlcmlzdGljLnZhbHVlLmJ1ZmZlcikpO1xuICAgIH1cbiAgfVxuXG4gIGNoYXJhY3RlcmlzdGljLnN0YXJ0Tm90aWZpY2F0aW9ucygpLnRoZW4oKCkgPT4ge1xuICAgIGNoYXJhY3RlcmlzdGljLmFkZEV2ZW50TGlzdGVuZXIoXCJjaGFyYWN0ZXJpc3RpY3ZhbHVlY2hhbmdlZFwiLCBvbkNoYXJhY3RlcmlzdGljVmFsdWVDaGFuZ2VkKTtcbiAgfSk7XG4gIHJldHVybiAoKSA9PiB7XG4gICAgbG9nKFwiYmxlLXZlcmJvc2VcIiwgXCJlbmQgbW9uaXRvciBcIiArIGNoYXJhY3RlcmlzdGljLnV1aWQpO1xuICAgIGNoYXJhY3RlcmlzdGljLnN0b3BOb3RpZmljYXRpb25zKCk7XG4gIH07XG59KTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPW1vbml0b3JDaGFyYWN0ZXJpc3RpYy5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@ledgerhq/hw-transport-web-ble/lib-es/monitorCharacteristic.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@ledgerhq/hw-transport-webhid/lib-es/TransportWebHID.js":
/*!******************************************************************************!*\
  !*** ./node_modules/@ledgerhq/hw-transport-webhid/lib-es/TransportWebHID.js ***!
  \******************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TransportWebHID)\n/* harmony export */ });\n/* harmony import */ var _ledgerhq_hw_transport__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @ledgerhq/hw-transport */ \"(ssr)/./node_modules/@ledgerhq/hw-transport/lib-es/Transport.js\");\n/* harmony import */ var _ledgerhq_devices_lib_hid_framing__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @ledgerhq/devices/lib/hid-framing */ \"(ssr)/./node_modules/@ledgerhq/devices/lib/hid-framing.js\");\n/* harmony import */ var _ledgerhq_devices__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @ledgerhq/devices */ \"(ssr)/./node_modules/@ledgerhq/devices/lib-es/index.js\");\n/* harmony import */ var _ledgerhq_logs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @ledgerhq/logs */ \"(ssr)/./node_modules/@ledgerhq/logs/lib-es/index.js\");\n/* harmony import */ var _ledgerhq_errors__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @ledgerhq/errors */ \"(ssr)/./node_modules/@ledgerhq/errors/dist/index.js\");\n\n\n\n\n\nconst ledgerDevices = [{\n  vendorId: _ledgerhq_devices__WEBPACK_IMPORTED_MODULE_2__.ledgerUSBVendorId\n}];\n\nconst isSupported = () => Promise.resolve(!!(global.navigator && global.navigator.hid));\n\nconst getHID = () => {\n  // $FlowFixMe\n  const {\n    hid\n  } = navigator;\n  if (!hid) throw new _ledgerhq_errors__WEBPACK_IMPORTED_MODULE_4__.TransportError(\"navigator.hid is not supported\", \"HIDNotSupported\");\n  return hid;\n};\n\nasync function requestLedgerDevices() {\n  const device = await getHID().requestDevice({\n    filters: ledgerDevices\n  });\n  if (Array.isArray(device)) return device;\n  return [device];\n}\n\nasync function getLedgerDevices() {\n  const devices = await getHID().getDevices();\n  return devices.filter(d => d.vendorId === _ledgerhq_devices__WEBPACK_IMPORTED_MODULE_2__.ledgerUSBVendorId);\n}\n\nasync function getFirstLedgerDevice() {\n  const existingDevices = await getLedgerDevices();\n  if (existingDevices.length > 0) return existingDevices[0];\n  const devices = await requestLedgerDevices();\n  return devices[0];\n}\n/**\n * WebHID Transport implementation\n * @example\n * import TransportWebHID from \"@ledgerhq/hw-transport-webhid\";\n * ...\n * TransportWebHID.create().then(transport => ...)\n */\n\n\nclass TransportWebHID extends _ledgerhq_hw_transport__WEBPACK_IMPORTED_MODULE_0__[\"default\"] {\n  constructor(device) {\n    super();\n    this.device = void 0;\n    this.deviceModel = void 0;\n    this.channel = Math.floor(Math.random() * 0xffff);\n    this.packetSize = 64;\n    this.inputs = [];\n    this.inputCallback = void 0;\n\n    this.read = () => {\n      if (this.inputs.length) {\n        return Promise.resolve(this.inputs.shift());\n      }\n\n      return new Promise(success => {\n        this.inputCallback = success;\n      });\n    };\n\n    this.onInputReport = e => {\n      const buffer = Buffer.from(e.data.buffer);\n\n      if (this.inputCallback) {\n        this.inputCallback(buffer);\n        this.inputCallback = null;\n      } else {\n        this.inputs.push(buffer);\n      }\n    };\n\n    this._disconnectEmitted = false;\n\n    this._emitDisconnect = e => {\n      if (this._disconnectEmitted) return;\n      this._disconnectEmitted = true;\n      this.emit(\"disconnect\", e);\n    };\n\n    this.exchange = apdu => this.exchangeAtomicImpl(async () => {\n      const {\n        channel,\n        packetSize\n      } = this;\n      (0,_ledgerhq_logs__WEBPACK_IMPORTED_MODULE_3__.log)(\"apdu\", \"=> \" + apdu.toString(\"hex\"));\n      const framing = (0,_ledgerhq_devices_lib_hid_framing__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(channel, packetSize); // Write...\n\n      const blocks = framing.makeBlocks(apdu);\n\n      for (let i = 0; i < blocks.length; i++) {\n        await this.device.sendReport(0, blocks[i]);\n      } // Read...\n\n\n      let result;\n      let acc;\n\n      while (!(result = framing.getReducedResult(acc))) {\n        const buffer = await this.read();\n        acc = framing.reduceResponse(acc, buffer);\n      }\n\n      (0,_ledgerhq_logs__WEBPACK_IMPORTED_MODULE_3__.log)(\"apdu\", \"<= \" + result.toString(\"hex\"));\n      return result;\n    }).catch(e => {\n      if (e && e.message && e.message.includes(\"write\")) {\n        this._emitDisconnect(e);\n\n        throw new _ledgerhq_errors__WEBPACK_IMPORTED_MODULE_4__.DisconnectedDeviceDuringOperation(e.message);\n      }\n\n      throw e;\n    });\n\n    this.device = device;\n    this.deviceModel = (0,_ledgerhq_devices__WEBPACK_IMPORTED_MODULE_2__.identifyUSBProductId)(device.productId);\n    device.addEventListener(\"inputreport\", this.onInputReport);\n  }\n\n  /**\n   * Similar to create() except it will always display the device permission (even if some devices are already accepted).\n   */\n  static async request() {\n    const [device] = await requestLedgerDevices();\n    return TransportWebHID.open(device);\n  }\n  /**\n   * Similar to create() except it will never display the device permission (it returns a Promise<?Transport>, null if it fails to find a device).\n   */\n\n\n  static async openConnected() {\n    const devices = await getLedgerDevices();\n    if (devices.length === 0) return null;\n    return TransportWebHID.open(devices[0]);\n  }\n  /**\n   * Create a Ledger transport with a HIDDevice\n   */\n\n\n  static async open(device) {\n    await device.open();\n    const transport = new TransportWebHID(device);\n\n    const onDisconnect = e => {\n      if (device === e.device) {\n        getHID().removeEventListener(\"disconnect\", onDisconnect);\n\n        transport._emitDisconnect(new _ledgerhq_errors__WEBPACK_IMPORTED_MODULE_4__.DisconnectedDevice());\n      }\n    };\n\n    getHID().addEventListener(\"disconnect\", onDisconnect);\n    return transport;\n  }\n\n  /**\n   * Release the transport device\n   */\n  async close() {\n    await this.exchangeBusyPromise;\n    this.device.removeEventListener(\"inputreport\", this.onInputReport);\n    await this.device.close();\n  }\n  /**\n   * Exchange with the device using APDU protocol.\n   * @param apdu\n   * @returns a promise of apdu response\n   */\n\n\n  setScrambleKey() {}\n\n}\nTransportWebHID.isSupported = isSupported;\nTransportWebHID.list = getLedgerDevices;\n\nTransportWebHID.listen = observer => {\n  let unsubscribed = false;\n  getFirstLedgerDevice().then(device => {\n    if (!device) {\n      observer.error(new _ledgerhq_errors__WEBPACK_IMPORTED_MODULE_4__.TransportOpenUserCancelled(\"Access denied to use Ledger device\"));\n    } else if (!unsubscribed) {\n      const deviceModel = (0,_ledgerhq_devices__WEBPACK_IMPORTED_MODULE_2__.identifyUSBProductId)(device.productId);\n      observer.next({\n        type: \"add\",\n        descriptor: device,\n        deviceModel\n      });\n      observer.complete();\n    }\n  }, error => {\n    observer.error(new _ledgerhq_errors__WEBPACK_IMPORTED_MODULE_4__.TransportOpenUserCancelled(error.message));\n  });\n\n  function unsubscribe() {\n    unsubscribed = true;\n  }\n\n  return {\n    unsubscribe\n  };\n};\n//# sourceMappingURL=TransportWebHID.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@ledgerhq/hw-transport-webhid/lib-es/TransportWebHID.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@ledgerhq/hw-transport-webusb/lib-es/TransportWebUSB.js":
/*!******************************************************************************!*\
  !*** ./node_modules/@ledgerhq/hw-transport-webusb/lib-es/TransportWebUSB.js ***!
  \******************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TransportWebUSB)\n/* harmony export */ });\n/* harmony import */ var _ledgerhq_hw_transport__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @ledgerhq/hw-transport */ \"(ssr)/./node_modules/@ledgerhq/hw-transport/lib-es/Transport.js\");\n/* harmony import */ var _ledgerhq_devices_lib_hid_framing__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @ledgerhq/devices/lib/hid-framing */ \"(ssr)/./node_modules/@ledgerhq/devices/lib/hid-framing.js\");\n/* harmony import */ var _ledgerhq_devices__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @ledgerhq/devices */ \"(ssr)/./node_modules/@ledgerhq/devices/lib-es/index.js\");\n/* harmony import */ var _ledgerhq_logs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @ledgerhq/logs */ \"(ssr)/./node_modules/@ledgerhq/logs/lib-es/index.js\");\n/* harmony import */ var _ledgerhq_errors__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @ledgerhq/errors */ \"(ssr)/./node_modules/@ledgerhq/errors/dist/index.js\");\n/* harmony import */ var _webusb__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./webusb */ \"(ssr)/./node_modules/@ledgerhq/hw-transport-webusb/lib-es/webusb.js\");\n\n\n\n\n\n\nconst configurationValue = 1;\nconst endpointNumber = 3;\n/**\n * WebUSB Transport implementation\n * @example\n * import TransportWebUSB from \"@ledgerhq/hw-transport-webusb\";\n * ...\n * TransportWebUSB.create().then(transport => ...)\n */\n\nclass TransportWebUSB extends _ledgerhq_hw_transport__WEBPACK_IMPORTED_MODULE_0__[\"default\"] {\n  constructor(device, interfaceNumber) {\n    super();\n    this.device = void 0;\n    this.deviceModel = void 0;\n    this.channel = Math.floor(Math.random() * 0xffff);\n    this.packetSize = 64;\n    this.interfaceNumber = void 0;\n    this._disconnectEmitted = false;\n\n    this._emitDisconnect = e => {\n      if (this._disconnectEmitted) return;\n      this._disconnectEmitted = true;\n      this.emit(\"disconnect\", e);\n    };\n\n    this.exchange = apdu => this.exchangeAtomicImpl(async () => {\n      const {\n        channel,\n        packetSize\n      } = this;\n      (0,_ledgerhq_logs__WEBPACK_IMPORTED_MODULE_3__.log)(\"apdu\", \"=> \" + apdu.toString(\"hex\"));\n      const framing = (0,_ledgerhq_devices_lib_hid_framing__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(channel, packetSize); // Write...\n\n      const blocks = framing.makeBlocks(apdu);\n\n      for (let i = 0; i < blocks.length; i++) {\n        await this.device.transferOut(endpointNumber, blocks[i]);\n      } // Read...\n\n\n      let result;\n      let acc;\n\n      while (!(result = framing.getReducedResult(acc))) {\n        const r = await this.device.transferIn(endpointNumber, packetSize);\n        const buffer = Buffer.from(r.data.buffer);\n        acc = framing.reduceResponse(acc, buffer);\n      }\n\n      (0,_ledgerhq_logs__WEBPACK_IMPORTED_MODULE_3__.log)(\"apdu\", \"<= \" + result.toString(\"hex\"));\n      return result;\n    }).catch(e => {\n      if (e && e.message && e.message.includes(\"disconnected\")) {\n        this._emitDisconnect(e);\n\n        throw new _ledgerhq_errors__WEBPACK_IMPORTED_MODULE_4__.DisconnectedDeviceDuringOperation(e.message);\n      }\n\n      throw e;\n    });\n\n    this.device = device;\n    this.interfaceNumber = interfaceNumber;\n    this.deviceModel = (0,_ledgerhq_devices__WEBPACK_IMPORTED_MODULE_2__.identifyUSBProductId)(device.productId);\n  }\n  /**\n   * Check if WebUSB transport is supported.\n   */\n\n\n  /**\n   * Similar to create() except it will always display the device permission (even if some devices are already accepted).\n   */\n  static async request() {\n    const device = await (0,_webusb__WEBPACK_IMPORTED_MODULE_5__.requestLedgerDevice)();\n    return TransportWebUSB.open(device);\n  }\n  /**\n   * Similar to create() except it will never display the device permission (it returns a Promise<?Transport>, null if it fails to find a device).\n   */\n\n\n  static async openConnected() {\n    const devices = await (0,_webusb__WEBPACK_IMPORTED_MODULE_5__.getLedgerDevices)();\n    if (devices.length === 0) return null;\n    return TransportWebUSB.open(devices[0]);\n  }\n  /**\n   * Create a Ledger transport with a USBDevice\n   */\n\n\n  static async open(device) {\n    await device.open();\n\n    if (device.configuration === null) {\n      await device.selectConfiguration(configurationValue);\n    }\n\n    await gracefullyResetDevice(device);\n    const iface = device.configurations[0].interfaces.find(({\n      alternates\n    }) => alternates.some(a => a.interfaceClass === 255));\n\n    if (!iface) {\n      throw new _ledgerhq_errors__WEBPACK_IMPORTED_MODULE_4__.TransportInterfaceNotAvailable(\"No WebUSB interface found for your Ledger device. Please upgrade firmware or contact techsupport.\");\n    }\n\n    const interfaceNumber = iface.interfaceNumber;\n\n    try {\n      await device.claimInterface(interfaceNumber);\n    } catch (e) {\n      await device.close();\n      throw new _ledgerhq_errors__WEBPACK_IMPORTED_MODULE_4__.TransportInterfaceNotAvailable(e.message);\n    }\n\n    const transport = new TransportWebUSB(device, interfaceNumber);\n\n    const onDisconnect = e => {\n      if (device === e.device) {\n        // $FlowFixMe\n        navigator.usb.removeEventListener(\"disconnect\", onDisconnect);\n\n        transport._emitDisconnect(new _ledgerhq_errors__WEBPACK_IMPORTED_MODULE_4__.DisconnectedDevice());\n      }\n    }; // $FlowFixMe\n\n\n    navigator.usb.addEventListener(\"disconnect\", onDisconnect);\n    return transport;\n  }\n\n  /**\n   * Release the transport device\n   */\n  async close() {\n    await this.exchangeBusyPromise;\n    await this.device.releaseInterface(this.interfaceNumber);\n    await gracefullyResetDevice(this.device);\n    await this.device.close();\n  }\n  /**\n   * Exchange with the device using APDU protocol.\n   * @param apdu\n   * @returns a promise of apdu response\n   */\n\n\n  setScrambleKey() {}\n\n}\nTransportWebUSB.isSupported = _webusb__WEBPACK_IMPORTED_MODULE_5__.isSupported;\nTransportWebUSB.list = _webusb__WEBPACK_IMPORTED_MODULE_5__.getLedgerDevices;\n\nTransportWebUSB.listen = observer => {\n  let unsubscribed = false;\n  (0,_webusb__WEBPACK_IMPORTED_MODULE_5__.getFirstLedgerDevice)().then(device => {\n    if (!unsubscribed) {\n      const deviceModel = (0,_ledgerhq_devices__WEBPACK_IMPORTED_MODULE_2__.identifyUSBProductId)(device.productId);\n      observer.next({\n        type: \"add\",\n        descriptor: device,\n        deviceModel\n      });\n      observer.complete();\n    }\n  }, error => {\n    if (window.DOMException && error instanceof window.DOMException && error.code === 18) {\n      observer.error(new _ledgerhq_errors__WEBPACK_IMPORTED_MODULE_4__.TransportWebUSBGestureRequired(error.message));\n    } else {\n      observer.error(new _ledgerhq_errors__WEBPACK_IMPORTED_MODULE_4__.TransportOpenUserCancelled(error.message));\n    }\n  });\n\n  function unsubscribe() {\n    unsubscribed = true;\n  }\n\n  return {\n    unsubscribe\n  };\n};\n\nasync function gracefullyResetDevice(device) {\n  try {\n    await device.reset();\n  } catch (err) {\n    console.warn(err);\n  }\n}\n//# sourceMappingURL=TransportWebUSB.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@ledgerhq/hw-transport-webusb/lib-es/TransportWebUSB.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@ledgerhq/hw-transport-webusb/lib-es/webusb.js":
/*!*********************************************************************!*\
  !*** ./node_modules/@ledgerhq/hw-transport-webusb/lib-es/webusb.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getFirstLedgerDevice: () => (/* binding */ getFirstLedgerDevice),\n/* harmony export */   getLedgerDevices: () => (/* binding */ getLedgerDevices),\n/* harmony export */   isSupported: () => (/* binding */ isSupported),\n/* harmony export */   requestLedgerDevice: () => (/* binding */ requestLedgerDevice)\n/* harmony export */ });\n/* harmony import */ var _ledgerhq_devices__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @ledgerhq/devices */ \"(ssr)/./node_modules/@ledgerhq/devices/lib-es/index.js\");\n\nconst ledgerDevices = [{\n  vendorId: _ledgerhq_devices__WEBPACK_IMPORTED_MODULE_0__.ledgerUSBVendorId\n}];\nasync function requestLedgerDevice() {\n  // $FlowFixMe\n  const device = await navigator.usb.requestDevice({\n    filters: ledgerDevices\n  });\n  return device;\n}\nasync function getLedgerDevices() {\n  // $FlowFixMe\n  const devices = await navigator.usb.getDevices();\n  return devices.filter(d => d.vendorId === _ledgerhq_devices__WEBPACK_IMPORTED_MODULE_0__.ledgerUSBVendorId);\n}\nasync function getFirstLedgerDevice() {\n  const existingDevices = await getLedgerDevices();\n  if (existingDevices.length > 0) return existingDevices[0];\n  return requestLedgerDevice();\n}\nconst isSupported = () => Promise.resolve(!!navigator && // $FlowFixMe\n!!navigator.usb && typeof navigator.usb.getDevices === \"function\");\n//# sourceMappingURL=webusb.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQGxlZGdlcmhxL2h3LXRyYW5zcG9ydC13ZWJ1c2IvbGliLWVzL3dlYnVzYi5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUFzRDtBQUN0RDtBQUNBLFlBQVksZ0VBQWlCO0FBQzdCLENBQUM7QUFDTTtBQUNQO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ087QUFDUDtBQUNBO0FBQ0EsNENBQTRDLGdFQUFpQjtBQUM3RDtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDTztBQUNQO0FBQ0EiLCJzb3VyY2VzIjpbIi9Vc2Vycy94eW0vY29kZS90Ym9vay9vbnRvbl9jYy9vbmlvbi1sYXVuY2gvZnJvbnRlbmQvbm9kZV9tb2R1bGVzL0BsZWRnZXJocS9ody10cmFuc3BvcnQtd2VidXNiL2xpYi1lcy93ZWJ1c2IuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgbGVkZ2VyVVNCVmVuZG9ySWQgfSBmcm9tIFwiQGxlZGdlcmhxL2RldmljZXNcIjtcbmNvbnN0IGxlZGdlckRldmljZXMgPSBbe1xuICB2ZW5kb3JJZDogbGVkZ2VyVVNCVmVuZG9ySWRcbn1dO1xuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIHJlcXVlc3RMZWRnZXJEZXZpY2UoKSB7XG4gIC8vICRGbG93Rml4TWVcbiAgY29uc3QgZGV2aWNlID0gYXdhaXQgbmF2aWdhdG9yLnVzYi5yZXF1ZXN0RGV2aWNlKHtcbiAgICBmaWx0ZXJzOiBsZWRnZXJEZXZpY2VzXG4gIH0pO1xuICByZXR1cm4gZGV2aWNlO1xufVxuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIGdldExlZGdlckRldmljZXMoKSB7XG4gIC8vICRGbG93Rml4TWVcbiAgY29uc3QgZGV2aWNlcyA9IGF3YWl0IG5hdmlnYXRvci51c2IuZ2V0RGV2aWNlcygpO1xuICByZXR1cm4gZGV2aWNlcy5maWx0ZXIoZCA9PiBkLnZlbmRvcklkID09PSBsZWRnZXJVU0JWZW5kb3JJZCk7XG59XG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gZ2V0Rmlyc3RMZWRnZXJEZXZpY2UoKSB7XG4gIGNvbnN0IGV4aXN0aW5nRGV2aWNlcyA9IGF3YWl0IGdldExlZGdlckRldmljZXMoKTtcbiAgaWYgKGV4aXN0aW5nRGV2aWNlcy5sZW5ndGggPiAwKSByZXR1cm4gZXhpc3RpbmdEZXZpY2VzWzBdO1xuICByZXR1cm4gcmVxdWVzdExlZGdlckRldmljZSgpO1xufVxuZXhwb3J0IGNvbnN0IGlzU3VwcG9ydGVkID0gKCkgPT4gUHJvbWlzZS5yZXNvbHZlKCEhbmF2aWdhdG9yICYmIC8vICRGbG93Rml4TWVcbiEhbmF2aWdhdG9yLnVzYiAmJiB0eXBlb2YgbmF2aWdhdG9yLnVzYi5nZXREZXZpY2VzID09PSBcImZ1bmN0aW9uXCIpO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9d2VidXNiLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@ledgerhq/hw-transport-webusb/lib-es/webusb.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@ledgerhq/hw-transport/lib-es/Transport.js":
/*!*****************************************************************!*\
  !*** ./node_modules/@ledgerhq/hw-transport/lib-es/Transport.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   StatusCodes: () => (/* reexport safe */ _ledgerhq_errors__WEBPACK_IMPORTED_MODULE_1__.StatusCodes),\n/* harmony export */   TransportError: () => (/* reexport safe */ _ledgerhq_errors__WEBPACK_IMPORTED_MODULE_1__.TransportError),\n/* harmony export */   TransportStatusError: () => (/* reexport safe */ _ledgerhq_errors__WEBPACK_IMPORTED_MODULE_1__.TransportStatusError),\n/* harmony export */   \"default\": () => (/* binding */ Transport),\n/* harmony export */   getAltStatusMessage: () => (/* reexport safe */ _ledgerhq_errors__WEBPACK_IMPORTED_MODULE_1__.getAltStatusMessage)\n/* harmony export */ });\n/* harmony import */ var events__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! events */ \"events\");\n/* harmony import */ var events__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(events__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _ledgerhq_errors__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @ledgerhq/errors */ \"(ssr)/./node_modules/@ledgerhq/errors/dist/index.js\");\n\n\n\n/**\n */\n\n/**\n * Transport defines the generic interface to share between node/u2f impl\n * A **Descriptor** is a parametric type that is up to be determined for the implementation.\n * it can be for instance an ID, an file path, a URL,...\n */\nclass Transport {\n  constructor() {\n    this.exchangeTimeout = 30000;\n    this.unresponsiveTimeout = 15000;\n    this.deviceModel = null;\n    this._events = new (events__WEBPACK_IMPORTED_MODULE_0___default())();\n\n    this.send = async (cla, ins, p1, p2, data = Buffer.alloc(0), statusList = [_ledgerhq_errors__WEBPACK_IMPORTED_MODULE_1__.StatusCodes.OK]) => {\n      if (data.length >= 256) {\n        throw new _ledgerhq_errors__WEBPACK_IMPORTED_MODULE_1__.TransportError(\"data.length exceed 256 bytes limit. Got: \" + data.length, \"DataLengthTooBig\");\n      }\n\n      const response = await this.exchange(Buffer.concat([Buffer.from([cla, ins, p1, p2]), Buffer.from([data.length]), data]));\n      const sw = response.readUInt16BE(response.length - 2);\n\n      if (!statusList.some(s => s === sw)) {\n        throw new _ledgerhq_errors__WEBPACK_IMPORTED_MODULE_1__.TransportStatusError(sw);\n      }\n\n      return response;\n    };\n\n    this.exchangeBusyPromise = void 0;\n\n    this.exchangeAtomicImpl = async f => {\n      if (this.exchangeBusyPromise) {\n        throw new _ledgerhq_errors__WEBPACK_IMPORTED_MODULE_1__.TransportRaceCondition(\"An action was already pending on the Ledger device. Please deny or reconnect.\");\n      }\n\n      let resolveBusy;\n      const busyPromise = new Promise(r => {\n        resolveBusy = r;\n      });\n      this.exchangeBusyPromise = busyPromise;\n      let unresponsiveReached = false;\n      const timeout = setTimeout(() => {\n        unresponsiveReached = true;\n        this.emit(\"unresponsive\");\n      }, this.unresponsiveTimeout);\n\n      try {\n        const res = await f();\n\n        if (unresponsiveReached) {\n          this.emit(\"responsive\");\n        }\n\n        return res;\n      } finally {\n        clearTimeout(timeout);\n        if (resolveBusy) resolveBusy();\n        this.exchangeBusyPromise = null;\n      }\n    };\n\n    this._appAPIlock = null;\n  }\n\n  /**\n   * low level api to communicate with the device\n   * This method is for implementations to implement but should not be directly called.\n   * Instead, the recommanded way is to use send() method\n   * @param apdu the data to send\n   * @return a Promise of response data\n   */\n  exchange(_apdu) {\n    throw new Error(\"exchange not implemented\");\n  }\n  /**\n   * set the \"scramble key\" for the next exchanges with the device.\n   * Each App can have a different scramble key and they internally will set it at instanciation.\n   * @param key the scramble key\n   */\n\n\n  setScrambleKey(_key) {}\n  /**\n   * close the exchange with the device.\n   * @return a Promise that ends when the transport is closed.\n   */\n\n\n  close() {\n    return Promise.resolve();\n  }\n\n  /**\n   * Listen to an event on an instance of transport.\n   * Transport implementation can have specific events. Here is the common events:\n   * * `\"disconnect\"` : triggered if Transport is disconnected\n   */\n  on(eventName, cb) {\n    this._events.on(eventName, cb);\n  }\n  /**\n   * Stop listening to an event on an instance of transport.\n   */\n\n\n  off(eventName, cb) {\n    this._events.removeListener(eventName, cb);\n  }\n\n  emit(event, ...args) {\n    this._events.emit(event, ...args);\n  }\n  /**\n   * Enable or not logs of the binary exchange\n   */\n\n\n  setDebugMode() {\n    console.warn(\"setDebugMode is deprecated. use @ledgerhq/logs instead. No logs are emitted in this anymore.\");\n  }\n  /**\n   * Set a timeout (in milliseconds) for the exchange call. Only some transport might implement it. (e.g. U2F)\n   */\n\n\n  setExchangeTimeout(exchangeTimeout) {\n    this.exchangeTimeout = exchangeTimeout;\n  }\n  /**\n   * Define the delay before emitting \"unresponsive\" on an exchange that does not respond\n   */\n\n\n  setExchangeUnresponsiveTimeout(unresponsiveTimeout) {\n    this.unresponsiveTimeout = unresponsiveTimeout;\n  }\n  /**\n   * wrapper on top of exchange to simplify work of the implementation.\n   * @param cla\n   * @param ins\n   * @param p1\n   * @param p2\n   * @param data\n   * @param statusList is a list of accepted status code (shorts). [0x9000] by default\n   * @return a Promise of response buffer\n   */\n\n\n  /**\n   * create() allows to open the first descriptor available or\n   * throw if there is none or if timeout is reached.\n   * This is a light helper, alternative to using listen() and open() (that you may need for any more advanced usecase)\n   * @example\n  TransportFoo.create().then(transport => ...)\n   */\n  static create(openTimeout = 3000, listenTimeout) {\n    return new Promise((resolve, reject) => {\n      let found = false;\n      const sub = this.listen({\n        next: e => {\n          found = true;\n          if (sub) sub.unsubscribe();\n          if (listenTimeoutId) clearTimeout(listenTimeoutId);\n          this.open(e.descriptor, openTimeout).then(resolve, reject);\n        },\n        error: e => {\n          if (listenTimeoutId) clearTimeout(listenTimeoutId);\n          reject(e);\n        },\n        complete: () => {\n          if (listenTimeoutId) clearTimeout(listenTimeoutId);\n\n          if (!found) {\n            reject(new _ledgerhq_errors__WEBPACK_IMPORTED_MODULE_1__.TransportError(this.ErrorMessage_NoDeviceFound, \"NoDeviceFound\"));\n          }\n        }\n      });\n      const listenTimeoutId = listenTimeout ? setTimeout(() => {\n        sub.unsubscribe();\n        reject(new _ledgerhq_errors__WEBPACK_IMPORTED_MODULE_1__.TransportError(this.ErrorMessage_ListenTimeout, \"ListenTimeout\"));\n      }, listenTimeout) : null;\n    });\n  }\n\n  decorateAppAPIMethods(self, methods, scrambleKey) {\n    for (let methodName of methods) {\n      self[methodName] = this.decorateAppAPIMethod(methodName, self[methodName], self, scrambleKey);\n    }\n  }\n\n  decorateAppAPIMethod(methodName, f, ctx, scrambleKey) {\n    return async (...args) => {\n      const {\n        _appAPIlock\n      } = this;\n\n      if (_appAPIlock) {\n        return Promise.reject(new _ledgerhq_errors__WEBPACK_IMPORTED_MODULE_1__.TransportError(\"Ledger Device is busy (lock \" + _appAPIlock + \")\", \"TransportLocked\"));\n      }\n\n      try {\n        this._appAPIlock = methodName;\n        this.setScrambleKey(scrambleKey);\n        return await f.apply(ctx, args);\n      } finally {\n        this._appAPIlock = null;\n      }\n    };\n  }\n\n}\nTransport.isSupported = void 0;\nTransport.list = void 0;\nTransport.listen = void 0;\nTransport.open = void 0;\nTransport.ErrorMessage_ListenTimeout = \"No Ledger device found (timeout)\";\nTransport.ErrorMessage_NoDeviceFound = \"No Ledger device found\";\n//# sourceMappingURL=Transport.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@ledgerhq/hw-transport/lib-es/Transport.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@ledgerhq/logs/lib-es/index.js":
/*!*****************************************************!*\
  !*** ./node_modules/@ledgerhq/logs/lib-es/index.js ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   listen: () => (/* binding */ listen),\n/* harmony export */   log: () => (/* binding */ log)\n/* harmony export */ });\n/**\n * A Log object\n */\nlet id = 0;\nconst subscribers = [];\n/**\n * log something\n * @param type a namespaced identifier of the log (it is not a level like \"debug\", \"error\" but more like \"apdu-in\", \"apdu-out\", etc...)\n * @param message a clear message of the log associated to the type\n */\n\nconst log = (type, message, data) => {\n  const obj = {\n    type,\n    id: String(++id),\n    date: new Date()\n  };\n  if (message) obj.message = message;\n  if (data) obj.data = data;\n  dispatch(obj);\n};\n/**\n * listen to logs.\n * @param cb that is called for each future log() with the Log object\n * @return a function that can be called to unsubscribe the listener\n */\n\nconst listen = cb => {\n  subscribers.push(cb);\n  return () => {\n    const i = subscribers.indexOf(cb);\n\n    if (i !== -1) {\n      // equivalent of subscribers.splice(i, 1) // https://twitter.com/Rich_Harris/status/1125850391155965952\n      subscribers[i] = subscribers[subscribers.length - 1];\n      subscribers.pop();\n    }\n  };\n};\n\nfunction dispatch(log) {\n  for (let i = 0; i < subscribers.length; i++) {\n    try {\n      subscribers[i](log);\n    } catch (e) {\n      console.error(e);\n    }\n  }\n} // for debug purpose\n\n\nif (typeof window !== \"undefined\") {\n  window.__ledgerLogsListen = listen;\n}\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@ledgerhq/logs/lib-es/index.js\n");

/***/ })

};
;