"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/webcrypto-core";
exports.ids = ["vendor-chunks/webcrypto-core"];
exports.modules = {

/***/ "(ssr)/./node_modules/webcrypto-core/build/webcrypto-core.es.js":
/*!****************************************************************!*\
  !*** ./node_modules/webcrypto-core/build/webcrypto-core.es.js ***!
  \****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AesCbcProvider: () => (/* binding */ AesCbcProvider),\n/* harmony export */   AesCmacProvider: () => (/* binding */ AesCmacProvider),\n/* harmony export */   AesCtrProvider: () => (/* binding */ AesCtrProvider),\n/* harmony export */   AesEcbProvider: () => (/* binding */ AesEcbProvider),\n/* harmony export */   AesGcmProvider: () => (/* binding */ AesGcmProvider),\n/* harmony export */   AesKwProvider: () => (/* binding */ AesKwProvider),\n/* harmony export */   AesProvider: () => (/* binding */ AesProvider),\n/* harmony export */   AlgorithmError: () => (/* binding */ AlgorithmError),\n/* harmony export */   BufferSourceConverter: () => (/* reexport safe */ pvtsutils__WEBPACK_IMPORTED_MODULE_0__.BufferSourceConverter),\n/* harmony export */   Crypto: () => (/* binding */ Crypto),\n/* harmony export */   CryptoError: () => (/* binding */ CryptoError),\n/* harmony export */   CryptoKey: () => (/* binding */ CryptoKey),\n/* harmony export */   DesProvider: () => (/* binding */ DesProvider),\n/* harmony export */   EcCurves: () => (/* binding */ EcCurves),\n/* harmony export */   EcUtils: () => (/* binding */ EcUtils),\n/* harmony export */   EcdhEsProvider: () => (/* binding */ EcdhEsProvider),\n/* harmony export */   EcdhProvider: () => (/* binding */ EcdhProvider),\n/* harmony export */   EcdsaProvider: () => (/* binding */ EcdsaProvider),\n/* harmony export */   Ed25519Provider: () => (/* binding */ Ed25519Provider),\n/* harmony export */   EdDsaProvider: () => (/* binding */ EdDsaProvider),\n/* harmony export */   EllipticProvider: () => (/* binding */ EllipticProvider),\n/* harmony export */   HkdfProvider: () => (/* binding */ HkdfProvider),\n/* harmony export */   HmacProvider: () => (/* binding */ HmacProvider),\n/* harmony export */   JwkUtils: () => (/* binding */ JwkUtils),\n/* harmony export */   OperationError: () => (/* binding */ OperationError),\n/* harmony export */   Pbkdf2Provider: () => (/* binding */ Pbkdf2Provider),\n/* harmony export */   PemConverter: () => (/* binding */ PemConverter),\n/* harmony export */   ProviderCrypto: () => (/* binding */ ProviderCrypto),\n/* harmony export */   ProviderStorage: () => (/* binding */ ProviderStorage),\n/* harmony export */   RequiredPropertyError: () => (/* binding */ RequiredPropertyError),\n/* harmony export */   RsaOaepProvider: () => (/* binding */ RsaOaepProvider),\n/* harmony export */   RsaProvider: () => (/* binding */ RsaProvider),\n/* harmony export */   RsaPssProvider: () => (/* binding */ RsaPssProvider),\n/* harmony export */   RsaSsaProvider: () => (/* binding */ RsaSsaProvider),\n/* harmony export */   Shake128Provider: () => (/* binding */ Shake128Provider),\n/* harmony export */   Shake256Provider: () => (/* binding */ Shake256Provider),\n/* harmony export */   ShakeProvider: () => (/* binding */ ShakeProvider),\n/* harmony export */   SubtleCrypto: () => (/* binding */ SubtleCrypto),\n/* harmony export */   UnsupportedOperationError: () => (/* binding */ UnsupportedOperationError),\n/* harmony export */   X25519Provider: () => (/* binding */ X25519Provider),\n/* harmony export */   asn1: () => (/* binding */ index$1),\n/* harmony export */   isJWK: () => (/* binding */ isJWK),\n/* harmony export */   json: () => (/* binding */ index)\n/* harmony export */ });\n/* harmony import */ var pvtsutils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! pvtsutils */ \"(ssr)/./node_modules/pvtsutils/build/index.es.js\");\n/* harmony import */ var _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @peculiar/asn1-schema */ \"(ssr)/./node_modules/@peculiar/asn1-schema/build/es2015/index.js\");\n/* harmony import */ var tslib__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! tslib */ \"(ssr)/./node_modules/tslib/tslib.es6.mjs\");\n/* harmony import */ var _peculiar_json_schema__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @peculiar/json-schema */ \"(ssr)/./node_modules/@peculiar/json-schema/build/index.es.js\");\n/* harmony import */ var asn1js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! asn1js */ \"(ssr)/./node_modules/asn1js/build/index.es.js\");\n/*!\n Copyright (c) Peculiar Ventures, LLC\n*/\n\n\n\n\n\n\n\n\nclass CryptoError extends Error {\n}\n\nclass AlgorithmError extends CryptoError {\n}\n\nclass UnsupportedOperationError extends CryptoError {\n    constructor(methodName) {\n        super(`Unsupported operation: ${methodName ? `${methodName}` : \"\"}`);\n    }\n}\n\nclass OperationError extends CryptoError {\n}\n\nclass RequiredPropertyError extends CryptoError {\n    constructor(propName) {\n        super(`${propName}: Missing required property`);\n    }\n}\n\nclass PemConverter {\n    static toArrayBuffer(pem) {\n        const base64 = pem\n            .replace(/-{5}(BEGIN|END) .*-{5}/g, \"\")\n            .replace(\"\\r\", \"\")\n            .replace(\"\\n\", \"\");\n        return pvtsutils__WEBPACK_IMPORTED_MODULE_0__.Convert.FromBase64(base64);\n    }\n    static toUint8Array(pem) {\n        const bytes = this.toArrayBuffer(pem);\n        return new Uint8Array(bytes);\n    }\n    static fromBufferSource(buffer, tag) {\n        const base64 = pvtsutils__WEBPACK_IMPORTED_MODULE_0__.Convert.ToBase64(buffer);\n        let sliced;\n        let offset = 0;\n        const rows = [];\n        while (offset < base64.length) {\n            sliced = base64.slice(offset, offset + 64);\n            if (sliced.length) {\n                rows.push(sliced);\n            }\n            else {\n                break;\n            }\n            offset += 64;\n        }\n        const upperCaseTag = tag.toUpperCase();\n        return `-----BEGIN ${upperCaseTag}-----\\n${rows.join(\"\\n\")}\\n-----END ${upperCaseTag}-----`;\n    }\n    static isPEM(data) {\n        return /-----BEGIN .+-----[A-Za-z0-9+/+=\\s\\n]+-----END .+-----/i.test(data);\n    }\n    static getTagName(pem) {\n        if (!this.isPEM(pem)) {\n            throw new Error(\"Bad parameter. Incoming data is not right PEM\");\n        }\n        const res = /-----BEGIN (.+)-----/.exec(pem);\n        if (!res) {\n            throw new Error(\"Cannot get tag from PEM\");\n        }\n        return res[1];\n    }\n    static hasTagName(pem, tagName) {\n        const tag = this.getTagName(pem);\n        return tagName.toLowerCase() === tag.toLowerCase();\n    }\n    static isCertificate(pem) {\n        return this.hasTagName(pem, \"certificate\");\n    }\n    static isCertificateRequest(pem) {\n        return this.hasTagName(pem, \"certificate request\");\n    }\n    static isCRL(pem) {\n        return this.hasTagName(pem, \"x509 crl\");\n    }\n    static isPublicKey(pem) {\n        return this.hasTagName(pem, \"public key\");\n    }\n}\n\nfunction isJWK(data) {\n    return typeof data === \"object\" && \"kty\" in data;\n}\n\nclass ProviderCrypto {\n    async digest(...args) {\n        this.checkDigest.apply(this, args);\n        return this.onDigest.apply(this, args);\n    }\n    checkDigest(algorithm, _data) {\n        this.checkAlgorithmName(algorithm);\n    }\n    async onDigest(_algorithm, _data) {\n        throw new UnsupportedOperationError(\"digest\");\n    }\n    async generateKey(...args) {\n        this.checkGenerateKey.apply(this, args);\n        return this.onGenerateKey.apply(this, args);\n    }\n    checkGenerateKey(algorithm, _extractable, keyUsages, ..._args) {\n        this.checkAlgorithmName(algorithm);\n        this.checkGenerateKeyParams(algorithm);\n        if (!(keyUsages && keyUsages.length)) {\n            throw new TypeError(`Usages cannot be empty when creating a key.`);\n        }\n        let allowedUsages;\n        if (Array.isArray(this.usages)) {\n            allowedUsages = this.usages;\n        }\n        else {\n            allowedUsages = this.usages.privateKey.concat(this.usages.publicKey);\n        }\n        this.checkKeyUsages(keyUsages, allowedUsages);\n    }\n    checkGenerateKeyParams(_algorithm) {\n    }\n    async onGenerateKey(_algorithm, _extractable, _keyUsages, ..._args) {\n        throw new UnsupportedOperationError(\"generateKey\");\n    }\n    async sign(...args) {\n        this.checkSign.apply(this, args);\n        return this.onSign.apply(this, args);\n    }\n    checkSign(algorithm, key, _data, ..._args) {\n        this.checkAlgorithmName(algorithm);\n        this.checkAlgorithmParams(algorithm);\n        this.checkCryptoKey(key, \"sign\");\n    }\n    async onSign(_algorithm, _key, _data, ..._args) {\n        throw new UnsupportedOperationError(\"sign\");\n    }\n    async verify(...args) {\n        this.checkVerify.apply(this, args);\n        return this.onVerify.apply(this, args);\n    }\n    checkVerify(algorithm, key, _signature, _data, ..._args) {\n        this.checkAlgorithmName(algorithm);\n        this.checkAlgorithmParams(algorithm);\n        this.checkCryptoKey(key, \"verify\");\n    }\n    async onVerify(_algorithm, _key, _signature, _data, ..._args) {\n        throw new UnsupportedOperationError(\"verify\");\n    }\n    async encrypt(...args) {\n        this.checkEncrypt.apply(this, args);\n        return this.onEncrypt.apply(this, args);\n    }\n    checkEncrypt(algorithm, key, _data, options = {}, ..._args) {\n        this.checkAlgorithmName(algorithm);\n        this.checkAlgorithmParams(algorithm);\n        this.checkCryptoKey(key, options.keyUsage ? \"encrypt\" : void 0);\n    }\n    async onEncrypt(_algorithm, _key, _data, ..._args) {\n        throw new UnsupportedOperationError(\"encrypt\");\n    }\n    async decrypt(...args) {\n        this.checkDecrypt.apply(this, args);\n        return this.onDecrypt.apply(this, args);\n    }\n    checkDecrypt(algorithm, key, _data, options = {}, ..._args) {\n        this.checkAlgorithmName(algorithm);\n        this.checkAlgorithmParams(algorithm);\n        this.checkCryptoKey(key, options.keyUsage ? \"decrypt\" : void 0);\n    }\n    async onDecrypt(_algorithm, _key, _data, ..._args) {\n        throw new UnsupportedOperationError(\"decrypt\");\n    }\n    async deriveBits(...args) {\n        this.checkDeriveBits.apply(this, args);\n        return this.onDeriveBits.apply(this, args);\n    }\n    checkDeriveBits(algorithm, baseKey, length, options = {}, ..._args) {\n        this.checkAlgorithmName(algorithm);\n        this.checkAlgorithmParams(algorithm);\n        this.checkCryptoKey(baseKey, options.keyUsage ? \"deriveBits\" : void 0);\n        if (length % 8 !== 0) {\n            throw new OperationError(\"length: Is not multiple of 8\");\n        }\n    }\n    async onDeriveBits(_algorithm, _baseKey, _length, ..._args) {\n        throw new UnsupportedOperationError(\"deriveBits\");\n    }\n    async exportKey(...args) {\n        this.checkExportKey.apply(this, args);\n        return this.onExportKey.apply(this, args);\n    }\n    checkExportKey(format, key, ..._args) {\n        this.checkKeyFormat(format);\n        this.checkCryptoKey(key);\n        if (!key.extractable) {\n            throw new CryptoError(\"key: Is not extractable\");\n        }\n    }\n    async onExportKey(_format, _key, ..._args) {\n        throw new UnsupportedOperationError(\"exportKey\");\n    }\n    async importKey(...args) {\n        this.checkImportKey.apply(this, args);\n        return this.onImportKey.apply(this, args);\n    }\n    checkImportKey(format, keyData, algorithm, _extractable, keyUsages, ..._args) {\n        this.checkKeyFormat(format);\n        this.checkKeyData(format, keyData);\n        this.checkAlgorithmName(algorithm);\n        this.checkImportParams(algorithm);\n        if (Array.isArray(this.usages)) {\n            this.checkKeyUsages(keyUsages, this.usages);\n        }\n    }\n    async onImportKey(_format, _keyData, _algorithm, _extractable, _keyUsages, ..._args) {\n        throw new UnsupportedOperationError(\"importKey\");\n    }\n    checkAlgorithmName(algorithm) {\n        if (algorithm.name.toLowerCase() !== this.name.toLowerCase()) {\n            throw new AlgorithmError(\"Unrecognized name\");\n        }\n    }\n    checkAlgorithmParams(_algorithm) {\n    }\n    checkDerivedKeyParams(_algorithm) {\n    }\n    checkKeyUsages(usages, allowed) {\n        for (const usage of usages) {\n            if (allowed.indexOf(usage) === -1) {\n                throw new TypeError(\"Cannot create a key using the specified key usages\");\n            }\n        }\n    }\n    checkCryptoKey(key, keyUsage) {\n        this.checkAlgorithmName(key.algorithm);\n        if (keyUsage && key.usages.indexOf(keyUsage) === -1) {\n            throw new CryptoError(`key does not match that of operation`);\n        }\n    }\n    checkRequiredProperty(data, propName) {\n        if (!(propName in data)) {\n            throw new RequiredPropertyError(propName);\n        }\n    }\n    checkHashAlgorithm(algorithm, hashAlgorithms) {\n        for (const item of hashAlgorithms) {\n            if (item.toLowerCase() === algorithm.name.toLowerCase()) {\n                return;\n            }\n        }\n        throw new OperationError(`hash: Must be one of ${hashAlgorithms.join(\", \")}`);\n    }\n    checkImportParams(_algorithm) {\n    }\n    checkKeyFormat(format) {\n        switch (format) {\n            case \"raw\":\n            case \"pkcs8\":\n            case \"spki\":\n            case \"jwk\":\n                break;\n            default:\n                throw new TypeError(\"format: Is invalid value. Must be 'jwk', 'raw', 'spki', or 'pkcs8'\");\n        }\n    }\n    checkKeyData(format, keyData) {\n        if (!keyData) {\n            throw new TypeError(\"keyData: Cannot be empty on empty on key importing\");\n        }\n        if (format === \"jwk\") {\n            if (!isJWK(keyData)) {\n                throw new TypeError(\"keyData: Is not JsonWebToken\");\n            }\n        }\n        else if (!pvtsutils__WEBPACK_IMPORTED_MODULE_0__.BufferSourceConverter.isBufferSource(keyData)) {\n            throw new TypeError(\"keyData: Is not ArrayBufferView or ArrayBuffer\");\n        }\n    }\n    prepareData(data) {\n        return pvtsutils__WEBPACK_IMPORTED_MODULE_0__.BufferSourceConverter.toArrayBuffer(data);\n    }\n}\n\nclass AesProvider extends ProviderCrypto {\n    checkGenerateKeyParams(algorithm) {\n        this.checkRequiredProperty(algorithm, \"length\");\n        if (typeof algorithm.length !== \"number\") {\n            throw new TypeError(\"length: Is not of type Number\");\n        }\n        switch (algorithm.length) {\n            case 128:\n            case 192:\n            case 256:\n                break;\n            default:\n                throw new TypeError(\"length: Must be 128, 192, or 256\");\n        }\n    }\n    checkDerivedKeyParams(algorithm) {\n        this.checkGenerateKeyParams(algorithm);\n    }\n}\n\nclass AesCbcProvider extends AesProvider {\n    constructor() {\n        super(...arguments);\n        this.name = \"AES-CBC\";\n        this.usages = [\"encrypt\", \"decrypt\", \"wrapKey\", \"unwrapKey\"];\n    }\n    checkAlgorithmParams(algorithm) {\n        this.checkRequiredProperty(algorithm, \"iv\");\n        if (!(algorithm.iv instanceof ArrayBuffer || ArrayBuffer.isView(algorithm.iv))) {\n            throw new TypeError(\"iv: Is not of type '(ArrayBuffer or ArrayBufferView)'\");\n        }\n        if (algorithm.iv.byteLength !== 16) {\n            throw new TypeError(\"iv: Must have length 16 bytes\");\n        }\n    }\n}\n\nclass AesCmacProvider extends AesProvider {\n    constructor() {\n        super(...arguments);\n        this.name = \"AES-CMAC\";\n        this.usages = [\"sign\", \"verify\"];\n    }\n    checkAlgorithmParams(algorithm) {\n        this.checkRequiredProperty(algorithm, \"length\");\n        if (typeof algorithm.length !== \"number\") {\n            throw new TypeError(\"length: Is not a Number\");\n        }\n        if (algorithm.length < 1) {\n            throw new OperationError(\"length: Must be more than 0\");\n        }\n    }\n}\n\nclass AesCtrProvider extends AesProvider {\n    constructor() {\n        super(...arguments);\n        this.name = \"AES-CTR\";\n        this.usages = [\"encrypt\", \"decrypt\", \"wrapKey\", \"unwrapKey\"];\n    }\n    checkAlgorithmParams(algorithm) {\n        this.checkRequiredProperty(algorithm, \"counter\");\n        if (!(algorithm.counter instanceof ArrayBuffer || ArrayBuffer.isView(algorithm.counter))) {\n            throw new TypeError(\"counter: Is not of type '(ArrayBuffer or ArrayBufferView)'\");\n        }\n        if (algorithm.counter.byteLength !== 16) {\n            throw new TypeError(\"iv: Must have length 16 bytes\");\n        }\n        this.checkRequiredProperty(algorithm, \"length\");\n        if (typeof algorithm.length !== \"number\") {\n            throw new TypeError(\"length: Is not a Number\");\n        }\n        if (algorithm.length < 1) {\n            throw new OperationError(\"length: Must be more than 0\");\n        }\n    }\n}\n\nclass AesEcbProvider extends AesProvider {\n    constructor() {\n        super(...arguments);\n        this.name = \"AES-ECB\";\n        this.usages = [\"encrypt\", \"decrypt\", \"wrapKey\", \"unwrapKey\"];\n    }\n}\n\nclass AesGcmProvider extends AesProvider {\n    constructor() {\n        super(...arguments);\n        this.name = \"AES-GCM\";\n        this.usages = [\"encrypt\", \"decrypt\", \"wrapKey\", \"unwrapKey\"];\n    }\n    checkAlgorithmParams(algorithm) {\n        var _a;\n        this.checkRequiredProperty(algorithm, \"iv\");\n        if (!(algorithm.iv instanceof ArrayBuffer || ArrayBuffer.isView(algorithm.iv))) {\n            throw new TypeError(\"iv: Is not of type '(ArrayBuffer or ArrayBufferView)'\");\n        }\n        if (algorithm.iv.byteLength < 1) {\n            throw new OperationError(\"iv: Must have length more than 0 and less than 2^64 - 1\");\n        }\n        (_a = algorithm.tagLength) !== null && _a !== void 0 ? _a : (algorithm.tagLength = 128);\n        switch (algorithm.tagLength) {\n            case 32:\n            case 64:\n            case 96:\n            case 104:\n            case 112:\n            case 120:\n            case 128:\n                break;\n            default:\n                throw new OperationError(\"tagLength: Must be one of 32, 64, 96, 104, 112, 120 or 128\");\n        }\n    }\n}\n\nclass AesKwProvider extends AesProvider {\n    constructor() {\n        super(...arguments);\n        this.name = \"AES-KW\";\n        this.usages = [\"wrapKey\", \"unwrapKey\"];\n    }\n}\n\nclass DesProvider extends ProviderCrypto {\n    constructor() {\n        super(...arguments);\n        this.usages = [\"encrypt\", \"decrypt\", \"wrapKey\", \"unwrapKey\"];\n    }\n    checkAlgorithmParams(algorithm) {\n        if (this.ivSize) {\n            this.checkRequiredProperty(algorithm, \"iv\");\n            if (!(algorithm.iv instanceof ArrayBuffer || ArrayBuffer.isView(algorithm.iv))) {\n                throw new TypeError(\"iv: Is not of type '(ArrayBuffer or ArrayBufferView)'\");\n            }\n            if (algorithm.iv.byteLength !== this.ivSize) {\n                throw new TypeError(`iv: Must have length ${this.ivSize} bytes`);\n            }\n        }\n    }\n    checkGenerateKeyParams(algorithm) {\n        this.checkRequiredProperty(algorithm, \"length\");\n        if (typeof algorithm.length !== \"number\") {\n            throw new TypeError(\"length: Is not of type Number\");\n        }\n        if (algorithm.length !== this.keySizeBits) {\n            throw new OperationError(`algorithm.length: Must be ${this.keySizeBits}`);\n        }\n    }\n    checkDerivedKeyParams(algorithm) {\n        this.checkGenerateKeyParams(algorithm);\n    }\n}\n\nclass RsaProvider extends ProviderCrypto {\n    constructor() {\n        super(...arguments);\n        this.hashAlgorithms = [\"SHA-1\", \"SHA-256\", \"SHA-384\", \"SHA-512\"];\n    }\n    checkGenerateKeyParams(algorithm) {\n        this.checkRequiredProperty(algorithm, \"hash\");\n        this.checkHashAlgorithm(algorithm.hash, this.hashAlgorithms);\n        this.checkRequiredProperty(algorithm, \"publicExponent\");\n        if (!(algorithm.publicExponent && algorithm.publicExponent instanceof Uint8Array)) {\n            throw new TypeError(\"publicExponent: Missing or not a Uint8Array\");\n        }\n        const publicExponent = pvtsutils__WEBPACK_IMPORTED_MODULE_0__.Convert.ToBase64(algorithm.publicExponent);\n        if (!(publicExponent === \"Aw==\" || publicExponent === \"AQAB\")) {\n            throw new TypeError(\"publicExponent: Must be [3] or [1,0,1]\");\n        }\n        this.checkRequiredProperty(algorithm, \"modulusLength\");\n        if (algorithm.modulusLength % 8\n            || algorithm.modulusLength < 256\n            || algorithm.modulusLength > 16384) {\n            throw new TypeError(\"The modulus length must be a multiple of 8 bits and >= 256 and <= 16384\");\n        }\n    }\n    checkImportParams(algorithm) {\n        this.checkRequiredProperty(algorithm, \"hash\");\n        this.checkHashAlgorithm(algorithm.hash, this.hashAlgorithms);\n    }\n}\n\nclass RsaSsaProvider extends RsaProvider {\n    constructor() {\n        super(...arguments);\n        this.name = \"RSASSA-PKCS1-v1_5\";\n        this.usages = {\n            privateKey: [\"sign\"],\n            publicKey: [\"verify\"],\n        };\n    }\n}\n\nclass RsaPssProvider extends RsaProvider {\n    constructor() {\n        super(...arguments);\n        this.name = \"RSA-PSS\";\n        this.usages = {\n            privateKey: [\"sign\"],\n            publicKey: [\"verify\"],\n        };\n    }\n    checkAlgorithmParams(algorithm) {\n        this.checkRequiredProperty(algorithm, \"saltLength\");\n        if (typeof algorithm.saltLength !== \"number\") {\n            throw new TypeError(\"saltLength: Is not a Number\");\n        }\n        if (algorithm.saltLength < 0) {\n            throw new RangeError(\"saltLength: Must be positive number\");\n        }\n    }\n}\n\nclass RsaOaepProvider extends RsaProvider {\n    constructor() {\n        super(...arguments);\n        this.name = \"RSA-OAEP\";\n        this.usages = {\n            privateKey: [\"decrypt\", \"unwrapKey\"],\n            publicKey: [\"encrypt\", \"wrapKey\"],\n        };\n    }\n    checkAlgorithmParams(algorithm) {\n        if (algorithm.label\n            && !(algorithm.label instanceof ArrayBuffer || ArrayBuffer.isView(algorithm.label))) {\n            throw new TypeError(\"label: Is not of type '(ArrayBuffer or ArrayBufferView)'\");\n        }\n    }\n}\n\nclass EllipticProvider extends ProviderCrypto {\n    checkGenerateKeyParams(algorithm) {\n        this.checkRequiredProperty(algorithm, \"namedCurve\");\n        this.checkNamedCurve(algorithm.namedCurve);\n    }\n    checkNamedCurve(namedCurve) {\n        for (const item of this.namedCurves) {\n            if (item.toLowerCase() === namedCurve.toLowerCase()) {\n                return;\n            }\n        }\n        throw new OperationError(`namedCurve: Must be one of ${this.namedCurves.join(\", \")}`);\n    }\n}\n\nclass EcdsaProvider extends EllipticProvider {\n    constructor() {\n        super(...arguments);\n        this.name = \"ECDSA\";\n        this.hashAlgorithms = [\"SHA-1\", \"SHA-256\", \"SHA-384\", \"SHA-512\"];\n        this.usages = {\n            privateKey: [\"sign\"],\n            publicKey: [\"verify\"],\n        };\n        this.namedCurves = [\"P-256\", \"P-384\", \"P-521\", \"K-256\"];\n    }\n    checkAlgorithmParams(algorithm) {\n        this.checkRequiredProperty(algorithm, \"hash\");\n        this.checkHashAlgorithm(algorithm.hash, this.hashAlgorithms);\n    }\n}\n\nconst KEY_TYPES = [\"secret\", \"private\", \"public\"];\nclass CryptoKey {\n    static create(algorithm, type, extractable, usages) {\n        const key = new this();\n        key.algorithm = algorithm;\n        key.type = type;\n        key.extractable = extractable;\n        key.usages = usages;\n        return key;\n    }\n    static isKeyType(data) {\n        return KEY_TYPES.indexOf(data) !== -1;\n    }\n    get [Symbol.toStringTag]() {\n        return \"CryptoKey\";\n    }\n}\n\nclass EcdhProvider extends EllipticProvider {\n    constructor() {\n        super(...arguments);\n        this.name = \"ECDH\";\n        this.usages = {\n            privateKey: [\"deriveBits\", \"deriveKey\"],\n            publicKey: [],\n        };\n        this.namedCurves = [\"P-256\", \"P-384\", \"P-521\", \"K-256\"];\n    }\n    checkAlgorithmParams(algorithm) {\n        this.checkRequiredProperty(algorithm, \"public\");\n        if (!(algorithm.public instanceof CryptoKey)) {\n            throw new TypeError(\"public: Is not a CryptoKey\");\n        }\n        if (algorithm.public.type !== \"public\") {\n            throw new OperationError(\"public: Is not a public key\");\n        }\n        if (algorithm.public.algorithm.name !== this.name) {\n            throw new OperationError(`public: Is not ${this.name} key`);\n        }\n    }\n}\n\nclass EcdhEsProvider extends EcdhProvider {\n    constructor() {\n        super(...arguments);\n        this.name = \"ECDH-ES\";\n        this.namedCurves = [\"X25519\", \"X448\"];\n    }\n}\n\nclass EdDsaProvider extends EllipticProvider {\n    constructor() {\n        super(...arguments);\n        this.name = \"EdDSA\";\n        this.usages = {\n            privateKey: [\"sign\"],\n            publicKey: [\"verify\"],\n        };\n        this.namedCurves = [\"Ed25519\", \"Ed448\"];\n    }\n}\n\nlet ObjectIdentifier = class ObjectIdentifier {\n    constructor(value) {\n        if (value) {\n            this.value = value;\n        }\n    }\n};\n(0,tslib__WEBPACK_IMPORTED_MODULE_4__.__decorate)([\n    (0,_peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnProp)({ type: _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnPropTypes.ObjectIdentifier })\n], ObjectIdentifier.prototype, \"value\", void 0);\nObjectIdentifier = (0,tslib__WEBPACK_IMPORTED_MODULE_4__.__decorate)([\n    (0,_peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnType)({ type: _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnTypeTypes.Choice })\n], ObjectIdentifier);\n\nclass AlgorithmIdentifier {\n    constructor(params) {\n        Object.assign(this, params);\n    }\n}\n(0,tslib__WEBPACK_IMPORTED_MODULE_4__.__decorate)([\n    (0,_peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnProp)({\n        type: _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnPropTypes.ObjectIdentifier,\n    })\n], AlgorithmIdentifier.prototype, \"algorithm\", void 0);\n(0,tslib__WEBPACK_IMPORTED_MODULE_4__.__decorate)([\n    (0,_peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnProp)({\n        type: _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnPropTypes.Any,\n        optional: true,\n    })\n], AlgorithmIdentifier.prototype, \"parameters\", void 0);\n\nclass PrivateKeyInfo {\n    constructor() {\n        this.version = 0;\n        this.privateKeyAlgorithm = new AlgorithmIdentifier();\n        this.privateKey = new ArrayBuffer(0);\n    }\n}\n(0,tslib__WEBPACK_IMPORTED_MODULE_4__.__decorate)([\n    (0,_peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnProp)({ type: _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnPropTypes.Integer })\n], PrivateKeyInfo.prototype, \"version\", void 0);\n(0,tslib__WEBPACK_IMPORTED_MODULE_4__.__decorate)([\n    (0,_peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnProp)({ type: AlgorithmIdentifier })\n], PrivateKeyInfo.prototype, \"privateKeyAlgorithm\", void 0);\n(0,tslib__WEBPACK_IMPORTED_MODULE_4__.__decorate)([\n    (0,_peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnProp)({ type: _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnPropTypes.OctetString })\n], PrivateKeyInfo.prototype, \"privateKey\", void 0);\n(0,tslib__WEBPACK_IMPORTED_MODULE_4__.__decorate)([\n    (0,_peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnProp)({ type: _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnPropTypes.Any, optional: true })\n], PrivateKeyInfo.prototype, \"attributes\", void 0);\n\nclass PublicKeyInfo {\n    constructor() {\n        this.publicKeyAlgorithm = new AlgorithmIdentifier();\n        this.publicKey = new ArrayBuffer(0);\n    }\n}\n(0,tslib__WEBPACK_IMPORTED_MODULE_4__.__decorate)([\n    (0,_peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnProp)({ type: AlgorithmIdentifier })\n], PublicKeyInfo.prototype, \"publicKeyAlgorithm\", void 0);\n(0,tslib__WEBPACK_IMPORTED_MODULE_4__.__decorate)([\n    (0,_peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnProp)({ type: _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnPropTypes.BitString })\n], PublicKeyInfo.prototype, \"publicKey\", void 0);\n\nconst JsonBase64UrlArrayBufferConverter = {\n    fromJSON: (value) => pvtsutils__WEBPACK_IMPORTED_MODULE_0__.Convert.FromBase64Url(value),\n    toJSON: (value) => pvtsutils__WEBPACK_IMPORTED_MODULE_0__.Convert.ToBase64Url(new Uint8Array(value)),\n};\n\nconst AsnIntegerArrayBufferConverter = {\n    fromASN: (value) => {\n        const valueHex = value.valueBlock.valueHex;\n        return !(new Uint8Array(valueHex)[0])\n            ? value.valueBlock.valueHex.slice(1)\n            : value.valueBlock.valueHex;\n    },\n    toASN: (value) => {\n        const valueHex = new Uint8Array(value)[0] > 127\n            ? (0,pvtsutils__WEBPACK_IMPORTED_MODULE_0__.combine)(new Uint8Array([0]).buffer, value)\n            : value;\n        return new asn1js__WEBPACK_IMPORTED_MODULE_3__.Integer({ valueHex });\n    },\n};\n\nvar index$3 = /*#__PURE__*/Object.freeze({\n  __proto__: null,\n  AsnIntegerArrayBufferConverter: AsnIntegerArrayBufferConverter,\n  JsonBase64UrlArrayBufferConverter: JsonBase64UrlArrayBufferConverter\n});\n\nclass RsaPrivateKey {\n    constructor() {\n        this.version = 0;\n        this.modulus = new ArrayBuffer(0);\n        this.publicExponent = new ArrayBuffer(0);\n        this.privateExponent = new ArrayBuffer(0);\n        this.prime1 = new ArrayBuffer(0);\n        this.prime2 = new ArrayBuffer(0);\n        this.exponent1 = new ArrayBuffer(0);\n        this.exponent2 = new ArrayBuffer(0);\n        this.coefficient = new ArrayBuffer(0);\n    }\n}\n(0,tslib__WEBPACK_IMPORTED_MODULE_4__.__decorate)([\n    (0,_peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnProp)({ type: _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnPropTypes.Integer, converter: _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnIntegerConverter })\n], RsaPrivateKey.prototype, \"version\", void 0);\n(0,tslib__WEBPACK_IMPORTED_MODULE_4__.__decorate)([\n    (0,_peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnProp)({ type: _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnPropTypes.Integer, converter: AsnIntegerArrayBufferConverter }),\n    (0,_peculiar_json_schema__WEBPACK_IMPORTED_MODULE_2__.JsonProp)({ name: \"n\", converter: JsonBase64UrlArrayBufferConverter })\n], RsaPrivateKey.prototype, \"modulus\", void 0);\n(0,tslib__WEBPACK_IMPORTED_MODULE_4__.__decorate)([\n    (0,_peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnProp)({ type: _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnPropTypes.Integer, converter: AsnIntegerArrayBufferConverter }),\n    (0,_peculiar_json_schema__WEBPACK_IMPORTED_MODULE_2__.JsonProp)({ name: \"e\", converter: JsonBase64UrlArrayBufferConverter })\n], RsaPrivateKey.prototype, \"publicExponent\", void 0);\n(0,tslib__WEBPACK_IMPORTED_MODULE_4__.__decorate)([\n    (0,_peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnProp)({ type: _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnPropTypes.Integer, converter: AsnIntegerArrayBufferConverter }),\n    (0,_peculiar_json_schema__WEBPACK_IMPORTED_MODULE_2__.JsonProp)({ name: \"d\", converter: JsonBase64UrlArrayBufferConverter })\n], RsaPrivateKey.prototype, \"privateExponent\", void 0);\n(0,tslib__WEBPACK_IMPORTED_MODULE_4__.__decorate)([\n    (0,_peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnProp)({ type: _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnPropTypes.Integer, converter: AsnIntegerArrayBufferConverter }),\n    (0,_peculiar_json_schema__WEBPACK_IMPORTED_MODULE_2__.JsonProp)({ name: \"p\", converter: JsonBase64UrlArrayBufferConverter })\n], RsaPrivateKey.prototype, \"prime1\", void 0);\n(0,tslib__WEBPACK_IMPORTED_MODULE_4__.__decorate)([\n    (0,_peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnProp)({ type: _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnPropTypes.Integer, converter: AsnIntegerArrayBufferConverter }),\n    (0,_peculiar_json_schema__WEBPACK_IMPORTED_MODULE_2__.JsonProp)({ name: \"q\", converter: JsonBase64UrlArrayBufferConverter })\n], RsaPrivateKey.prototype, \"prime2\", void 0);\n(0,tslib__WEBPACK_IMPORTED_MODULE_4__.__decorate)([\n    (0,_peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnProp)({ type: _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnPropTypes.Integer, converter: AsnIntegerArrayBufferConverter }),\n    (0,_peculiar_json_schema__WEBPACK_IMPORTED_MODULE_2__.JsonProp)({ name: \"dp\", converter: JsonBase64UrlArrayBufferConverter })\n], RsaPrivateKey.prototype, \"exponent1\", void 0);\n(0,tslib__WEBPACK_IMPORTED_MODULE_4__.__decorate)([\n    (0,_peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnProp)({ type: _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnPropTypes.Integer, converter: AsnIntegerArrayBufferConverter }),\n    (0,_peculiar_json_schema__WEBPACK_IMPORTED_MODULE_2__.JsonProp)({ name: \"dq\", converter: JsonBase64UrlArrayBufferConverter })\n], RsaPrivateKey.prototype, \"exponent2\", void 0);\n(0,tslib__WEBPACK_IMPORTED_MODULE_4__.__decorate)([\n    (0,_peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnProp)({ type: _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnPropTypes.Integer, converter: AsnIntegerArrayBufferConverter }),\n    (0,_peculiar_json_schema__WEBPACK_IMPORTED_MODULE_2__.JsonProp)({ name: \"qi\", converter: JsonBase64UrlArrayBufferConverter })\n], RsaPrivateKey.prototype, \"coefficient\", void 0);\n(0,tslib__WEBPACK_IMPORTED_MODULE_4__.__decorate)([\n    (0,_peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnProp)({ type: _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnPropTypes.Any, optional: true })\n], RsaPrivateKey.prototype, \"otherPrimeInfos\", void 0);\n\nclass RsaPublicKey {\n    constructor() {\n        this.modulus = new ArrayBuffer(0);\n        this.publicExponent = new ArrayBuffer(0);\n    }\n}\n(0,tslib__WEBPACK_IMPORTED_MODULE_4__.__decorate)([\n    (0,_peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnProp)({ type: _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnPropTypes.Integer, converter: AsnIntegerArrayBufferConverter }),\n    (0,_peculiar_json_schema__WEBPACK_IMPORTED_MODULE_2__.JsonProp)({ name: \"n\", converter: JsonBase64UrlArrayBufferConverter })\n], RsaPublicKey.prototype, \"modulus\", void 0);\n(0,tslib__WEBPACK_IMPORTED_MODULE_4__.__decorate)([\n    (0,_peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnProp)({ type: _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnPropTypes.Integer, converter: AsnIntegerArrayBufferConverter }),\n    (0,_peculiar_json_schema__WEBPACK_IMPORTED_MODULE_2__.JsonProp)({ name: \"e\", converter: JsonBase64UrlArrayBufferConverter })\n], RsaPublicKey.prototype, \"publicExponent\", void 0);\n\nlet EcPublicKey = class EcPublicKey {\n    constructor(value) {\n        this.value = new ArrayBuffer(0);\n        if (value) {\n            this.value = value;\n        }\n    }\n    toJSON() {\n        let bytes = new Uint8Array(this.value);\n        if (bytes[0] !== 0x04) {\n            throw new CryptoError(\"Wrong ECPoint. Current version supports only Uncompressed (0x04) point\");\n        }\n        bytes = new Uint8Array(this.value.slice(1));\n        const size = bytes.length / 2;\n        const offset = 0;\n        const json = {\n            x: pvtsutils__WEBPACK_IMPORTED_MODULE_0__.Convert.ToBase64Url(bytes.buffer.slice(offset, offset + size)),\n            y: pvtsutils__WEBPACK_IMPORTED_MODULE_0__.Convert.ToBase64Url(bytes.buffer.slice(offset + size, offset + size + size)),\n        };\n        return json;\n    }\n    fromJSON(json) {\n        if (!(\"x\" in json)) {\n            throw new Error(\"x: Missing required property\");\n        }\n        if (!(\"y\" in json)) {\n            throw new Error(\"y: Missing required property\");\n        }\n        const x = pvtsutils__WEBPACK_IMPORTED_MODULE_0__.Convert.FromBase64Url(json.x);\n        const y = pvtsutils__WEBPACK_IMPORTED_MODULE_0__.Convert.FromBase64Url(json.y);\n        const value = (0,pvtsutils__WEBPACK_IMPORTED_MODULE_0__.combine)(new Uint8Array([0x04]).buffer, x, y);\n        this.value = new Uint8Array(value).buffer;\n        return this;\n    }\n};\n(0,tslib__WEBPACK_IMPORTED_MODULE_4__.__decorate)([\n    (0,_peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnProp)({ type: _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnPropTypes.OctetString })\n], EcPublicKey.prototype, \"value\", void 0);\nEcPublicKey = (0,tslib__WEBPACK_IMPORTED_MODULE_4__.__decorate)([\n    (0,_peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnType)({ type: _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnTypeTypes.Choice })\n], EcPublicKey);\n\nclass EcPrivateKey {\n    constructor() {\n        this.version = 1;\n        this.privateKey = new ArrayBuffer(0);\n    }\n    fromJSON(json) {\n        if (!(\"d\" in json)) {\n            throw new Error(\"d: Missing required property\");\n        }\n        this.privateKey = pvtsutils__WEBPACK_IMPORTED_MODULE_0__.Convert.FromBase64Url(json.d);\n        if (\"x\" in json) {\n            const publicKey = new EcPublicKey();\n            publicKey.fromJSON(json);\n            const asn = _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnSerializer.toASN(publicKey);\n            if (\"valueHex\" in asn.valueBlock) {\n                this.publicKey = asn.valueBlock.valueHex;\n            }\n        }\n        return this;\n    }\n    toJSON() {\n        const jwk = {};\n        jwk.d = pvtsutils__WEBPACK_IMPORTED_MODULE_0__.Convert.ToBase64Url(this.privateKey);\n        if (this.publicKey) {\n            Object.assign(jwk, new EcPublicKey(this.publicKey).toJSON());\n        }\n        return jwk;\n    }\n}\n(0,tslib__WEBPACK_IMPORTED_MODULE_4__.__decorate)([\n    (0,_peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnProp)({ type: _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnPropTypes.Integer, converter: _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnIntegerConverter })\n], EcPrivateKey.prototype, \"version\", void 0);\n(0,tslib__WEBPACK_IMPORTED_MODULE_4__.__decorate)([\n    (0,_peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnProp)({ type: _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnPropTypes.OctetString })\n], EcPrivateKey.prototype, \"privateKey\", void 0);\n(0,tslib__WEBPACK_IMPORTED_MODULE_4__.__decorate)([\n    (0,_peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnProp)({ context: 0, type: _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnPropTypes.Any, optional: true })\n], EcPrivateKey.prototype, \"parameters\", void 0);\n(0,tslib__WEBPACK_IMPORTED_MODULE_4__.__decorate)([\n    (0,_peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnProp)({ context: 1, type: _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnPropTypes.BitString, optional: true })\n], EcPrivateKey.prototype, \"publicKey\", void 0);\n\nconst AsnIntegerWithoutPaddingConverter = {\n    fromASN: (value) => {\n        const bytes = new Uint8Array(value.valueBlock.valueHex);\n        return (bytes[0] === 0)\n            ? bytes.buffer.slice(1)\n            : bytes.buffer;\n    },\n    toASN: (value) => {\n        const bytes = new Uint8Array(value);\n        if (bytes[0] > 127) {\n            const newValue = new Uint8Array(bytes.length + 1);\n            newValue.set(bytes, 1);\n            return new asn1js__WEBPACK_IMPORTED_MODULE_3__.Integer({ valueHex: newValue.buffer });\n        }\n        return new asn1js__WEBPACK_IMPORTED_MODULE_3__.Integer({ valueHex: value });\n    },\n};\n\nvar index$2 = /*#__PURE__*/Object.freeze({\n  __proto__: null,\n  AsnIntegerWithoutPaddingConverter: AsnIntegerWithoutPaddingConverter\n});\n\nclass EcUtils {\n    static decodePoint(data, pointSize) {\n        const view = pvtsutils__WEBPACK_IMPORTED_MODULE_0__.BufferSourceConverter.toUint8Array(data);\n        if ((view.length === 0) || (view[0] !== 4)) {\n            throw new Error(\"Only uncompressed point format supported\");\n        }\n        const n = (view.length - 1) / 2;\n        if (n !== (Math.ceil(pointSize / 8))) {\n            throw new Error(\"Point does not match field size\");\n        }\n        const xb = view.slice(1, n + 1);\n        const yb = view.slice(n + 1, n + 1 + n);\n        return { x: xb, y: yb };\n    }\n    static encodePoint(point, pointSize) {\n        const size = Math.ceil(pointSize / 8);\n        if (point.x.byteLength !== size || point.y.byteLength !== size) {\n            throw new Error(\"X,Y coordinates don't match point size criteria\");\n        }\n        const x = pvtsutils__WEBPACK_IMPORTED_MODULE_0__.BufferSourceConverter.toUint8Array(point.x);\n        const y = pvtsutils__WEBPACK_IMPORTED_MODULE_0__.BufferSourceConverter.toUint8Array(point.y);\n        const res = new Uint8Array(size * 2 + 1);\n        res[0] = 4;\n        res.set(x, 1);\n        res.set(y, size + 1);\n        return res;\n    }\n    static getSize(pointSize) {\n        return Math.ceil(pointSize / 8);\n    }\n    static encodeSignature(signature, pointSize) {\n        const size = this.getSize(pointSize);\n        const r = pvtsutils__WEBPACK_IMPORTED_MODULE_0__.BufferSourceConverter.toUint8Array(signature.r);\n        const s = pvtsutils__WEBPACK_IMPORTED_MODULE_0__.BufferSourceConverter.toUint8Array(signature.s);\n        const res = new Uint8Array(size * 2);\n        res.set(this.padStart(r, size));\n        res.set(this.padStart(s, size), size);\n        return res;\n    }\n    static decodeSignature(data, pointSize) {\n        const size = this.getSize(pointSize);\n        const view = pvtsutils__WEBPACK_IMPORTED_MODULE_0__.BufferSourceConverter.toUint8Array(data);\n        if (view.length !== (size * 2)) {\n            throw new Error(\"Incorrect size of the signature\");\n        }\n        const r = view.slice(0, size);\n        const s = view.slice(size);\n        return {\n            r: this.trimStart(r),\n            s: this.trimStart(s),\n        };\n    }\n    static trimStart(data) {\n        let i = 0;\n        while ((i < data.length - 1) && (data[i] === 0)) {\n            i++;\n        }\n        if (i === 0) {\n            return data;\n        }\n        return data.slice(i, data.length);\n    }\n    static padStart(data, size) {\n        if (size === data.length) {\n            return data;\n        }\n        const res = new Uint8Array(size);\n        res.set(data, size - data.length);\n        return res;\n    }\n}\n\nclass EcDsaSignature {\n    constructor() {\n        this.r = new ArrayBuffer(0);\n        this.s = new ArrayBuffer(0);\n    }\n    static fromWebCryptoSignature(value) {\n        const pointSize = value.byteLength / 2;\n        const point = EcUtils.decodeSignature(value, pointSize * 8);\n        const ecSignature = new EcDsaSignature();\n        ecSignature.r = pvtsutils__WEBPACK_IMPORTED_MODULE_0__.BufferSourceConverter.toArrayBuffer(point.r);\n        ecSignature.s = pvtsutils__WEBPACK_IMPORTED_MODULE_0__.BufferSourceConverter.toArrayBuffer(point.s);\n        return ecSignature;\n    }\n    toWebCryptoSignature(pointSize) {\n        if (!pointSize) {\n            const maxPointLength = Math.max(this.r.byteLength, this.s.byteLength);\n            if (maxPointLength <= 32) {\n                pointSize = 256;\n            }\n            else if (maxPointLength <= 48) {\n                pointSize = 384;\n            }\n            else {\n                pointSize = 521;\n            }\n        }\n        const signature = EcUtils.encodeSignature(this, pointSize);\n        return signature.buffer;\n    }\n}\n(0,tslib__WEBPACK_IMPORTED_MODULE_4__.__decorate)([\n    (0,_peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnProp)({ type: _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnPropTypes.Integer, converter: AsnIntegerWithoutPaddingConverter })\n], EcDsaSignature.prototype, \"r\", void 0);\n(0,tslib__WEBPACK_IMPORTED_MODULE_4__.__decorate)([\n    (0,_peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnProp)({ type: _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnPropTypes.Integer, converter: AsnIntegerWithoutPaddingConverter })\n], EcDsaSignature.prototype, \"s\", void 0);\n\nclass OneAsymmetricKey extends PrivateKeyInfo {\n}\n(0,tslib__WEBPACK_IMPORTED_MODULE_4__.__decorate)([\n    (0,_peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnProp)({ context: 1, implicit: true, type: _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnPropTypes.BitString, optional: true })\n], OneAsymmetricKey.prototype, \"publicKey\", void 0);\n\nlet EdPrivateKey = class EdPrivateKey {\n    constructor() {\n        this.value = new ArrayBuffer(0);\n    }\n    fromJSON(json) {\n        if (!json.d) {\n            throw new Error(\"d: Missing required property\");\n        }\n        this.value = pvtsutils__WEBPACK_IMPORTED_MODULE_0__.Convert.FromBase64Url(json.d);\n        return this;\n    }\n    toJSON() {\n        const jwk = {\n            d: pvtsutils__WEBPACK_IMPORTED_MODULE_0__.Convert.ToBase64Url(this.value),\n        };\n        return jwk;\n    }\n};\n(0,tslib__WEBPACK_IMPORTED_MODULE_4__.__decorate)([\n    (0,_peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnProp)({ type: _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnPropTypes.OctetString })\n], EdPrivateKey.prototype, \"value\", void 0);\nEdPrivateKey = (0,tslib__WEBPACK_IMPORTED_MODULE_4__.__decorate)([\n    (0,_peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnType)({ type: _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnTypeTypes.Choice })\n], EdPrivateKey);\n\nlet EdPublicKey = class EdPublicKey {\n    constructor(value) {\n        this.value = new ArrayBuffer(0);\n        if (value) {\n            this.value = value;\n        }\n    }\n    toJSON() {\n        const json = {\n            x: pvtsutils__WEBPACK_IMPORTED_MODULE_0__.Convert.ToBase64Url(this.value),\n        };\n        return json;\n    }\n    fromJSON(json) {\n        if (!(\"x\" in json)) {\n            throw new Error(\"x: Missing required property\");\n        }\n        this.value = pvtsutils__WEBPACK_IMPORTED_MODULE_0__.Convert.FromBase64Url(json.x);\n        return this;\n    }\n};\n(0,tslib__WEBPACK_IMPORTED_MODULE_4__.__decorate)([\n    (0,_peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnProp)({ type: _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnPropTypes.BitString })\n], EdPublicKey.prototype, \"value\", void 0);\nEdPublicKey = (0,tslib__WEBPACK_IMPORTED_MODULE_4__.__decorate)([\n    (0,_peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnType)({ type: _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnTypeTypes.Choice })\n], EdPublicKey);\n\nlet CurvePrivateKey = class CurvePrivateKey {\n};\n(0,tslib__WEBPACK_IMPORTED_MODULE_4__.__decorate)([\n    (0,_peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnProp)({ type: _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnPropTypes.OctetString }),\n    (0,_peculiar_json_schema__WEBPACK_IMPORTED_MODULE_2__.JsonProp)({ type: _peculiar_json_schema__WEBPACK_IMPORTED_MODULE_2__.JsonPropTypes.String, converter: JsonBase64UrlArrayBufferConverter })\n], CurvePrivateKey.prototype, \"d\", void 0);\nCurvePrivateKey = (0,tslib__WEBPACK_IMPORTED_MODULE_4__.__decorate)([\n    (0,_peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnType)({ type: _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnTypeTypes.Choice })\n], CurvePrivateKey);\n\nconst idSecp256r1 = \"1.2.840.10045.3.1.7\";\nconst idEllipticCurve = \"*********\";\nconst idSecp384r1 = `${idEllipticCurve}.34`;\nconst idSecp521r1 = `${idEllipticCurve}.35`;\nconst idSecp256k1 = `${idEllipticCurve}.10`;\nconst idVersionOne = \"********.*******.1\";\nconst idBrainpoolP160r1 = `${idVersionOne}.1`;\nconst idBrainpoolP160t1 = `${idVersionOne}.2`;\nconst idBrainpoolP192r1 = `${idVersionOne}.3`;\nconst idBrainpoolP192t1 = `${idVersionOne}.4`;\nconst idBrainpoolP224r1 = `${idVersionOne}.5`;\nconst idBrainpoolP224t1 = `${idVersionOne}.6`;\nconst idBrainpoolP256r1 = `${idVersionOne}.7`;\nconst idBrainpoolP256t1 = `${idVersionOne}.8`;\nconst idBrainpoolP320r1 = `${idVersionOne}.9`;\nconst idBrainpoolP320t1 = `${idVersionOne}.10`;\nconst idBrainpoolP384r1 = `${idVersionOne}.11`;\nconst idBrainpoolP384t1 = `${idVersionOne}.12`;\nconst idBrainpoolP512r1 = `${idVersionOne}.13`;\nconst idBrainpoolP512t1 = `${idVersionOne}.14`;\nconst idX25519 = \"***********\";\nconst idX448 = \"***********\";\nconst idEd25519 = \"***********\";\nconst idEd448 = \"***********\";\n\nvar index$1 = /*#__PURE__*/Object.freeze({\n  __proto__: null,\n  AlgorithmIdentifier: AlgorithmIdentifier,\n  get CurvePrivateKey () { return CurvePrivateKey; },\n  EcDsaSignature: EcDsaSignature,\n  EcPrivateKey: EcPrivateKey,\n  get EcPublicKey () { return EcPublicKey; },\n  get EdPrivateKey () { return EdPrivateKey; },\n  get EdPublicKey () { return EdPublicKey; },\n  get ObjectIdentifier () { return ObjectIdentifier; },\n  OneAsymmetricKey: OneAsymmetricKey,\n  PrivateKeyInfo: PrivateKeyInfo,\n  PublicKeyInfo: PublicKeyInfo,\n  RsaPrivateKey: RsaPrivateKey,\n  RsaPublicKey: RsaPublicKey,\n  converters: index$2,\n  idBrainpoolP160r1: idBrainpoolP160r1,\n  idBrainpoolP160t1: idBrainpoolP160t1,\n  idBrainpoolP192r1: idBrainpoolP192r1,\n  idBrainpoolP192t1: idBrainpoolP192t1,\n  idBrainpoolP224r1: idBrainpoolP224r1,\n  idBrainpoolP224t1: idBrainpoolP224t1,\n  idBrainpoolP256r1: idBrainpoolP256r1,\n  idBrainpoolP256t1: idBrainpoolP256t1,\n  idBrainpoolP320r1: idBrainpoolP320r1,\n  idBrainpoolP320t1: idBrainpoolP320t1,\n  idBrainpoolP384r1: idBrainpoolP384r1,\n  idBrainpoolP384t1: idBrainpoolP384t1,\n  idBrainpoolP512r1: idBrainpoolP512r1,\n  idBrainpoolP512t1: idBrainpoolP512t1,\n  idEd25519: idEd25519,\n  idEd448: idEd448,\n  idEllipticCurve: idEllipticCurve,\n  idSecp256k1: idSecp256k1,\n  idSecp256r1: idSecp256r1,\n  idSecp384r1: idSecp384r1,\n  idSecp521r1: idSecp521r1,\n  idVersionOne: idVersionOne,\n  idX25519: idX25519,\n  idX448: idX448\n});\n\nclass EcCurves {\n    constructor() { }\n    static register(item) {\n        const oid = new ObjectIdentifier();\n        oid.value = item.id;\n        const raw = _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnConvert.serialize(oid);\n        this.items.push({\n            ...item,\n            raw,\n        });\n        this.names.push(item.name);\n    }\n    static find(nameOrId) {\n        nameOrId = nameOrId.toUpperCase();\n        for (const item of this.items) {\n            if (item.name.toUpperCase() === nameOrId || item.id.toUpperCase() === nameOrId) {\n                return item;\n            }\n        }\n        return null;\n    }\n    static get(nameOrId) {\n        const res = this.find(nameOrId);\n        if (!res) {\n            throw new Error(`Unsupported EC named curve '${nameOrId}'`);\n        }\n        return res;\n    }\n}\nEcCurves.items = [];\nEcCurves.names = [];\nEcCurves.register({ name: \"P-256\", id: idSecp256r1, size: 256 });\nEcCurves.register({ name: \"P-384\", id: idSecp384r1, size: 384 });\nEcCurves.register({ name: \"P-521\", id: idSecp521r1, size: 521 });\nEcCurves.register({ name: \"K-256\", id: idSecp256k1, size: 256 });\nEcCurves.register({ name: \"brainpoolP160r1\", id: idBrainpoolP160r1, size: 160 });\nEcCurves.register({ name: \"brainpoolP160t1\", id: idBrainpoolP160t1, size: 160 });\nEcCurves.register({ name: \"brainpoolP192r1\", id: idBrainpoolP192r1, size: 192 });\nEcCurves.register({ name: \"brainpoolP192t1\", id: idBrainpoolP192t1, size: 192 });\nEcCurves.register({ name: \"brainpoolP224r1\", id: idBrainpoolP224r1, size: 224 });\nEcCurves.register({ name: \"brainpoolP224t1\", id: idBrainpoolP224t1, size: 224 });\nEcCurves.register({ name: \"brainpoolP256r1\", id: idBrainpoolP256r1, size: 256 });\nEcCurves.register({ name: \"brainpoolP256t1\", id: idBrainpoolP256t1, size: 256 });\nEcCurves.register({ name: \"brainpoolP320r1\", id: idBrainpoolP320r1, size: 320 });\nEcCurves.register({ name: \"brainpoolP320t1\", id: idBrainpoolP320t1, size: 320 });\nEcCurves.register({ name: \"brainpoolP384r1\", id: idBrainpoolP384r1, size: 384 });\nEcCurves.register({ name: \"brainpoolP384t1\", id: idBrainpoolP384t1, size: 384 });\nEcCurves.register({ name: \"brainpoolP512r1\", id: idBrainpoolP512r1, size: 512 });\nEcCurves.register({ name: \"brainpoolP512t1\", id: idBrainpoolP512t1, size: 512 });\n\nclass X25519Provider extends ProviderCrypto {\n    constructor() {\n        super(...arguments);\n        this.name = \"X25519\";\n        this.usages = {\n            privateKey: [\"deriveKey\", \"deriveBits\"],\n            publicKey: [],\n        };\n    }\n    checkAlgorithmParams(algorithm) {\n        this.checkRequiredProperty(algorithm, \"public\");\n    }\n}\n\nclass Ed25519Provider extends ProviderCrypto {\n    constructor() {\n        super(...arguments);\n        this.name = \"Ed25519\";\n        this.usages = {\n            privateKey: [\"sign\"],\n            publicKey: [\"verify\"],\n        };\n    }\n}\n\nclass HmacProvider extends ProviderCrypto {\n    constructor() {\n        super(...arguments);\n        this.name = \"HMAC\";\n        this.hashAlgorithms = [\"SHA-1\", \"SHA-256\", \"SHA-384\", \"SHA-512\"];\n        this.usages = [\"sign\", \"verify\"];\n    }\n    getDefaultLength(algName) {\n        switch (algName.toUpperCase()) {\n            case \"SHA-1\":\n            case \"SHA-256\":\n            case \"SHA-384\":\n            case \"SHA-512\":\n                return 512;\n            default:\n                throw new Error(`Unknown algorithm name '${algName}'`);\n        }\n    }\n    checkGenerateKeyParams(algorithm) {\n        this.checkRequiredProperty(algorithm, \"hash\");\n        this.checkHashAlgorithm(algorithm.hash, this.hashAlgorithms);\n        if (\"length\" in algorithm) {\n            if (typeof algorithm.length !== \"number\") {\n                throw new TypeError(\"length: Is not a Number\");\n            }\n            if (algorithm.length < 1) {\n                throw new RangeError(\"length: Number is out of range\");\n            }\n        }\n    }\n    checkImportParams(algorithm) {\n        this.checkRequiredProperty(algorithm, \"hash\");\n        this.checkHashAlgorithm(algorithm.hash, this.hashAlgorithms);\n    }\n}\n\nclass Pbkdf2Provider extends ProviderCrypto {\n    constructor() {\n        super(...arguments);\n        this.name = \"PBKDF2\";\n        this.hashAlgorithms = [\"SHA-1\", \"SHA-256\", \"SHA-384\", \"SHA-512\"];\n        this.usages = [\"deriveBits\", \"deriveKey\"];\n    }\n    checkAlgorithmParams(algorithm) {\n        this.checkRequiredProperty(algorithm, \"hash\");\n        this.checkHashAlgorithm(algorithm.hash, this.hashAlgorithms);\n        this.checkRequiredProperty(algorithm, \"salt\");\n        if (!(algorithm.salt instanceof ArrayBuffer || ArrayBuffer.isView(algorithm.salt))) {\n            throw new TypeError(\"salt: Is not of type '(ArrayBuffer or ArrayBufferView)'\");\n        }\n        this.checkRequiredProperty(algorithm, \"iterations\");\n        if (typeof algorithm.iterations !== \"number\") {\n            throw new TypeError(\"iterations: Is not a Number\");\n        }\n        if (algorithm.iterations < 1) {\n            throw new TypeError(\"iterations: Is less than 1\");\n        }\n    }\n    checkImportKey(format, keyData, algorithm, extractable, keyUsages, ...args) {\n        super.checkImportKey(format, keyData, algorithm, extractable, keyUsages, ...args);\n        if (extractable) {\n            throw new SyntaxError(\"extractable: Must be 'false'\");\n        }\n    }\n}\n\nclass HkdfProvider extends ProviderCrypto {\n    constructor() {\n        super(...arguments);\n        this.name = \"HKDF\";\n        this.hashAlgorithms = [\"SHA-1\", \"SHA-256\", \"SHA-384\", \"SHA-512\"];\n        this.usages = [\"deriveKey\", \"deriveBits\"];\n    }\n    checkAlgorithmParams(algorithm) {\n        this.checkRequiredProperty(algorithm, \"hash\");\n        this.checkHashAlgorithm(algorithm.hash, this.hashAlgorithms);\n        this.checkRequiredProperty(algorithm, \"salt\");\n        if (!pvtsutils__WEBPACK_IMPORTED_MODULE_0__.BufferSourceConverter.isBufferSource(algorithm.salt)) {\n            throw new TypeError(\"salt: Is not of type '(ArrayBuffer or ArrayBufferView)'\");\n        }\n        this.checkRequiredProperty(algorithm, \"info\");\n        if (!pvtsutils__WEBPACK_IMPORTED_MODULE_0__.BufferSourceConverter.isBufferSource(algorithm.info)) {\n            throw new TypeError(\"salt: Is not of type '(ArrayBuffer or ArrayBufferView)'\");\n        }\n    }\n    checkImportKey(format, keyData, algorithm, extractable, keyUsages, ...args) {\n        super.checkImportKey(format, keyData, algorithm, extractable, keyUsages, ...args);\n        if (extractable) {\n            throw new SyntaxError(\"extractable: Must be 'false'\");\n        }\n    }\n}\n\nclass ShakeProvider extends ProviderCrypto {\n    constructor() {\n        super(...arguments);\n        this.usages = [];\n        this.defaultLength = 0;\n    }\n    digest(...args) {\n        args[0] = { length: this.defaultLength, ...args[0] };\n        return super.digest.apply(this, args);\n    }\n    checkDigest(algorithm, data) {\n        super.checkDigest(algorithm, data);\n        const length = algorithm.length || 0;\n        if (typeof length !== \"number\") {\n            throw new TypeError(\"length: Is not a Number\");\n        }\n        if (length < 0) {\n            throw new TypeError(\"length: Is negative\");\n        }\n    }\n}\n\nclass Shake128Provider extends ShakeProvider {\n    constructor() {\n        super(...arguments);\n        this.name = \"shake128\";\n        this.defaultLength = 16;\n    }\n}\n\nclass Shake256Provider extends ShakeProvider {\n    constructor() {\n        super(...arguments);\n        this.name = \"shake256\";\n        this.defaultLength = 32;\n    }\n}\n\nclass Crypto {\n    get [Symbol.toStringTag]() {\n        return \"Crypto\";\n    }\n    randomUUID() {\n        const b = this.getRandomValues(new Uint8Array(16));\n        b[6] = (b[6] & 0x0f) | 0x40;\n        b[8] = (b[8] & 0x3f) | 0x80;\n        const uuid = pvtsutils__WEBPACK_IMPORTED_MODULE_0__.Convert.ToHex(b).toLowerCase();\n        return `${uuid.substring(0, 8)}-${uuid.substring(8, 12)}-${uuid.substring(12, 16)}-${uuid.substring(16, 20)}-${uuid.substring(20)}`;\n    }\n}\n\nclass ProviderStorage {\n    constructor() {\n        this.items = {};\n    }\n    get(algorithmName) {\n        return this.items[algorithmName.toLowerCase()] || null;\n    }\n    set(provider) {\n        this.items[provider.name.toLowerCase()] = provider;\n    }\n    removeAt(algorithmName) {\n        const provider = this.get(algorithmName.toLowerCase());\n        if (provider) {\n            delete this.items[algorithmName];\n        }\n        return provider;\n    }\n    has(name) {\n        return !!this.get(name);\n    }\n    get length() {\n        return Object.keys(this.items).length;\n    }\n    get algorithms() {\n        const algorithms = [];\n        for (const key in this.items) {\n            const provider = this.items[key];\n            algorithms.push(provider.name);\n        }\n        return algorithms.sort();\n    }\n}\n\nconst keyFormatMap = {\n    \"jwk\": [\"private\", \"public\", \"secret\"],\n    \"pkcs8\": [\"private\"],\n    \"spki\": [\"public\"],\n    \"raw\": [\"secret\", \"public\"]\n};\nconst sourceBufferKeyFormats = [\"pkcs8\", \"spki\", \"raw\"];\nclass SubtleCrypto {\n    constructor() {\n        this.providers = new ProviderStorage();\n    }\n    static isHashedAlgorithm(data) {\n        return data\n            && typeof data === \"object\"\n            && \"name\" in data\n            && \"hash\" in data\n            ? true\n            : false;\n    }\n    get [Symbol.toStringTag]() {\n        return \"SubtleCrypto\";\n    }\n    async digest(...args) {\n        this.checkRequiredArguments(args, 2, \"digest\");\n        const [algorithm, data, ...params] = args;\n        const preparedAlgorithm = this.prepareAlgorithm(algorithm);\n        const preparedData = pvtsutils__WEBPACK_IMPORTED_MODULE_0__.BufferSourceConverter.toArrayBuffer(data);\n        const provider = this.getProvider(preparedAlgorithm.name);\n        const result = await provider.digest(preparedAlgorithm, preparedData, ...params);\n        return result;\n    }\n    async generateKey(...args) {\n        this.checkRequiredArguments(args, 3, \"generateKey\");\n        const [algorithm, extractable, keyUsages, ...params] = args;\n        const preparedAlgorithm = this.prepareAlgorithm(algorithm);\n        const provider = this.getProvider(preparedAlgorithm.name);\n        const result = await provider.generateKey({ ...preparedAlgorithm, name: provider.name }, extractable, keyUsages, ...params);\n        return result;\n    }\n    async sign(...args) {\n        this.checkRequiredArguments(args, 3, \"sign\");\n        const [algorithm, key, data, ...params] = args;\n        this.checkCryptoKey(key);\n        const preparedAlgorithm = this.prepareAlgorithm(algorithm);\n        const preparedData = pvtsutils__WEBPACK_IMPORTED_MODULE_0__.BufferSourceConverter.toArrayBuffer(data);\n        const provider = this.getProvider(preparedAlgorithm.name);\n        const result = await provider.sign({ ...preparedAlgorithm, name: provider.name }, key, preparedData, ...params);\n        return result;\n    }\n    async verify(...args) {\n        this.checkRequiredArguments(args, 4, \"verify\");\n        const [algorithm, key, signature, data, ...params] = args;\n        this.checkCryptoKey(key);\n        const preparedAlgorithm = this.prepareAlgorithm(algorithm);\n        const preparedData = pvtsutils__WEBPACK_IMPORTED_MODULE_0__.BufferSourceConverter.toArrayBuffer(data);\n        const preparedSignature = pvtsutils__WEBPACK_IMPORTED_MODULE_0__.BufferSourceConverter.toArrayBuffer(signature);\n        const provider = this.getProvider(preparedAlgorithm.name);\n        const result = await provider.verify({ ...preparedAlgorithm, name: provider.name }, key, preparedSignature, preparedData, ...params);\n        return result;\n    }\n    async encrypt(...args) {\n        this.checkRequiredArguments(args, 3, \"encrypt\");\n        const [algorithm, key, data, ...params] = args;\n        this.checkCryptoKey(key);\n        const preparedAlgorithm = this.prepareAlgorithm(algorithm);\n        const preparedData = pvtsutils__WEBPACK_IMPORTED_MODULE_0__.BufferSourceConverter.toArrayBuffer(data);\n        const provider = this.getProvider(preparedAlgorithm.name);\n        const result = await provider.encrypt({ ...preparedAlgorithm, name: provider.name }, key, preparedData, { keyUsage: true }, ...params);\n        return result;\n    }\n    async decrypt(...args) {\n        this.checkRequiredArguments(args, 3, \"decrypt\");\n        const [algorithm, key, data, ...params] = args;\n        this.checkCryptoKey(key);\n        const preparedAlgorithm = this.prepareAlgorithm(algorithm);\n        const preparedData = pvtsutils__WEBPACK_IMPORTED_MODULE_0__.BufferSourceConverter.toArrayBuffer(data);\n        const provider = this.getProvider(preparedAlgorithm.name);\n        const result = await provider.decrypt({ ...preparedAlgorithm, name: provider.name }, key, preparedData, { keyUsage: true }, ...params);\n        return result;\n    }\n    async deriveBits(...args) {\n        this.checkRequiredArguments(args, 3, \"deriveBits\");\n        const [algorithm, baseKey, length, ...params] = args;\n        this.checkCryptoKey(baseKey);\n        const preparedAlgorithm = this.prepareAlgorithm(algorithm);\n        const provider = this.getProvider(preparedAlgorithm.name);\n        const result = await provider.deriveBits({ ...preparedAlgorithm, name: provider.name }, baseKey, length, { keyUsage: true }, ...params);\n        return result;\n    }\n    async deriveKey(...args) {\n        this.checkRequiredArguments(args, 5, \"deriveKey\");\n        const [algorithm, baseKey, derivedKeyType, extractable, keyUsages, ...params] = args;\n        const preparedDerivedKeyType = this.prepareAlgorithm(derivedKeyType);\n        const importProvider = this.getProvider(preparedDerivedKeyType.name);\n        importProvider.checkDerivedKeyParams(preparedDerivedKeyType);\n        const preparedAlgorithm = this.prepareAlgorithm(algorithm);\n        const provider = this.getProvider(preparedAlgorithm.name);\n        provider.checkCryptoKey(baseKey, \"deriveKey\");\n        const derivedBits = await provider.deriveBits({ ...preparedAlgorithm, name: provider.name }, baseKey, derivedKeyType.length || 512, { keyUsage: false }, ...params);\n        return this.importKey(\"raw\", derivedBits, derivedKeyType, extractable, keyUsages, ...params);\n    }\n    async exportKey(...args) {\n        this.checkRequiredArguments(args, 2, \"exportKey\");\n        const [format, key, ...params] = args;\n        this.checkCryptoKey(key);\n        if (!keyFormatMap[format]) {\n            throw new TypeError(\"Invalid keyFormat argument\");\n        }\n        if (!keyFormatMap[format].includes(key.type)) {\n            throw new DOMException(\"The key is not of the expected type\");\n        }\n        const provider = this.getProvider(key.algorithm.name);\n        const result = await provider.exportKey(format, key, ...params);\n        return result;\n    }\n    async importKey(...args) {\n        this.checkRequiredArguments(args, 5, \"importKey\");\n        const [format, keyData, algorithm, extractable, keyUsages, ...params] = args;\n        const preparedAlgorithm = this.prepareAlgorithm(algorithm);\n        const provider = this.getProvider(preparedAlgorithm.name);\n        if (format === \"jwk\") {\n            if (typeof keyData !== \"object\" || !keyData.kty) {\n                throw new TypeError(\"Key data must be an object for JWK import\");\n            }\n        }\n        else if (sourceBufferKeyFormats.includes(format)) {\n            if (!pvtsutils__WEBPACK_IMPORTED_MODULE_0__.BufferSourceConverter.isBufferSource(keyData)) {\n                throw new TypeError(\"Key data must be a BufferSource for non-JWK formats\");\n            }\n        }\n        else {\n            throw new TypeError(\"The provided value is not of type '(ArrayBuffer or ArrayBufferView or JsonWebKey)'\");\n        }\n        return provider.importKey(format, keyData, { ...preparedAlgorithm, name: provider.name }, extractable, keyUsages, ...params);\n    }\n    async wrapKey(format, key, wrappingKey, wrapAlgorithm, ...args) {\n        let keyData = await this.exportKey(format, key, ...args);\n        if (format === \"jwk\") {\n            const json = JSON.stringify(keyData);\n            keyData = pvtsutils__WEBPACK_IMPORTED_MODULE_0__.Convert.FromUtf8String(json);\n        }\n        const preparedAlgorithm = this.prepareAlgorithm(wrapAlgorithm);\n        const preparedData = pvtsutils__WEBPACK_IMPORTED_MODULE_0__.BufferSourceConverter.toArrayBuffer(keyData);\n        const provider = this.getProvider(preparedAlgorithm.name);\n        return provider.encrypt({ ...preparedAlgorithm, name: provider.name }, wrappingKey, preparedData, { keyUsage: false }, ...args);\n    }\n    async unwrapKey(format, wrappedKey, unwrappingKey, unwrapAlgorithm, unwrappedKeyAlgorithm, extractable, keyUsages, ...args) {\n        const preparedAlgorithm = this.prepareAlgorithm(unwrapAlgorithm);\n        const preparedData = pvtsutils__WEBPACK_IMPORTED_MODULE_0__.BufferSourceConverter.toArrayBuffer(wrappedKey);\n        const provider = this.getProvider(preparedAlgorithm.name);\n        let keyData = await provider.decrypt({ ...preparedAlgorithm, name: provider.name }, unwrappingKey, preparedData, { keyUsage: false }, ...args);\n        if (format === \"jwk\") {\n            try {\n                keyData = JSON.parse(pvtsutils__WEBPACK_IMPORTED_MODULE_0__.Convert.ToUtf8String(keyData));\n            }\n            catch (e) {\n                const error = new TypeError(\"wrappedKey: Is not a JSON\");\n                error.internal = e;\n                throw error;\n            }\n        }\n        return this.importKey(format, keyData, unwrappedKeyAlgorithm, extractable, keyUsages, ...args);\n    }\n    checkRequiredArguments(args, size, methodName) {\n        if (args.length < size) {\n            throw new TypeError(`Failed to execute '${methodName}' on 'SubtleCrypto': ${size} arguments required, but only ${args.length} present`);\n        }\n    }\n    prepareAlgorithm(algorithm) {\n        if (typeof algorithm === \"string\") {\n            return {\n                name: algorithm,\n            };\n        }\n        if (SubtleCrypto.isHashedAlgorithm(algorithm)) {\n            const preparedAlgorithm = { ...algorithm };\n            preparedAlgorithm.hash = this.prepareAlgorithm(algorithm.hash);\n            return preparedAlgorithm;\n        }\n        return { ...algorithm };\n    }\n    getProvider(name) {\n        const provider = this.providers.get(name);\n        if (!provider) {\n            throw new AlgorithmError(\"Unrecognized name\");\n        }\n        return provider;\n    }\n    checkCryptoKey(key) {\n        if (!(key instanceof CryptoKey)) {\n            throw new TypeError(`Key is not of type 'CryptoKey'`);\n        }\n    }\n}\n\nvar index = /*#__PURE__*/Object.freeze({\n  __proto__: null,\n  converters: index$3\n});\n\nconst REQUIRED_FIELDS = [\"crv\", \"e\", \"k\", \"kty\", \"n\", \"x\", \"y\"];\nclass JwkUtils {\n    static async thumbprint(hash, jwk, crypto) {\n        const data = this.format(jwk, true);\n        return crypto.subtle.digest(hash, pvtsutils__WEBPACK_IMPORTED_MODULE_0__.Convert.FromBinary(JSON.stringify(data)));\n    }\n    static format(jwk, remove = false) {\n        let res = Object.entries(jwk);\n        if (remove) {\n            res = res.filter(o => REQUIRED_FIELDS.includes(o[0]));\n        }\n        res = res.sort(([keyA], [keyB]) => keyA > keyB ? 1 : keyA < keyB ? -1 : 0);\n        return Object.fromEntries(res);\n    }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/webcrypto-core/build/webcrypto-core.es.js\n");

/***/ })

};
;