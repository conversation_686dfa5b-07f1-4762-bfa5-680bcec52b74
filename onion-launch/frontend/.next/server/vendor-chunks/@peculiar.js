"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@peculiar";
exports.ids = ["vendor-chunks/@peculiar"];
exports.modules = {

/***/ "(ssr)/./node_modules/@peculiar/asn1-schema/build/es2015/convert.js":
/*!********************************************************************!*\
  !*** ./node_modules/@peculiar/asn1-schema/build/es2015/convert.js ***!
  \********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AsnConvert: () => (/* binding */ AsnConvert)\n/* harmony export */ });\n/* harmony import */ var asn1js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! asn1js */ \"(ssr)/./node_modules/asn1js/build/index.es.js\");\n/* harmony import */ var pvtsutils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! pvtsutils */ \"(ssr)/./node_modules/pvtsutils/build/index.es.js\");\n/* harmony import */ var _parser__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./parser */ \"(ssr)/./node_modules/@peculiar/asn1-schema/build/es2015/parser.js\");\n/* harmony import */ var _serializer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./serializer */ \"(ssr)/./node_modules/@peculiar/asn1-schema/build/es2015/serializer.js\");\n\n\n\n\nclass AsnConvert {\n    static serialize(obj) {\n        return _serializer__WEBPACK_IMPORTED_MODULE_3__.AsnSerializer.serialize(obj);\n    }\n    static parse(data, target) {\n        return _parser__WEBPACK_IMPORTED_MODULE_2__.AsnParser.parse(data, target);\n    }\n    static toString(data) {\n        const buf = pvtsutils__WEBPACK_IMPORTED_MODULE_1__.BufferSourceConverter.isBufferSource(data)\n            ? pvtsutils__WEBPACK_IMPORTED_MODULE_1__.BufferSourceConverter.toArrayBuffer(data)\n            : AsnConvert.serialize(data);\n        const asn = asn1js__WEBPACK_IMPORTED_MODULE_0__.fromBER(buf);\n        if (asn.offset === -1) {\n            throw new Error(`Cannot decode ASN.1 data. ${asn.result.error}`);\n        }\n        return asn.result.toString();\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHBlY3VsaWFyL2FzbjEtc2NoZW1hL2J1aWxkL2VzMjAxNS9jb252ZXJ0LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQWlDO0FBQ2lCO0FBQ2I7QUFDUTtBQUN0QztBQUNQO0FBQ0EsZUFBZSxzREFBYTtBQUM1QjtBQUNBO0FBQ0EsZUFBZSw4Q0FBUztBQUN4QjtBQUNBO0FBQ0Esb0JBQW9CLDREQUFxQjtBQUN6QyxjQUFjLDREQUFxQjtBQUNuQztBQUNBLG9CQUFvQiwyQ0FBYztBQUNsQztBQUNBLHlEQUF5RCxpQkFBaUI7QUFDMUU7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIi9Vc2Vycy94eW0vY29kZS90Ym9vay9vbnRvbl9jYy9vbmlvbi1sYXVuY2gvZnJvbnRlbmQvbm9kZV9tb2R1bGVzL0BwZWN1bGlhci9hc24xLXNjaGVtYS9idWlsZC9lczIwMTUvY29udmVydC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgKiBhcyBhc24xanMgZnJvbSBcImFzbjFqc1wiO1xuaW1wb3J0IHsgQnVmZmVyU291cmNlQ29udmVydGVyIH0gZnJvbSBcInB2dHN1dGlsc1wiO1xuaW1wb3J0IHsgQXNuUGFyc2VyIH0gZnJvbSBcIi4vcGFyc2VyXCI7XG5pbXBvcnQgeyBBc25TZXJpYWxpemVyIH0gZnJvbSBcIi4vc2VyaWFsaXplclwiO1xuZXhwb3J0IGNsYXNzIEFzbkNvbnZlcnQge1xuICAgIHN0YXRpYyBzZXJpYWxpemUob2JqKSB7XG4gICAgICAgIHJldHVybiBBc25TZXJpYWxpemVyLnNlcmlhbGl6ZShvYmopO1xuICAgIH1cbiAgICBzdGF0aWMgcGFyc2UoZGF0YSwgdGFyZ2V0KSB7XG4gICAgICAgIHJldHVybiBBc25QYXJzZXIucGFyc2UoZGF0YSwgdGFyZ2V0KTtcbiAgICB9XG4gICAgc3RhdGljIHRvU3RyaW5nKGRhdGEpIHtcbiAgICAgICAgY29uc3QgYnVmID0gQnVmZmVyU291cmNlQ29udmVydGVyLmlzQnVmZmVyU291cmNlKGRhdGEpXG4gICAgICAgICAgICA/IEJ1ZmZlclNvdXJjZUNvbnZlcnRlci50b0FycmF5QnVmZmVyKGRhdGEpXG4gICAgICAgICAgICA6IEFzbkNvbnZlcnQuc2VyaWFsaXplKGRhdGEpO1xuICAgICAgICBjb25zdCBhc24gPSBhc24xanMuZnJvbUJFUihidWYpO1xuICAgICAgICBpZiAoYXNuLm9mZnNldCA9PT0gLTEpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBFcnJvcihgQ2Fubm90IGRlY29kZSBBU04uMSBkYXRhLiAke2Fzbi5yZXN1bHQuZXJyb3J9YCk7XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIGFzbi5yZXN1bHQudG9TdHJpbmcoKTtcbiAgICB9XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@peculiar/asn1-schema/build/es2015/convert.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@peculiar/asn1-schema/build/es2015/converters.js":
/*!***********************************************************************!*\
  !*** ./node_modules/@peculiar/asn1-schema/build/es2015/converters.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AsnAnyConverter: () => (/* binding */ AsnAnyConverter),\n/* harmony export */   AsnBitStringConverter: () => (/* binding */ AsnBitStringConverter),\n/* harmony export */   AsnBmpStringConverter: () => (/* binding */ AsnBmpStringConverter),\n/* harmony export */   AsnBooleanConverter: () => (/* binding */ AsnBooleanConverter),\n/* harmony export */   AsnCharacterStringConverter: () => (/* binding */ AsnCharacterStringConverter),\n/* harmony export */   AsnConstructedOctetStringConverter: () => (/* binding */ AsnConstructedOctetStringConverter),\n/* harmony export */   AsnEnumeratedConverter: () => (/* binding */ AsnEnumeratedConverter),\n/* harmony export */   AsnGeneralStringConverter: () => (/* binding */ AsnGeneralStringConverter),\n/* harmony export */   AsnGeneralizedTimeConverter: () => (/* binding */ AsnGeneralizedTimeConverter),\n/* harmony export */   AsnGraphicStringConverter: () => (/* binding */ AsnGraphicStringConverter),\n/* harmony export */   AsnIA5StringConverter: () => (/* binding */ AsnIA5StringConverter),\n/* harmony export */   AsnIntegerArrayBufferConverter: () => (/* binding */ AsnIntegerArrayBufferConverter),\n/* harmony export */   AsnIntegerBigIntConverter: () => (/* binding */ AsnIntegerBigIntConverter),\n/* harmony export */   AsnIntegerConverter: () => (/* binding */ AsnIntegerConverter),\n/* harmony export */   AsnNullConverter: () => (/* binding */ AsnNullConverter),\n/* harmony export */   AsnNumericStringConverter: () => (/* binding */ AsnNumericStringConverter),\n/* harmony export */   AsnObjectIdentifierConverter: () => (/* binding */ AsnObjectIdentifierConverter),\n/* harmony export */   AsnOctetStringConverter: () => (/* binding */ AsnOctetStringConverter),\n/* harmony export */   AsnPrintableStringConverter: () => (/* binding */ AsnPrintableStringConverter),\n/* harmony export */   AsnTeletexStringConverter: () => (/* binding */ AsnTeletexStringConverter),\n/* harmony export */   AsnUTCTimeConverter: () => (/* binding */ AsnUTCTimeConverter),\n/* harmony export */   AsnUniversalStringConverter: () => (/* binding */ AsnUniversalStringConverter),\n/* harmony export */   AsnUtf8StringConverter: () => (/* binding */ AsnUtf8StringConverter),\n/* harmony export */   AsnVideotexStringConverter: () => (/* binding */ AsnVideotexStringConverter),\n/* harmony export */   AsnVisibleStringConverter: () => (/* binding */ AsnVisibleStringConverter),\n/* harmony export */   defaultConverter: () => (/* binding */ defaultConverter)\n/* harmony export */ });\n/* harmony import */ var asn1js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! asn1js */ \"(ssr)/./node_modules/asn1js/build/index.es.js\");\n/* harmony import */ var _enums__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./enums */ \"(ssr)/./node_modules/@peculiar/asn1-schema/build/es2015/enums.js\");\n/* harmony import */ var _types_index__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./types/index */ \"(ssr)/./node_modules/@peculiar/asn1-schema/build/es2015/types/index.js\");\n\n\n\nconst AsnAnyConverter = {\n    fromASN: (value) => value instanceof asn1js__WEBPACK_IMPORTED_MODULE_0__.Null ? null : value.valueBeforeDecodeView,\n    toASN: (value) => {\n        if (value === null) {\n            return new asn1js__WEBPACK_IMPORTED_MODULE_0__.Null();\n        }\n        const schema = asn1js__WEBPACK_IMPORTED_MODULE_0__.fromBER(value);\n        if (schema.result.error) {\n            throw new Error(schema.result.error);\n        }\n        return schema.result;\n    },\n};\nconst AsnIntegerConverter = {\n    fromASN: (value) => value.valueBlock.valueHexView.byteLength >= 4\n        ? value.valueBlock.toString()\n        : value.valueBlock.valueDec,\n    toASN: (value) => new asn1js__WEBPACK_IMPORTED_MODULE_0__.Integer({ value: +value }),\n};\nconst AsnEnumeratedConverter = {\n    fromASN: (value) => value.valueBlock.valueDec,\n    toASN: (value) => new asn1js__WEBPACK_IMPORTED_MODULE_0__.Enumerated({ value }),\n};\nconst AsnIntegerArrayBufferConverter = {\n    fromASN: (value) => value.valueBlock.valueHexView,\n    toASN: (value) => new asn1js__WEBPACK_IMPORTED_MODULE_0__.Integer({ valueHex: value }),\n};\nconst AsnIntegerBigIntConverter = {\n    fromASN: (value) => value.toBigInt(),\n    toASN: (value) => asn1js__WEBPACK_IMPORTED_MODULE_0__.Integer.fromBigInt(value),\n};\nconst AsnBitStringConverter = {\n    fromASN: (value) => value.valueBlock.valueHexView,\n    toASN: (value) => new asn1js__WEBPACK_IMPORTED_MODULE_0__.BitString({ valueHex: value }),\n};\nconst AsnObjectIdentifierConverter = {\n    fromASN: (value) => value.valueBlock.toString(),\n    toASN: (value) => new asn1js__WEBPACK_IMPORTED_MODULE_0__.ObjectIdentifier({ value }),\n};\nconst AsnBooleanConverter = {\n    fromASN: (value) => value.valueBlock.value,\n    toASN: (value) => new asn1js__WEBPACK_IMPORTED_MODULE_0__.Boolean({ value }),\n};\nconst AsnOctetStringConverter = {\n    fromASN: (value) => value.valueBlock.valueHexView,\n    toASN: (value) => new asn1js__WEBPACK_IMPORTED_MODULE_0__.OctetString({ valueHex: value }),\n};\nconst AsnConstructedOctetStringConverter = {\n    fromASN: (value) => new _types_index__WEBPACK_IMPORTED_MODULE_2__.OctetString(value.getValue()),\n    toASN: (value) => value.toASN(),\n};\nfunction createStringConverter(Asn1Type) {\n    return {\n        fromASN: (value) => value.valueBlock.value,\n        toASN: (value) => new Asn1Type({ value }),\n    };\n}\nconst AsnUtf8StringConverter = createStringConverter(asn1js__WEBPACK_IMPORTED_MODULE_0__.Utf8String);\nconst AsnBmpStringConverter = createStringConverter(asn1js__WEBPACK_IMPORTED_MODULE_0__.BmpString);\nconst AsnUniversalStringConverter = createStringConverter(asn1js__WEBPACK_IMPORTED_MODULE_0__.UniversalString);\nconst AsnNumericStringConverter = createStringConverter(asn1js__WEBPACK_IMPORTED_MODULE_0__.NumericString);\nconst AsnPrintableStringConverter = createStringConverter(asn1js__WEBPACK_IMPORTED_MODULE_0__.PrintableString);\nconst AsnTeletexStringConverter = createStringConverter(asn1js__WEBPACK_IMPORTED_MODULE_0__.TeletexString);\nconst AsnVideotexStringConverter = createStringConverter(asn1js__WEBPACK_IMPORTED_MODULE_0__.VideotexString);\nconst AsnIA5StringConverter = createStringConverter(asn1js__WEBPACK_IMPORTED_MODULE_0__.IA5String);\nconst AsnGraphicStringConverter = createStringConverter(asn1js__WEBPACK_IMPORTED_MODULE_0__.GraphicString);\nconst AsnVisibleStringConverter = createStringConverter(asn1js__WEBPACK_IMPORTED_MODULE_0__.VisibleString);\nconst AsnGeneralStringConverter = createStringConverter(asn1js__WEBPACK_IMPORTED_MODULE_0__.GeneralString);\nconst AsnCharacterStringConverter = createStringConverter(asn1js__WEBPACK_IMPORTED_MODULE_0__.CharacterString);\nconst AsnUTCTimeConverter = {\n    fromASN: (value) => value.toDate(),\n    toASN: (value) => new asn1js__WEBPACK_IMPORTED_MODULE_0__.UTCTime({ valueDate: value }),\n};\nconst AsnGeneralizedTimeConverter = {\n    fromASN: (value) => value.toDate(),\n    toASN: (value) => new asn1js__WEBPACK_IMPORTED_MODULE_0__.GeneralizedTime({ valueDate: value }),\n};\nconst AsnNullConverter = {\n    fromASN: () => null,\n    toASN: () => {\n        return new asn1js__WEBPACK_IMPORTED_MODULE_0__.Null();\n    },\n};\nfunction defaultConverter(type) {\n    switch (type) {\n        case _enums__WEBPACK_IMPORTED_MODULE_1__.AsnPropTypes.Any:\n            return AsnAnyConverter;\n        case _enums__WEBPACK_IMPORTED_MODULE_1__.AsnPropTypes.BitString:\n            return AsnBitStringConverter;\n        case _enums__WEBPACK_IMPORTED_MODULE_1__.AsnPropTypes.BmpString:\n            return AsnBmpStringConverter;\n        case _enums__WEBPACK_IMPORTED_MODULE_1__.AsnPropTypes.Boolean:\n            return AsnBooleanConverter;\n        case _enums__WEBPACK_IMPORTED_MODULE_1__.AsnPropTypes.CharacterString:\n            return AsnCharacterStringConverter;\n        case _enums__WEBPACK_IMPORTED_MODULE_1__.AsnPropTypes.Enumerated:\n            return AsnEnumeratedConverter;\n        case _enums__WEBPACK_IMPORTED_MODULE_1__.AsnPropTypes.GeneralString:\n            return AsnGeneralStringConverter;\n        case _enums__WEBPACK_IMPORTED_MODULE_1__.AsnPropTypes.GeneralizedTime:\n            return AsnGeneralizedTimeConverter;\n        case _enums__WEBPACK_IMPORTED_MODULE_1__.AsnPropTypes.GraphicString:\n            return AsnGraphicStringConverter;\n        case _enums__WEBPACK_IMPORTED_MODULE_1__.AsnPropTypes.IA5String:\n            return AsnIA5StringConverter;\n        case _enums__WEBPACK_IMPORTED_MODULE_1__.AsnPropTypes.Integer:\n            return AsnIntegerConverter;\n        case _enums__WEBPACK_IMPORTED_MODULE_1__.AsnPropTypes.Null:\n            return AsnNullConverter;\n        case _enums__WEBPACK_IMPORTED_MODULE_1__.AsnPropTypes.NumericString:\n            return AsnNumericStringConverter;\n        case _enums__WEBPACK_IMPORTED_MODULE_1__.AsnPropTypes.ObjectIdentifier:\n            return AsnObjectIdentifierConverter;\n        case _enums__WEBPACK_IMPORTED_MODULE_1__.AsnPropTypes.OctetString:\n            return AsnOctetStringConverter;\n        case _enums__WEBPACK_IMPORTED_MODULE_1__.AsnPropTypes.PrintableString:\n            return AsnPrintableStringConverter;\n        case _enums__WEBPACK_IMPORTED_MODULE_1__.AsnPropTypes.TeletexString:\n            return AsnTeletexStringConverter;\n        case _enums__WEBPACK_IMPORTED_MODULE_1__.AsnPropTypes.UTCTime:\n            return AsnUTCTimeConverter;\n        case _enums__WEBPACK_IMPORTED_MODULE_1__.AsnPropTypes.UniversalString:\n            return AsnUniversalStringConverter;\n        case _enums__WEBPACK_IMPORTED_MODULE_1__.AsnPropTypes.Utf8String:\n            return AsnUtf8StringConverter;\n        case _enums__WEBPACK_IMPORTED_MODULE_1__.AsnPropTypes.VideotexString:\n            return AsnVideotexStringConverter;\n        case _enums__WEBPACK_IMPORTED_MODULE_1__.AsnPropTypes.VisibleString:\n            return AsnVisibleStringConverter;\n        default:\n            return null;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@peculiar/asn1-schema/build/es2015/converters.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@peculiar/asn1-schema/build/es2015/decorators.js":
/*!***********************************************************************!*\
  !*** ./node_modules/@peculiar/asn1-schema/build/es2015/decorators.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AsnChoiceType: () => (/* binding */ AsnChoiceType),\n/* harmony export */   AsnProp: () => (/* binding */ AsnProp),\n/* harmony export */   AsnSequenceType: () => (/* binding */ AsnSequenceType),\n/* harmony export */   AsnSetType: () => (/* binding */ AsnSetType),\n/* harmony export */   AsnType: () => (/* binding */ AsnType)\n/* harmony export */ });\n/* harmony import */ var _converters__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./converters */ \"(ssr)/./node_modules/@peculiar/asn1-schema/build/es2015/converters.js\");\n/* harmony import */ var _enums__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./enums */ \"(ssr)/./node_modules/@peculiar/asn1-schema/build/es2015/enums.js\");\n/* harmony import */ var _storage__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./storage */ \"(ssr)/./node_modules/@peculiar/asn1-schema/build/es2015/storage.js\");\n\n\n\nconst AsnType = (options) => (target) => {\n    let schema;\n    if (!_storage__WEBPACK_IMPORTED_MODULE_2__.schemaStorage.has(target)) {\n        schema = _storage__WEBPACK_IMPORTED_MODULE_2__.schemaStorage.createDefault(target);\n        _storage__WEBPACK_IMPORTED_MODULE_2__.schemaStorage.set(target, schema);\n    }\n    else {\n        schema = _storage__WEBPACK_IMPORTED_MODULE_2__.schemaStorage.get(target);\n    }\n    Object.assign(schema, options);\n};\nconst AsnChoiceType = () => AsnType({ type: _enums__WEBPACK_IMPORTED_MODULE_1__.AsnTypeTypes.Choice });\nconst AsnSetType = (options) => AsnType({ type: _enums__WEBPACK_IMPORTED_MODULE_1__.AsnTypeTypes.Set, ...options });\nconst AsnSequenceType = (options) => AsnType({ type: _enums__WEBPACK_IMPORTED_MODULE_1__.AsnTypeTypes.Sequence, ...options });\nconst AsnProp = (options) => (target, propertyKey) => {\n    let schema;\n    if (!_storage__WEBPACK_IMPORTED_MODULE_2__.schemaStorage.has(target.constructor)) {\n        schema = _storage__WEBPACK_IMPORTED_MODULE_2__.schemaStorage.createDefault(target.constructor);\n        _storage__WEBPACK_IMPORTED_MODULE_2__.schemaStorage.set(target.constructor, schema);\n    }\n    else {\n        schema = _storage__WEBPACK_IMPORTED_MODULE_2__.schemaStorage.get(target.constructor);\n    }\n    const copyOptions = Object.assign({}, options);\n    if (typeof copyOptions.type === \"number\" && !copyOptions.converter) {\n        const defaultConverter = _converters__WEBPACK_IMPORTED_MODULE_0__.defaultConverter(options.type);\n        if (!defaultConverter) {\n            throw new Error(`Cannot get default converter for property '${propertyKey}' of ${target.constructor.name}`);\n        }\n        copyOptions.converter = defaultConverter;\n    }\n    schema.items[propertyKey] = copyOptions;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@peculiar/asn1-schema/build/es2015/decorators.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@peculiar/asn1-schema/build/es2015/enums.js":
/*!******************************************************************!*\
  !*** ./node_modules/@peculiar/asn1-schema/build/es2015/enums.js ***!
  \******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AsnPropTypes: () => (/* binding */ AsnPropTypes),\n/* harmony export */   AsnTypeTypes: () => (/* binding */ AsnTypeTypes)\n/* harmony export */ });\nvar AsnTypeTypes;\n(function (AsnTypeTypes) {\n    AsnTypeTypes[AsnTypeTypes[\"Sequence\"] = 0] = \"Sequence\";\n    AsnTypeTypes[AsnTypeTypes[\"Set\"] = 1] = \"Set\";\n    AsnTypeTypes[AsnTypeTypes[\"Choice\"] = 2] = \"Choice\";\n})(AsnTypeTypes || (AsnTypeTypes = {}));\nvar AsnPropTypes;\n(function (AsnPropTypes) {\n    AsnPropTypes[AsnPropTypes[\"Any\"] = 1] = \"Any\";\n    AsnPropTypes[AsnPropTypes[\"Boolean\"] = 2] = \"Boolean\";\n    AsnPropTypes[AsnPropTypes[\"OctetString\"] = 3] = \"OctetString\";\n    AsnPropTypes[AsnPropTypes[\"BitString\"] = 4] = \"BitString\";\n    AsnPropTypes[AsnPropTypes[\"Integer\"] = 5] = \"Integer\";\n    AsnPropTypes[AsnPropTypes[\"Enumerated\"] = 6] = \"Enumerated\";\n    AsnPropTypes[AsnPropTypes[\"ObjectIdentifier\"] = 7] = \"ObjectIdentifier\";\n    AsnPropTypes[AsnPropTypes[\"Utf8String\"] = 8] = \"Utf8String\";\n    AsnPropTypes[AsnPropTypes[\"BmpString\"] = 9] = \"BmpString\";\n    AsnPropTypes[AsnPropTypes[\"UniversalString\"] = 10] = \"UniversalString\";\n    AsnPropTypes[AsnPropTypes[\"NumericString\"] = 11] = \"NumericString\";\n    AsnPropTypes[AsnPropTypes[\"PrintableString\"] = 12] = \"PrintableString\";\n    AsnPropTypes[AsnPropTypes[\"TeletexString\"] = 13] = \"TeletexString\";\n    AsnPropTypes[AsnPropTypes[\"VideotexString\"] = 14] = \"VideotexString\";\n    AsnPropTypes[AsnPropTypes[\"IA5String\"] = 15] = \"IA5String\";\n    AsnPropTypes[AsnPropTypes[\"GraphicString\"] = 16] = \"GraphicString\";\n    AsnPropTypes[AsnPropTypes[\"VisibleString\"] = 17] = \"VisibleString\";\n    AsnPropTypes[AsnPropTypes[\"GeneralString\"] = 18] = \"GeneralString\";\n    AsnPropTypes[AsnPropTypes[\"CharacterString\"] = 19] = \"CharacterString\";\n    AsnPropTypes[AsnPropTypes[\"UTCTime\"] = 20] = \"UTCTime\";\n    AsnPropTypes[AsnPropTypes[\"GeneralizedTime\"] = 21] = \"GeneralizedTime\";\n    AsnPropTypes[AsnPropTypes[\"DATE\"] = 22] = \"DATE\";\n    AsnPropTypes[AsnPropTypes[\"TimeOfDay\"] = 23] = \"TimeOfDay\";\n    AsnPropTypes[AsnPropTypes[\"DateTime\"] = 24] = \"DateTime\";\n    AsnPropTypes[AsnPropTypes[\"Duration\"] = 25] = \"Duration\";\n    AsnPropTypes[AsnPropTypes[\"TIME\"] = 26] = \"TIME\";\n    AsnPropTypes[AsnPropTypes[\"Null\"] = 27] = \"Null\";\n})(AsnPropTypes || (AsnPropTypes = {}));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@peculiar/asn1-schema/build/es2015/enums.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@peculiar/asn1-schema/build/es2015/errors/index.js":
/*!*************************************************************************!*\
  !*** ./node_modules/@peculiar/asn1-schema/build/es2015/errors/index.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AsnSchemaValidationError: () => (/* reexport safe */ _schema_validation__WEBPACK_IMPORTED_MODULE_0__.AsnSchemaValidationError)\n/* harmony export */ });\n/* harmony import */ var _schema_validation__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./schema_validation */ \"(ssr)/./node_modules/@peculiar/asn1-schema/build/es2015/errors/schema_validation.js\");\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHBlY3VsaWFyL2FzbjEtc2NoZW1hL2J1aWxkL2VzMjAxNS9lcnJvcnMvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBb0MiLCJzb3VyY2VzIjpbIi9Vc2Vycy94eW0vY29kZS90Ym9vay9vbnRvbl9jYy9vbmlvbi1sYXVuY2gvZnJvbnRlbmQvbm9kZV9tb2R1bGVzL0BwZWN1bGlhci9hc24xLXNjaGVtYS9idWlsZC9lczIwMTUvZXJyb3JzL2luZGV4LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCAqIGZyb20gXCIuL3NjaGVtYV92YWxpZGF0aW9uXCI7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@peculiar/asn1-schema/build/es2015/errors/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@peculiar/asn1-schema/build/es2015/errors/schema_validation.js":
/*!*************************************************************************************!*\
  !*** ./node_modules/@peculiar/asn1-schema/build/es2015/errors/schema_validation.js ***!
  \*************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AsnSchemaValidationError: () => (/* binding */ AsnSchemaValidationError)\n/* harmony export */ });\nclass AsnSchemaValidationError extends Error {\n    constructor() {\n        super(...arguments);\n        this.schemas = [];\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHBlY3VsaWFyL2FzbjEtc2NoZW1hL2J1aWxkL2VzMjAxNS9lcnJvcnMvc2NoZW1hX3ZhbGlkYXRpb24uanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiL1VzZXJzL3h5bS9jb2RlL3Rib29rL29udG9uX2NjL29uaW9uLWxhdW5jaC9mcm9udGVuZC9ub2RlX21vZHVsZXMvQHBlY3VsaWFyL2FzbjEtc2NoZW1hL2J1aWxkL2VzMjAxNS9lcnJvcnMvc2NoZW1hX3ZhbGlkYXRpb24uanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGNsYXNzIEFzblNjaGVtYVZhbGlkYXRpb25FcnJvciBleHRlbmRzIEVycm9yIHtcbiAgICBjb25zdHJ1Y3RvcigpIHtcbiAgICAgICAgc3VwZXIoLi4uYXJndW1lbnRzKTtcbiAgICAgICAgdGhpcy5zY2hlbWFzID0gW107XG4gICAgfVxufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@peculiar/asn1-schema/build/es2015/errors/schema_validation.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@peculiar/asn1-schema/build/es2015/helper.js":
/*!*******************************************************************!*\
  !*** ./node_modules/@peculiar/asn1-schema/build/es2015/helper.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isArrayEqual: () => (/* binding */ isArrayEqual),\n/* harmony export */   isConvertible: () => (/* binding */ isConvertible),\n/* harmony export */   isTypeOfArray: () => (/* binding */ isTypeOfArray)\n/* harmony export */ });\nfunction isConvertible(target) {\n    if (typeof target === \"function\" && target.prototype) {\n        if (target.prototype.toASN && target.prototype.fromASN) {\n            return true;\n        }\n        else {\n            return isConvertible(target.prototype);\n        }\n    }\n    else {\n        return !!(target && typeof target === \"object\" && \"toASN\" in target && \"fromASN\" in target);\n    }\n}\nfunction isTypeOfArray(target) {\n    var _a;\n    if (target) {\n        const proto = Object.getPrototypeOf(target);\n        if (((_a = proto === null || proto === void 0 ? void 0 : proto.prototype) === null || _a === void 0 ? void 0 : _a.constructor) === Array) {\n            return true;\n        }\n        return isTypeOfArray(proto);\n    }\n    return false;\n}\nfunction isArrayEqual(bytes1, bytes2) {\n    if (!(bytes1 && bytes2)) {\n        return false;\n    }\n    if (bytes1.byteLength !== bytes2.byteLength) {\n        return false;\n    }\n    const b1 = new Uint8Array(bytes1);\n    const b2 = new Uint8Array(bytes2);\n    for (let i = 0; i < bytes1.byteLength; i++) {\n        if (b1[i] !== b2[i]) {\n            return false;\n        }\n    }\n    return true;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@peculiar/asn1-schema/build/es2015/helper.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@peculiar/asn1-schema/build/es2015/index.js":
/*!******************************************************************!*\
  !*** ./node_modules/@peculiar/asn1-schema/build/es2015/index.js ***!
  \******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AsnAnyConverter: () => (/* reexport safe */ _converters__WEBPACK_IMPORTED_MODULE_0__.AsnAnyConverter),\n/* harmony export */   AsnArray: () => (/* reexport safe */ _objects__WEBPACK_IMPORTED_MODULE_7__.AsnArray),\n/* harmony export */   AsnBitStringConverter: () => (/* reexport safe */ _converters__WEBPACK_IMPORTED_MODULE_0__.AsnBitStringConverter),\n/* harmony export */   AsnBmpStringConverter: () => (/* reexport safe */ _converters__WEBPACK_IMPORTED_MODULE_0__.AsnBmpStringConverter),\n/* harmony export */   AsnBooleanConverter: () => (/* reexport safe */ _converters__WEBPACK_IMPORTED_MODULE_0__.AsnBooleanConverter),\n/* harmony export */   AsnCharacterStringConverter: () => (/* reexport safe */ _converters__WEBPACK_IMPORTED_MODULE_0__.AsnCharacterStringConverter),\n/* harmony export */   AsnChoiceType: () => (/* reexport safe */ _decorators__WEBPACK_IMPORTED_MODULE_2__.AsnChoiceType),\n/* harmony export */   AsnConstructedOctetStringConverter: () => (/* reexport safe */ _converters__WEBPACK_IMPORTED_MODULE_0__.AsnConstructedOctetStringConverter),\n/* harmony export */   AsnConvert: () => (/* reexport safe */ _convert__WEBPACK_IMPORTED_MODULE_8__.AsnConvert),\n/* harmony export */   AsnEnumeratedConverter: () => (/* reexport safe */ _converters__WEBPACK_IMPORTED_MODULE_0__.AsnEnumeratedConverter),\n/* harmony export */   AsnGeneralStringConverter: () => (/* reexport safe */ _converters__WEBPACK_IMPORTED_MODULE_0__.AsnGeneralStringConverter),\n/* harmony export */   AsnGeneralizedTimeConverter: () => (/* reexport safe */ _converters__WEBPACK_IMPORTED_MODULE_0__.AsnGeneralizedTimeConverter),\n/* harmony export */   AsnGraphicStringConverter: () => (/* reexport safe */ _converters__WEBPACK_IMPORTED_MODULE_0__.AsnGraphicStringConverter),\n/* harmony export */   AsnIA5StringConverter: () => (/* reexport safe */ _converters__WEBPACK_IMPORTED_MODULE_0__.AsnIA5StringConverter),\n/* harmony export */   AsnIntegerArrayBufferConverter: () => (/* reexport safe */ _converters__WEBPACK_IMPORTED_MODULE_0__.AsnIntegerArrayBufferConverter),\n/* harmony export */   AsnIntegerBigIntConverter: () => (/* reexport safe */ _converters__WEBPACK_IMPORTED_MODULE_0__.AsnIntegerBigIntConverter),\n/* harmony export */   AsnIntegerConverter: () => (/* reexport safe */ _converters__WEBPACK_IMPORTED_MODULE_0__.AsnIntegerConverter),\n/* harmony export */   AsnNullConverter: () => (/* reexport safe */ _converters__WEBPACK_IMPORTED_MODULE_0__.AsnNullConverter),\n/* harmony export */   AsnNumericStringConverter: () => (/* reexport safe */ _converters__WEBPACK_IMPORTED_MODULE_0__.AsnNumericStringConverter),\n/* harmony export */   AsnObjectIdentifierConverter: () => (/* reexport safe */ _converters__WEBPACK_IMPORTED_MODULE_0__.AsnObjectIdentifierConverter),\n/* harmony export */   AsnOctetStringConverter: () => (/* reexport safe */ _converters__WEBPACK_IMPORTED_MODULE_0__.AsnOctetStringConverter),\n/* harmony export */   AsnParser: () => (/* reexport safe */ _parser__WEBPACK_IMPORTED_MODULE_4__.AsnParser),\n/* harmony export */   AsnPrintableStringConverter: () => (/* reexport safe */ _converters__WEBPACK_IMPORTED_MODULE_0__.AsnPrintableStringConverter),\n/* harmony export */   AsnProp: () => (/* reexport safe */ _decorators__WEBPACK_IMPORTED_MODULE_2__.AsnProp),\n/* harmony export */   AsnPropTypes: () => (/* reexport safe */ _enums__WEBPACK_IMPORTED_MODULE_3__.AsnPropTypes),\n/* harmony export */   AsnSchemaValidationError: () => (/* reexport safe */ _errors__WEBPACK_IMPORTED_MODULE_6__.AsnSchemaValidationError),\n/* harmony export */   AsnSequenceType: () => (/* reexport safe */ _decorators__WEBPACK_IMPORTED_MODULE_2__.AsnSequenceType),\n/* harmony export */   AsnSerializer: () => (/* reexport safe */ _serializer__WEBPACK_IMPORTED_MODULE_5__.AsnSerializer),\n/* harmony export */   AsnSetType: () => (/* reexport safe */ _decorators__WEBPACK_IMPORTED_MODULE_2__.AsnSetType),\n/* harmony export */   AsnTeletexStringConverter: () => (/* reexport safe */ _converters__WEBPACK_IMPORTED_MODULE_0__.AsnTeletexStringConverter),\n/* harmony export */   AsnType: () => (/* reexport safe */ _decorators__WEBPACK_IMPORTED_MODULE_2__.AsnType),\n/* harmony export */   AsnTypeTypes: () => (/* reexport safe */ _enums__WEBPACK_IMPORTED_MODULE_3__.AsnTypeTypes),\n/* harmony export */   AsnUTCTimeConverter: () => (/* reexport safe */ _converters__WEBPACK_IMPORTED_MODULE_0__.AsnUTCTimeConverter),\n/* harmony export */   AsnUniversalStringConverter: () => (/* reexport safe */ _converters__WEBPACK_IMPORTED_MODULE_0__.AsnUniversalStringConverter),\n/* harmony export */   AsnUtf8StringConverter: () => (/* reexport safe */ _converters__WEBPACK_IMPORTED_MODULE_0__.AsnUtf8StringConverter),\n/* harmony export */   AsnVideotexStringConverter: () => (/* reexport safe */ _converters__WEBPACK_IMPORTED_MODULE_0__.AsnVideotexStringConverter),\n/* harmony export */   AsnVisibleStringConverter: () => (/* reexport safe */ _converters__WEBPACK_IMPORTED_MODULE_0__.AsnVisibleStringConverter),\n/* harmony export */   BitString: () => (/* reexport safe */ _types_index__WEBPACK_IMPORTED_MODULE_1__.BitString),\n/* harmony export */   OctetString: () => (/* reexport safe */ _types_index__WEBPACK_IMPORTED_MODULE_1__.OctetString),\n/* harmony export */   defaultConverter: () => (/* reexport safe */ _converters__WEBPACK_IMPORTED_MODULE_0__.defaultConverter)\n/* harmony export */ });\n/* harmony import */ var _converters__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./converters */ \"(ssr)/./node_modules/@peculiar/asn1-schema/build/es2015/converters.js\");\n/* harmony import */ var _types_index__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./types/index */ \"(ssr)/./node_modules/@peculiar/asn1-schema/build/es2015/types/index.js\");\n/* harmony import */ var _decorators__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./decorators */ \"(ssr)/./node_modules/@peculiar/asn1-schema/build/es2015/decorators.js\");\n/* harmony import */ var _enums__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./enums */ \"(ssr)/./node_modules/@peculiar/asn1-schema/build/es2015/enums.js\");\n/* harmony import */ var _parser__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./parser */ \"(ssr)/./node_modules/@peculiar/asn1-schema/build/es2015/parser.js\");\n/* harmony import */ var _serializer__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./serializer */ \"(ssr)/./node_modules/@peculiar/asn1-schema/build/es2015/serializer.js\");\n/* harmony import */ var _errors__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./errors */ \"(ssr)/./node_modules/@peculiar/asn1-schema/build/es2015/errors/index.js\");\n/* harmony import */ var _objects__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./objects */ \"(ssr)/./node_modules/@peculiar/asn1-schema/build/es2015/objects.js\");\n/* harmony import */ var _convert__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./convert */ \"(ssr)/./node_modules/@peculiar/asn1-schema/build/es2015/convert.js\");\n\n\n\n\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHBlY3VsaWFyL2FzbjEtc2NoZW1hL2J1aWxkL2VzMjAxNS9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQTZCO0FBQ0M7QUFDOEQ7QUFDdkM7QUFDaEI7QUFDUTtBQUNwQjtBQUNDO0FBQ0EiLCJzb3VyY2VzIjpbIi9Vc2Vycy94eW0vY29kZS90Ym9vay9vbnRvbl9jYy9vbmlvbi1sYXVuY2gvZnJvbnRlbmQvbm9kZV9tb2R1bGVzL0BwZWN1bGlhci9hc24xLXNjaGVtYS9idWlsZC9lczIwMTUvaW5kZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0ICogZnJvbSBcIi4vY29udmVydGVyc1wiO1xuZXhwb3J0ICogZnJvbSBcIi4vdHlwZXMvaW5kZXhcIjtcbmV4cG9ydCB7IEFzblByb3AsIEFzblR5cGUsIEFzbkNob2ljZVR5cGUsIEFzblNlcXVlbmNlVHlwZSwgQXNuU2V0VHlwZSB9IGZyb20gXCIuL2RlY29yYXRvcnNcIjtcbmV4cG9ydCB7IEFzblR5cGVUeXBlcywgQXNuUHJvcFR5cGVzIH0gZnJvbSBcIi4vZW51bXNcIjtcbmV4cG9ydCB7IEFzblBhcnNlciB9IGZyb20gXCIuL3BhcnNlclwiO1xuZXhwb3J0IHsgQXNuU2VyaWFsaXplciB9IGZyb20gXCIuL3NlcmlhbGl6ZXJcIjtcbmV4cG9ydCAqIGZyb20gXCIuL2Vycm9yc1wiO1xuZXhwb3J0ICogZnJvbSBcIi4vb2JqZWN0c1wiO1xuZXhwb3J0ICogZnJvbSBcIi4vY29udmVydFwiO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@peculiar/asn1-schema/build/es2015/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@peculiar/asn1-schema/build/es2015/objects.js":
/*!********************************************************************!*\
  !*** ./node_modules/@peculiar/asn1-schema/build/es2015/objects.js ***!
  \********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AsnArray: () => (/* binding */ AsnArray)\n/* harmony export */ });\nclass AsnArray extends Array {\n    constructor(items = []) {\n        if (typeof items === \"number\") {\n            super(items);\n        }\n        else {\n            super();\n            for (const item of items) {\n                this.push(item);\n            }\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHBlY3VsaWFyL2FzbjEtc2NoZW1hL2J1aWxkL2VzMjAxNS9vYmplY3RzLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiL1VzZXJzL3h5bS9jb2RlL3Rib29rL29udG9uX2NjL29uaW9uLWxhdW5jaC9mcm9udGVuZC9ub2RlX21vZHVsZXMvQHBlY3VsaWFyL2FzbjEtc2NoZW1hL2J1aWxkL2VzMjAxNS9vYmplY3RzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBjbGFzcyBBc25BcnJheSBleHRlbmRzIEFycmF5IHtcbiAgICBjb25zdHJ1Y3RvcihpdGVtcyA9IFtdKSB7XG4gICAgICAgIGlmICh0eXBlb2YgaXRlbXMgPT09IFwibnVtYmVyXCIpIHtcbiAgICAgICAgICAgIHN1cGVyKGl0ZW1zKTtcbiAgICAgICAgfVxuICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgIHN1cGVyKCk7XG4gICAgICAgICAgICBmb3IgKGNvbnN0IGl0ZW0gb2YgaXRlbXMpIHtcbiAgICAgICAgICAgICAgICB0aGlzLnB1c2goaXRlbSk7XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICB9XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@peculiar/asn1-schema/build/es2015/objects.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@peculiar/asn1-schema/build/es2015/parser.js":
/*!*******************************************************************!*\
  !*** ./node_modules/@peculiar/asn1-schema/build/es2015/parser.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AsnParser: () => (/* binding */ AsnParser)\n/* harmony export */ });\n/* harmony import */ var asn1js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! asn1js */ \"(ssr)/./node_modules/asn1js/build/index.es.js\");\n/* harmony import */ var _enums__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./enums */ \"(ssr)/./node_modules/@peculiar/asn1-schema/build/es2015/enums.js\");\n/* harmony import */ var _converters__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./converters */ \"(ssr)/./node_modules/@peculiar/asn1-schema/build/es2015/converters.js\");\n/* harmony import */ var _errors__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./errors */ \"(ssr)/./node_modules/@peculiar/asn1-schema/build/es2015/errors/index.js\");\n/* harmony import */ var _helper__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./helper */ \"(ssr)/./node_modules/@peculiar/asn1-schema/build/es2015/helper.js\");\n/* harmony import */ var _storage__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./storage */ \"(ssr)/./node_modules/@peculiar/asn1-schema/build/es2015/storage.js\");\n\n\n\n\n\n\nclass AsnParser {\n    static parse(data, target) {\n        const asn1Parsed = asn1js__WEBPACK_IMPORTED_MODULE_0__.fromBER(data);\n        if (asn1Parsed.result.error) {\n            throw new Error(asn1Parsed.result.error);\n        }\n        const res = this.fromASN(asn1Parsed.result, target);\n        return res;\n    }\n    static fromASN(asn1Schema, target) {\n        try {\n            if ((0,_helper__WEBPACK_IMPORTED_MODULE_4__.isConvertible)(target)) {\n                const value = new target();\n                return value.fromASN(asn1Schema);\n            }\n            const schema = _storage__WEBPACK_IMPORTED_MODULE_5__.schemaStorage.get(target);\n            _storage__WEBPACK_IMPORTED_MODULE_5__.schemaStorage.cache(target);\n            let targetSchema = schema.schema;\n            const choiceResult = this.handleChoiceTypes(asn1Schema, schema, target, targetSchema);\n            if (choiceResult === null || choiceResult === void 0 ? void 0 : choiceResult.result) {\n                return choiceResult.result;\n            }\n            if (choiceResult === null || choiceResult === void 0 ? void 0 : choiceResult.targetSchema) {\n                targetSchema = choiceResult.targetSchema;\n            }\n            const sequenceResult = this.handleSequenceTypes(asn1Schema, schema, target, targetSchema);\n            if (sequenceResult && \"isManualMapping\" in sequenceResult) {\n                return sequenceResult.result;\n            }\n            const asn1ComparedSchema = sequenceResult;\n            const res = new target();\n            if ((0,_helper__WEBPACK_IMPORTED_MODULE_4__.isTypeOfArray)(target)) {\n                return this.handleArrayTypes(asn1Schema, schema, target);\n            }\n            this.processSchemaItems(schema, asn1ComparedSchema, res);\n            return res;\n        }\n        catch (error) {\n            if (error instanceof _errors__WEBPACK_IMPORTED_MODULE_3__.AsnSchemaValidationError) {\n                error.schemas.push(target.name);\n            }\n            throw error;\n        }\n    }\n    static handleChoiceTypes(asn1Schema, schema, target, targetSchema) {\n        if (asn1Schema.constructor === asn1js__WEBPACK_IMPORTED_MODULE_0__.Constructed &&\n            schema.type === _enums__WEBPACK_IMPORTED_MODULE_1__.AsnTypeTypes.Choice &&\n            asn1Schema.idBlock.tagClass === 3) {\n            for (const key in schema.items) {\n                const schemaItem = schema.items[key];\n                if (schemaItem.context === asn1Schema.idBlock.tagNumber && schemaItem.implicit) {\n                    if (typeof schemaItem.type === \"function\" &&\n                        _storage__WEBPACK_IMPORTED_MODULE_5__.schemaStorage.has(schemaItem.type)) {\n                        const fieldSchema = _storage__WEBPACK_IMPORTED_MODULE_5__.schemaStorage.get(schemaItem.type);\n                        if (fieldSchema && fieldSchema.type === _enums__WEBPACK_IMPORTED_MODULE_1__.AsnTypeTypes.Sequence) {\n                            const newSeq = new asn1js__WEBPACK_IMPORTED_MODULE_0__.Sequence();\n                            if (\"value\" in asn1Schema.valueBlock &&\n                                Array.isArray(asn1Schema.valueBlock.value) &&\n                                \"value\" in newSeq.valueBlock) {\n                                newSeq.valueBlock.value = asn1Schema.valueBlock.value;\n                                const fieldValue = this.fromASN(newSeq, schemaItem.type);\n                                const res = new target();\n                                res[key] = fieldValue;\n                                return { result: res };\n                            }\n                        }\n                    }\n                }\n            }\n        }\n        else if (asn1Schema.constructor === asn1js__WEBPACK_IMPORTED_MODULE_0__.Constructed &&\n            schema.type !== _enums__WEBPACK_IMPORTED_MODULE_1__.AsnTypeTypes.Choice) {\n            const newTargetSchema = new asn1js__WEBPACK_IMPORTED_MODULE_0__.Constructed({\n                idBlock: {\n                    tagClass: 3,\n                    tagNumber: asn1Schema.idBlock.tagNumber,\n                },\n                value: schema.schema.valueBlock.value,\n            });\n            for (const key in schema.items) {\n                delete asn1Schema[key];\n            }\n            return { targetSchema: newTargetSchema };\n        }\n        return null;\n    }\n    static handleSequenceTypes(asn1Schema, schema, target, targetSchema) {\n        if (schema.type === _enums__WEBPACK_IMPORTED_MODULE_1__.AsnTypeTypes.Sequence) {\n            const optionalChoiceFields = Object.keys(schema.items).filter((key) => {\n                const item = schema.items[key];\n                return (item.optional &&\n                    typeof item.type === \"function\" &&\n                    _storage__WEBPACK_IMPORTED_MODULE_5__.schemaStorage.has(item.type) &&\n                    _storage__WEBPACK_IMPORTED_MODULE_5__.schemaStorage.get(item.type).type === _enums__WEBPACK_IMPORTED_MODULE_1__.AsnTypeTypes.Choice);\n            });\n            if (optionalChoiceFields.length > 0 &&\n                \"value\" in asn1Schema.valueBlock &&\n                Array.isArray(asn1Schema.valueBlock.value) &&\n                target.name === \"CertReqMsg\") {\n                return this.handleManualMapping(asn1Schema, schema, target);\n            }\n            const asn1ComparedSchema = asn1js__WEBPACK_IMPORTED_MODULE_0__.compareSchema({}, asn1Schema, targetSchema);\n            if (!asn1ComparedSchema.verified) {\n                throw new _errors__WEBPACK_IMPORTED_MODULE_3__.AsnSchemaValidationError(`Data does not match to ${target.name} ASN1 schema. ${asn1ComparedSchema.result.error}`);\n            }\n            return asn1ComparedSchema;\n        }\n        else {\n            const asn1ComparedSchema = asn1js__WEBPACK_IMPORTED_MODULE_0__.compareSchema({}, asn1Schema, targetSchema);\n            if (!asn1ComparedSchema.verified) {\n                throw new _errors__WEBPACK_IMPORTED_MODULE_3__.AsnSchemaValidationError(`Data does not match to ${target.name} ASN1 schema. ${asn1ComparedSchema.result.error}`);\n            }\n            return asn1ComparedSchema;\n        }\n    }\n    static handleManualMapping(asn1Schema, schema, target) {\n        const res = new target();\n        const asn1Elements = asn1Schema.valueBlock.value;\n        const schemaKeys = Object.keys(schema.items);\n        let asn1Index = 0;\n        for (let i = 0; i < schemaKeys.length; i++) {\n            const key = schemaKeys[i];\n            const schemaItem = schema.items[key];\n            if (asn1Index >= asn1Elements.length)\n                break;\n            if (schemaItem.repeated) {\n                res[key] = this.processRepeatedField(asn1Elements, asn1Index, schemaItem);\n                break;\n            }\n            else if (typeof schemaItem.type === \"number\") {\n                res[key] = this.processPrimitiveField(asn1Elements[asn1Index], schemaItem);\n                asn1Index++;\n            }\n            else if (this.isOptionalChoiceField(schemaItem)) {\n                const result = this.processOptionalChoiceField(asn1Elements[asn1Index], schemaItem);\n                if (result.processed) {\n                    res[key] = result.value;\n                    asn1Index++;\n                }\n            }\n            else {\n                res[key] = this.fromASN(asn1Elements[asn1Index], schemaItem.type);\n                asn1Index++;\n            }\n        }\n        return { result: res, verified: true, isManualMapping: true };\n    }\n    static processRepeatedField(asn1Elements, asn1Index, schemaItem) {\n        let elementsToProcess = asn1Elements.slice(asn1Index);\n        if (elementsToProcess.length === 1 && elementsToProcess[0].constructor.name === \"Sequence\") {\n            const seq = elementsToProcess[0];\n            if (seq.valueBlock && seq.valueBlock.value && Array.isArray(seq.valueBlock.value)) {\n                elementsToProcess = seq.valueBlock.value;\n            }\n        }\n        if (typeof schemaItem.type === \"number\") {\n            const converter = _converters__WEBPACK_IMPORTED_MODULE_2__.defaultConverter(schemaItem.type);\n            if (!converter)\n                throw new Error(`No converter for ASN.1 type ${schemaItem.type}`);\n            return elementsToProcess\n                .filter((el) => el && el.valueBlock)\n                .map((el) => {\n                try {\n                    return converter.fromASN(el);\n                }\n                catch {\n                    return undefined;\n                }\n            })\n                .filter((v) => v !== undefined);\n        }\n        else {\n            return elementsToProcess\n                .filter((el) => el && el.valueBlock)\n                .map((el) => {\n                try {\n                    return this.fromASN(el, schemaItem.type);\n                }\n                catch {\n                    return undefined;\n                }\n            })\n                .filter((v) => v !== undefined);\n        }\n    }\n    static processPrimitiveField(asn1Element, schemaItem) {\n        const converter = _converters__WEBPACK_IMPORTED_MODULE_2__.defaultConverter(schemaItem.type);\n        if (!converter)\n            throw new Error(`No converter for ASN.1 type ${schemaItem.type}`);\n        return converter.fromASN(asn1Element);\n    }\n    static isOptionalChoiceField(schemaItem) {\n        return (schemaItem.optional &&\n            typeof schemaItem.type === \"function\" &&\n            _storage__WEBPACK_IMPORTED_MODULE_5__.schemaStorage.has(schemaItem.type) &&\n            _storage__WEBPACK_IMPORTED_MODULE_5__.schemaStorage.get(schemaItem.type).type === _enums__WEBPACK_IMPORTED_MODULE_1__.AsnTypeTypes.Choice);\n    }\n    static processOptionalChoiceField(asn1Element, schemaItem) {\n        try {\n            const value = this.fromASN(asn1Element, schemaItem.type);\n            return { processed: true, value };\n        }\n        catch (err) {\n            if (err instanceof _errors__WEBPACK_IMPORTED_MODULE_3__.AsnSchemaValidationError &&\n                /Wrong values for Choice type/.test(err.message)) {\n                return { processed: false };\n            }\n            throw err;\n        }\n    }\n    static handleArrayTypes(asn1Schema, schema, target) {\n        if (!(\"value\" in asn1Schema.valueBlock && Array.isArray(asn1Schema.valueBlock.value))) {\n            throw new Error(`Cannot get items from the ASN.1 parsed value. ASN.1 object is not constructed.`);\n        }\n        const itemType = schema.itemType;\n        if (typeof itemType === \"number\") {\n            const converter = _converters__WEBPACK_IMPORTED_MODULE_2__.defaultConverter(itemType);\n            if (!converter) {\n                throw new Error(`Cannot get default converter for array item of ${target.name} ASN1 schema`);\n            }\n            return target.from(asn1Schema.valueBlock.value, (element) => converter.fromASN(element));\n        }\n        else {\n            return target.from(asn1Schema.valueBlock.value, (element) => this.fromASN(element, itemType));\n        }\n    }\n    static processSchemaItems(schema, asn1ComparedSchema, res) {\n        for (const key in schema.items) {\n            const asn1SchemaValue = asn1ComparedSchema.result[key];\n            if (!asn1SchemaValue) {\n                continue;\n            }\n            const schemaItem = schema.items[key];\n            const schemaItemType = schemaItem.type;\n            if (typeof schemaItemType === \"number\" || (0,_helper__WEBPACK_IMPORTED_MODULE_4__.isConvertible)(schemaItemType)) {\n                res[key] = this.processPrimitiveSchemaItem(asn1SchemaValue, schemaItem, schemaItemType);\n            }\n            else {\n                res[key] = this.processComplexSchemaItem(asn1SchemaValue, schemaItem, schemaItemType);\n            }\n        }\n    }\n    static processPrimitiveSchemaItem(asn1SchemaValue, schemaItem, schemaItemType) {\n        var _a;\n        const converter = (_a = schemaItem.converter) !== null && _a !== void 0 ? _a : ((0,_helper__WEBPACK_IMPORTED_MODULE_4__.isConvertible)(schemaItemType)\n            ? new schemaItemType()\n            : null);\n        if (!converter) {\n            throw new Error(\"Converter is empty\");\n        }\n        if (schemaItem.repeated) {\n            return this.processRepeatedPrimitiveItem(asn1SchemaValue, schemaItem, converter);\n        }\n        else {\n            return this.processSinglePrimitiveItem(asn1SchemaValue, schemaItem, schemaItemType, converter);\n        }\n    }\n    static processRepeatedPrimitiveItem(asn1SchemaValue, schemaItem, converter) {\n        if (schemaItem.implicit) {\n            const Container = schemaItem.repeated === \"sequence\" ? asn1js__WEBPACK_IMPORTED_MODULE_0__.Sequence : asn1js__WEBPACK_IMPORTED_MODULE_0__.Set;\n            const newItem = new Container();\n            newItem.valueBlock = asn1SchemaValue.valueBlock;\n            const newItemAsn = asn1js__WEBPACK_IMPORTED_MODULE_0__.fromBER(newItem.toBER(false));\n            if (newItemAsn.offset === -1) {\n                throw new Error(`Cannot parse the child item. ${newItemAsn.result.error}`);\n            }\n            if (!(\"value\" in newItemAsn.result.valueBlock &&\n                Array.isArray(newItemAsn.result.valueBlock.value))) {\n                throw new Error(\"Cannot get items from the ASN.1 parsed value. ASN.1 object is not constructed.\");\n            }\n            const value = newItemAsn.result.valueBlock.value;\n            return Array.from(value, (element) => converter.fromASN(element));\n        }\n        else {\n            return Array.from(asn1SchemaValue, (element) => converter.fromASN(element));\n        }\n    }\n    static processSinglePrimitiveItem(asn1SchemaValue, schemaItem, schemaItemType, converter) {\n        let value = asn1SchemaValue;\n        if (schemaItem.implicit) {\n            let newItem;\n            if ((0,_helper__WEBPACK_IMPORTED_MODULE_4__.isConvertible)(schemaItemType)) {\n                newItem = new schemaItemType().toSchema(\"\");\n            }\n            else {\n                const Asn1TypeName = _enums__WEBPACK_IMPORTED_MODULE_1__.AsnPropTypes[schemaItemType];\n                const Asn1Type = asn1js__WEBPACK_IMPORTED_MODULE_0__[Asn1TypeName];\n                if (!Asn1Type) {\n                    throw new Error(`Cannot get '${Asn1TypeName}' class from asn1js module`);\n                }\n                newItem = new Asn1Type();\n            }\n            newItem.valueBlock = value.valueBlock;\n            value = asn1js__WEBPACK_IMPORTED_MODULE_0__.fromBER(newItem.toBER(false)).result;\n        }\n        return converter.fromASN(value);\n    }\n    static processComplexSchemaItem(asn1SchemaValue, schemaItem, schemaItemType) {\n        if (schemaItem.repeated) {\n            if (!Array.isArray(asn1SchemaValue)) {\n                throw new Error(\"Cannot get list of items from the ASN.1 parsed value. ASN.1 value should be iterable.\");\n            }\n            return Array.from(asn1SchemaValue, (element) => this.fromASN(element, schemaItemType));\n        }\n        else {\n            const valueToProcess = this.handleImplicitTagging(asn1SchemaValue, schemaItem, schemaItemType);\n            if (this.isOptionalChoiceField(schemaItem)) {\n                try {\n                    return this.fromASN(valueToProcess, schemaItemType);\n                }\n                catch (err) {\n                    if (err instanceof _errors__WEBPACK_IMPORTED_MODULE_3__.AsnSchemaValidationError &&\n                        /Wrong values for Choice type/.test(err.message)) {\n                        return undefined;\n                    }\n                    throw err;\n                }\n            }\n            else {\n                return this.fromASN(valueToProcess, schemaItemType);\n            }\n        }\n    }\n    static handleImplicitTagging(asn1SchemaValue, schemaItem, schemaItemType) {\n        if (schemaItem.implicit && typeof schemaItem.context === \"number\") {\n            const schema = _storage__WEBPACK_IMPORTED_MODULE_5__.schemaStorage.get(schemaItemType);\n            if (schema.type === _enums__WEBPACK_IMPORTED_MODULE_1__.AsnTypeTypes.Sequence) {\n                const newSeq = new asn1js__WEBPACK_IMPORTED_MODULE_0__.Sequence();\n                if (\"value\" in asn1SchemaValue.valueBlock &&\n                    Array.isArray(asn1SchemaValue.valueBlock.value) &&\n                    \"value\" in newSeq.valueBlock) {\n                    newSeq.valueBlock.value = asn1SchemaValue.valueBlock.value;\n                    return newSeq;\n                }\n            }\n            else if (schema.type === _enums__WEBPACK_IMPORTED_MODULE_1__.AsnTypeTypes.Set) {\n                const newSet = new asn1js__WEBPACK_IMPORTED_MODULE_0__.Set();\n                if (\"value\" in asn1SchemaValue.valueBlock &&\n                    Array.isArray(asn1SchemaValue.valueBlock.value) &&\n                    \"value\" in newSet.valueBlock) {\n                    newSet.valueBlock.value = asn1SchemaValue.valueBlock.value;\n                    return newSet;\n                }\n            }\n        }\n        return asn1SchemaValue;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@peculiar/asn1-schema/build/es2015/parser.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@peculiar/asn1-schema/build/es2015/schema.js":
/*!*******************************************************************!*\
  !*** ./node_modules/@peculiar/asn1-schema/build/es2015/schema.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AsnSchemaStorage: () => (/* binding */ AsnSchemaStorage)\n/* harmony export */ });\n/* harmony import */ var asn1js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! asn1js */ \"(ssr)/./node_modules/asn1js/build/index.es.js\");\n/* harmony import */ var _enums__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./enums */ \"(ssr)/./node_modules/@peculiar/asn1-schema/build/es2015/enums.js\");\n/* harmony import */ var _helper__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./helper */ \"(ssr)/./node_modules/@peculiar/asn1-schema/build/es2015/helper.js\");\n\n\n\nclass AsnSchemaStorage {\n    constructor() {\n        this.items = new WeakMap();\n    }\n    has(target) {\n        return this.items.has(target);\n    }\n    get(target, checkSchema = false) {\n        const schema = this.items.get(target);\n        if (!schema) {\n            throw new Error(`Cannot get schema for '${target.prototype.constructor.name}' target`);\n        }\n        if (checkSchema && !schema.schema) {\n            throw new Error(`Schema '${target.prototype.constructor.name}' doesn't contain ASN.1 schema. Call 'AsnSchemaStorage.cache'.`);\n        }\n        return schema;\n    }\n    cache(target) {\n        const schema = this.get(target);\n        if (!schema.schema) {\n            schema.schema = this.create(target, true);\n        }\n    }\n    createDefault(target) {\n        const schema = { type: _enums__WEBPACK_IMPORTED_MODULE_1__.AsnTypeTypes.Sequence, items: {} };\n        const parentSchema = this.findParentSchema(target);\n        if (parentSchema) {\n            Object.assign(schema, parentSchema);\n            schema.items = Object.assign({}, schema.items, parentSchema.items);\n        }\n        return schema;\n    }\n    create(target, useNames) {\n        const schema = this.items.get(target) || this.createDefault(target);\n        const asn1Value = [];\n        for (const key in schema.items) {\n            const item = schema.items[key];\n            const name = useNames ? key : \"\";\n            let asn1Item;\n            if (typeof item.type === \"number\") {\n                const Asn1TypeName = _enums__WEBPACK_IMPORTED_MODULE_1__.AsnPropTypes[item.type];\n                const Asn1Type = asn1js__WEBPACK_IMPORTED_MODULE_0__[Asn1TypeName];\n                if (!Asn1Type) {\n                    throw new Error(`Cannot get ASN1 class by name '${Asn1TypeName}'`);\n                }\n                asn1Item = new Asn1Type({ name });\n            }\n            else if ((0,_helper__WEBPACK_IMPORTED_MODULE_2__.isConvertible)(item.type)) {\n                const instance = new item.type();\n                asn1Item = instance.toSchema(name);\n            }\n            else if (item.optional) {\n                const itemSchema = this.get(item.type);\n                if (itemSchema.type === _enums__WEBPACK_IMPORTED_MODULE_1__.AsnTypeTypes.Choice) {\n                    asn1Item = new asn1js__WEBPACK_IMPORTED_MODULE_0__.Any({ name });\n                }\n                else {\n                    asn1Item = this.create(item.type, false);\n                    asn1Item.name = name;\n                }\n            }\n            else {\n                asn1Item = new asn1js__WEBPACK_IMPORTED_MODULE_0__.Any({ name });\n            }\n            const optional = !!item.optional || item.defaultValue !== undefined;\n            if (item.repeated) {\n                asn1Item.name = \"\";\n                const Container = item.repeated === \"set\" ? asn1js__WEBPACK_IMPORTED_MODULE_0__.Set : asn1js__WEBPACK_IMPORTED_MODULE_0__.Sequence;\n                asn1Item = new Container({\n                    name: \"\",\n                    value: [new asn1js__WEBPACK_IMPORTED_MODULE_0__.Repeated({ name, value: asn1Item })],\n                });\n            }\n            if (item.context !== null && item.context !== undefined) {\n                if (item.implicit) {\n                    if (typeof item.type === \"number\" || (0,_helper__WEBPACK_IMPORTED_MODULE_2__.isConvertible)(item.type)) {\n                        const Container = item.repeated ? asn1js__WEBPACK_IMPORTED_MODULE_0__.Constructed : asn1js__WEBPACK_IMPORTED_MODULE_0__.Primitive;\n                        asn1Value.push(new Container({ name, optional, idBlock: { tagClass: 3, tagNumber: item.context } }));\n                    }\n                    else {\n                        this.cache(item.type);\n                        const isRepeated = !!item.repeated;\n                        let value = !isRepeated ? this.get(item.type, true).schema : asn1Item;\n                        value =\n                            \"valueBlock\" in value\n                                ? value.valueBlock.value\n                                :\n                                    value.value;\n                        asn1Value.push(new asn1js__WEBPACK_IMPORTED_MODULE_0__.Constructed({\n                            name: !isRepeated ? name : \"\",\n                            optional,\n                            idBlock: { tagClass: 3, tagNumber: item.context },\n                            value: value,\n                        }));\n                    }\n                }\n                else {\n                    asn1Value.push(new asn1js__WEBPACK_IMPORTED_MODULE_0__.Constructed({\n                        optional,\n                        idBlock: { tagClass: 3, tagNumber: item.context },\n                        value: [asn1Item],\n                    }));\n                }\n            }\n            else {\n                asn1Item.optional = optional;\n                asn1Value.push(asn1Item);\n            }\n        }\n        switch (schema.type) {\n            case _enums__WEBPACK_IMPORTED_MODULE_1__.AsnTypeTypes.Sequence:\n                return new asn1js__WEBPACK_IMPORTED_MODULE_0__.Sequence({ value: asn1Value, name: \"\" });\n            case _enums__WEBPACK_IMPORTED_MODULE_1__.AsnTypeTypes.Set:\n                return new asn1js__WEBPACK_IMPORTED_MODULE_0__.Set({ value: asn1Value, name: \"\" });\n            case _enums__WEBPACK_IMPORTED_MODULE_1__.AsnTypeTypes.Choice:\n                return new asn1js__WEBPACK_IMPORTED_MODULE_0__.Choice({ value: asn1Value, name: \"\" });\n            default:\n                throw new Error(`Unsupported ASN1 type in use`);\n        }\n    }\n    set(target, schema) {\n        this.items.set(target, schema);\n        return this;\n    }\n    findParentSchema(target) {\n        const parent = Object.getPrototypeOf(target);\n        if (parent) {\n            const schema = this.items.get(parent);\n            return schema || this.findParentSchema(parent);\n        }\n        return null;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@peculiar/asn1-schema/build/es2015/schema.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@peculiar/asn1-schema/build/es2015/serializer.js":
/*!***********************************************************************!*\
  !*** ./node_modules/@peculiar/asn1-schema/build/es2015/serializer.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AsnSerializer: () => (/* binding */ AsnSerializer)\n/* harmony export */ });\n/* harmony import */ var asn1js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! asn1js */ \"(ssr)/./node_modules/asn1js/build/index.es.js\");\n/* harmony import */ var _converters__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./converters */ \"(ssr)/./node_modules/@peculiar/asn1-schema/build/es2015/converters.js\");\n/* harmony import */ var _enums__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./enums */ \"(ssr)/./node_modules/@peculiar/asn1-schema/build/es2015/enums.js\");\n/* harmony import */ var _helper__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./helper */ \"(ssr)/./node_modules/@peculiar/asn1-schema/build/es2015/helper.js\");\n/* harmony import */ var _storage__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./storage */ \"(ssr)/./node_modules/@peculiar/asn1-schema/build/es2015/storage.js\");\n\n\n\n\n\nclass AsnSerializer {\n    static serialize(obj) {\n        if (obj instanceof asn1js__WEBPACK_IMPORTED_MODULE_0__.BaseBlock) {\n            return obj.toBER(false);\n        }\n        return this.toASN(obj).toBER(false);\n    }\n    static toASN(obj) {\n        if (obj && typeof obj === \"object\" && (0,_helper__WEBPACK_IMPORTED_MODULE_3__.isConvertible)(obj)) {\n            return obj.toASN();\n        }\n        if (!(obj && typeof obj === \"object\")) {\n            throw new TypeError(\"Parameter 1 should be type of Object.\");\n        }\n        const target = obj.constructor;\n        const schema = _storage__WEBPACK_IMPORTED_MODULE_4__.schemaStorage.get(target);\n        _storage__WEBPACK_IMPORTED_MODULE_4__.schemaStorage.cache(target);\n        let asn1Value = [];\n        if (schema.itemType) {\n            if (!Array.isArray(obj)) {\n                throw new TypeError(\"Parameter 1 should be type of Array.\");\n            }\n            if (typeof schema.itemType === \"number\") {\n                const converter = _converters__WEBPACK_IMPORTED_MODULE_1__.defaultConverter(schema.itemType);\n                if (!converter) {\n                    throw new Error(`Cannot get default converter for array item of ${target.name} ASN1 schema`);\n                }\n                asn1Value = obj.map((o) => converter.toASN(o));\n            }\n            else {\n                asn1Value = obj.map((o) => this.toAsnItem({ type: schema.itemType }, \"[]\", target, o));\n            }\n        }\n        else {\n            for (const key in schema.items) {\n                const schemaItem = schema.items[key];\n                const objProp = obj[key];\n                if (objProp === undefined ||\n                    schemaItem.defaultValue === objProp ||\n                    (typeof schemaItem.defaultValue === \"object\" &&\n                        typeof objProp === \"object\" &&\n                        (0,_helper__WEBPACK_IMPORTED_MODULE_3__.isArrayEqual)(this.serialize(schemaItem.defaultValue), this.serialize(objProp)))) {\n                    continue;\n                }\n                const asn1Item = AsnSerializer.toAsnItem(schemaItem, key, target, objProp);\n                if (typeof schemaItem.context === \"number\") {\n                    if (schemaItem.implicit) {\n                        if (!schemaItem.repeated &&\n                            (typeof schemaItem.type === \"number\" || (0,_helper__WEBPACK_IMPORTED_MODULE_3__.isConvertible)(schemaItem.type))) {\n                            const value = {};\n                            value.valueHex =\n                                asn1Item instanceof asn1js__WEBPACK_IMPORTED_MODULE_0__.Null\n                                    ? asn1Item.valueBeforeDecodeView\n                                    : asn1Item.valueBlock.toBER();\n                            asn1Value.push(new asn1js__WEBPACK_IMPORTED_MODULE_0__.Primitive({\n                                optional: schemaItem.optional,\n                                idBlock: {\n                                    tagClass: 3,\n                                    tagNumber: schemaItem.context,\n                                },\n                                ...value,\n                            }));\n                        }\n                        else {\n                            asn1Value.push(new asn1js__WEBPACK_IMPORTED_MODULE_0__.Constructed({\n                                optional: schemaItem.optional,\n                                idBlock: {\n                                    tagClass: 3,\n                                    tagNumber: schemaItem.context,\n                                },\n                                value: asn1Item.valueBlock.value,\n                            }));\n                        }\n                    }\n                    else {\n                        asn1Value.push(new asn1js__WEBPACK_IMPORTED_MODULE_0__.Constructed({\n                            optional: schemaItem.optional,\n                            idBlock: {\n                                tagClass: 3,\n                                tagNumber: schemaItem.context,\n                            },\n                            value: [asn1Item],\n                        }));\n                    }\n                }\n                else if (schemaItem.repeated) {\n                    asn1Value = asn1Value.concat(asn1Item);\n                }\n                else {\n                    asn1Value.push(asn1Item);\n                }\n            }\n        }\n        let asnSchema;\n        switch (schema.type) {\n            case _enums__WEBPACK_IMPORTED_MODULE_2__.AsnTypeTypes.Sequence:\n                asnSchema = new asn1js__WEBPACK_IMPORTED_MODULE_0__.Sequence({ value: asn1Value });\n                break;\n            case _enums__WEBPACK_IMPORTED_MODULE_2__.AsnTypeTypes.Set:\n                asnSchema = new asn1js__WEBPACK_IMPORTED_MODULE_0__.Set({ value: asn1Value });\n                break;\n            case _enums__WEBPACK_IMPORTED_MODULE_2__.AsnTypeTypes.Choice:\n                if (!asn1Value[0]) {\n                    throw new Error(`Schema '${target.name}' has wrong data. Choice cannot be empty.`);\n                }\n                asnSchema = asn1Value[0];\n                break;\n        }\n        return asnSchema;\n    }\n    static toAsnItem(schemaItem, key, target, objProp) {\n        let asn1Item;\n        if (typeof schemaItem.type === \"number\") {\n            const converter = schemaItem.converter;\n            if (!converter) {\n                throw new Error(`Property '${key}' doesn't have converter for type ${_enums__WEBPACK_IMPORTED_MODULE_2__.AsnPropTypes[schemaItem.type]} in schema '${target.name}'`);\n            }\n            if (schemaItem.repeated) {\n                if (!Array.isArray(objProp)) {\n                    throw new TypeError(\"Parameter 'objProp' should be type of Array.\");\n                }\n                const items = Array.from(objProp, (element) => converter.toASN(element));\n                const Container = schemaItem.repeated === \"sequence\" ? asn1js__WEBPACK_IMPORTED_MODULE_0__.Sequence : asn1js__WEBPACK_IMPORTED_MODULE_0__.Set;\n                asn1Item = new Container({\n                    value: items,\n                });\n            }\n            else {\n                asn1Item = converter.toASN(objProp);\n            }\n        }\n        else {\n            if (schemaItem.repeated) {\n                if (!Array.isArray(objProp)) {\n                    throw new TypeError(\"Parameter 'objProp' should be type of Array.\");\n                }\n                const items = Array.from(objProp, (element) => this.toASN(element));\n                const Container = schemaItem.repeated === \"sequence\" ? asn1js__WEBPACK_IMPORTED_MODULE_0__.Sequence : asn1js__WEBPACK_IMPORTED_MODULE_0__.Set;\n                asn1Item = new Container({\n                    value: items,\n                });\n            }\n            else {\n                asn1Item = this.toASN(objProp);\n            }\n        }\n        return asn1Item;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@peculiar/asn1-schema/build/es2015/serializer.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@peculiar/asn1-schema/build/es2015/storage.js":
/*!********************************************************************!*\
  !*** ./node_modules/@peculiar/asn1-schema/build/es2015/storage.js ***!
  \********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   schemaStorage: () => (/* binding */ schemaStorage)\n/* harmony export */ });\n/* harmony import */ var _schema__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./schema */ \"(ssr)/./node_modules/@peculiar/asn1-schema/build/es2015/schema.js\");\n\nconst schemaStorage = new _schema__WEBPACK_IMPORTED_MODULE_0__.AsnSchemaStorage();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHBlY3VsaWFyL2FzbjEtc2NoZW1hL2J1aWxkL2VzMjAxNS9zdG9yYWdlLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQTRDO0FBQ3JDLDBCQUEwQixxREFBZ0IiLCJzb3VyY2VzIjpbIi9Vc2Vycy94eW0vY29kZS90Ym9vay9vbnRvbl9jYy9vbmlvbi1sYXVuY2gvZnJvbnRlbmQvbm9kZV9tb2R1bGVzL0BwZWN1bGlhci9hc24xLXNjaGVtYS9idWlsZC9lczIwMTUvc3RvcmFnZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBBc25TY2hlbWFTdG9yYWdlIH0gZnJvbSBcIi4vc2NoZW1hXCI7XG5leHBvcnQgY29uc3Qgc2NoZW1hU3RvcmFnZSA9IG5ldyBBc25TY2hlbWFTdG9yYWdlKCk7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@peculiar/asn1-schema/build/es2015/storage.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@peculiar/asn1-schema/build/es2015/types/bit_string.js":
/*!*****************************************************************************!*\
  !*** ./node_modules/@peculiar/asn1-schema/build/es2015/types/bit_string.js ***!
  \*****************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BitString: () => (/* binding */ BitString)\n/* harmony export */ });\n/* harmony import */ var asn1js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! asn1js */ \"(ssr)/./node_modules/asn1js/build/index.es.js\");\n/* harmony import */ var pvtsutils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! pvtsutils */ \"(ssr)/./node_modules/pvtsutils/build/index.es.js\");\n\n\nclass BitString {\n    constructor(params, unusedBits = 0) {\n        this.unusedBits = 0;\n        this.value = new ArrayBuffer(0);\n        if (params) {\n            if (typeof params === \"number\") {\n                this.fromNumber(params);\n            }\n            else if (pvtsutils__WEBPACK_IMPORTED_MODULE_1__.BufferSourceConverter.isBufferSource(params)) {\n                this.unusedBits = unusedBits;\n                this.value = pvtsutils__WEBPACK_IMPORTED_MODULE_1__.BufferSourceConverter.toArrayBuffer(params);\n            }\n            else {\n                throw TypeError(\"Unsupported type of 'params' argument for BitString\");\n            }\n        }\n    }\n    fromASN(asn) {\n        if (!(asn instanceof asn1js__WEBPACK_IMPORTED_MODULE_0__.BitString)) {\n            throw new TypeError(\"Argument 'asn' is not instance of ASN.1 BitString\");\n        }\n        this.unusedBits = asn.valueBlock.unusedBits;\n        this.value = asn.valueBlock.valueHex;\n        return this;\n    }\n    toASN() {\n        return new asn1js__WEBPACK_IMPORTED_MODULE_0__.BitString({ unusedBits: this.unusedBits, valueHex: this.value });\n    }\n    toSchema(name) {\n        return new asn1js__WEBPACK_IMPORTED_MODULE_0__.BitString({ name });\n    }\n    toNumber() {\n        let res = \"\";\n        const uintArray = new Uint8Array(this.value);\n        for (const octet of uintArray) {\n            res += octet.toString(2).padStart(8, \"0\");\n        }\n        res = res.split(\"\").reverse().join(\"\");\n        if (this.unusedBits) {\n            res = res.slice(this.unusedBits).padStart(this.unusedBits, \"0\");\n        }\n        return parseInt(res, 2);\n    }\n    fromNumber(value) {\n        let bits = value.toString(2);\n        const octetSize = (bits.length + 7) >> 3;\n        this.unusedBits = (octetSize << 3) - bits.length;\n        const octets = new Uint8Array(octetSize);\n        bits = bits\n            .padStart(octetSize << 3, \"0\")\n            .split(\"\")\n            .reverse()\n            .join(\"\");\n        let index = 0;\n        while (index < octetSize) {\n            octets[index] = parseInt(bits.slice(index << 3, (index << 3) + 8), 2);\n            index++;\n        }\n        this.value = octets.buffer;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@peculiar/asn1-schema/build/es2015/types/bit_string.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@peculiar/asn1-schema/build/es2015/types/index.js":
/*!************************************************************************!*\
  !*** ./node_modules/@peculiar/asn1-schema/build/es2015/types/index.js ***!
  \************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BitString: () => (/* reexport safe */ _bit_string__WEBPACK_IMPORTED_MODULE_0__.BitString),\n/* harmony export */   OctetString: () => (/* reexport safe */ _octet_string__WEBPACK_IMPORTED_MODULE_1__.OctetString)\n/* harmony export */ });\n/* harmony import */ var _bit_string__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./bit_string */ \"(ssr)/./node_modules/@peculiar/asn1-schema/build/es2015/types/bit_string.js\");\n/* harmony import */ var _octet_string__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./octet_string */ \"(ssr)/./node_modules/@peculiar/asn1-schema/build/es2015/types/octet_string.js\");\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHBlY3VsaWFyL2FzbjEtc2NoZW1hL2J1aWxkL2VzMjAxNS90eXBlcy9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQTZCO0FBQ0UiLCJzb3VyY2VzIjpbIi9Vc2Vycy94eW0vY29kZS90Ym9vay9vbnRvbl9jYy9vbmlvbi1sYXVuY2gvZnJvbnRlbmQvbm9kZV9tb2R1bGVzL0BwZWN1bGlhci9hc24xLXNjaGVtYS9idWlsZC9lczIwMTUvdHlwZXMvaW5kZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0ICogZnJvbSBcIi4vYml0X3N0cmluZ1wiO1xuZXhwb3J0ICogZnJvbSBcIi4vb2N0ZXRfc3RyaW5nXCI7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@peculiar/asn1-schema/build/es2015/types/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@peculiar/asn1-schema/build/es2015/types/octet_string.js":
/*!*******************************************************************************!*\
  !*** ./node_modules/@peculiar/asn1-schema/build/es2015/types/octet_string.js ***!
  \*******************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OctetString: () => (/* binding */ OctetString)\n/* harmony export */ });\n/* harmony import */ var asn1js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! asn1js */ \"(ssr)/./node_modules/asn1js/build/index.es.js\");\n/* harmony import */ var pvtsutils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! pvtsutils */ \"(ssr)/./node_modules/pvtsutils/build/index.es.js\");\n\n\nclass OctetString {\n    get byteLength() {\n        return this.buffer.byteLength;\n    }\n    get byteOffset() {\n        return 0;\n    }\n    constructor(param) {\n        if (typeof param === \"number\") {\n            this.buffer = new ArrayBuffer(param);\n        }\n        else {\n            if (pvtsutils__WEBPACK_IMPORTED_MODULE_1__.BufferSourceConverter.isBufferSource(param)) {\n                this.buffer = pvtsutils__WEBPACK_IMPORTED_MODULE_1__.BufferSourceConverter.toArrayBuffer(param);\n            }\n            else if (Array.isArray(param)) {\n                this.buffer = new Uint8Array(param);\n            }\n            else {\n                this.buffer = new ArrayBuffer(0);\n            }\n        }\n    }\n    fromASN(asn) {\n        if (!(asn instanceof asn1js__WEBPACK_IMPORTED_MODULE_0__.OctetString)) {\n            throw new TypeError(\"Argument 'asn' is not instance of ASN.1 OctetString\");\n        }\n        this.buffer = asn.valueBlock.valueHex;\n        return this;\n    }\n    toASN() {\n        return new asn1js__WEBPACK_IMPORTED_MODULE_0__.OctetString({ valueHex: this.buffer });\n    }\n    toSchema(name) {\n        return new asn1js__WEBPACK_IMPORTED_MODULE_0__.OctetString({ name });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@peculiar/asn1-schema/build/es2015/types/octet_string.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@peculiar/json-schema/build/index.es.js":
/*!**************************************************************!*\
  !*** ./node_modules/@peculiar/json-schema/build/index.es.js ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   JsonError: () => (/* binding */ JsonError),\n/* harmony export */   JsonParser: () => (/* binding */ JsonParser),\n/* harmony export */   JsonProp: () => (/* binding */ JsonProp),\n/* harmony export */   JsonPropTypes: () => (/* binding */ JsonPropTypes),\n/* harmony export */   JsonSerializer: () => (/* binding */ JsonSerializer),\n/* harmony export */   KeyError: () => (/* binding */ KeyError),\n/* harmony export */   ParserError: () => (/* binding */ ParserError),\n/* harmony export */   SerializerError: () => (/* binding */ SerializerError),\n/* harmony export */   TransformError: () => (/* binding */ TransformError),\n/* harmony export */   ValidationError: () => (/* binding */ ValidationError)\n/* harmony export */ });\n/**\n * Copyright (c) 2020, Peculiar Ventures, All rights reserved.\n */\n\nclass JsonError extends Error {\r\n    constructor(message, innerError) {\r\n        super(innerError\r\n            ? `${message}. See the inner exception for more details.`\r\n            : message);\r\n        this.message = message;\r\n        this.innerError = innerError;\r\n    }\r\n}\n\nclass TransformError extends JsonError {\r\n    constructor(schema, message, innerError) {\r\n        super(message, innerError);\r\n        this.schema = schema;\r\n    }\r\n}\n\nclass ParserError extends TransformError {\r\n    constructor(schema, message, innerError) {\r\n        super(schema, `JSON doesn't match to '${schema.target.name}' schema. ${message}`, innerError);\r\n    }\r\n}\n\nclass ValidationError extends JsonError {\r\n}\n\nclass SerializerError extends JsonError {\r\n    constructor(schemaName, message, innerError) {\r\n        super(`Cannot serialize by '${schemaName}' schema. ${message}`, innerError);\r\n        this.schemaName = schemaName;\r\n    }\r\n}\n\nclass KeyError extends ParserError {\r\n    constructor(schema, keys, errors = {}) {\r\n        super(schema, \"Some keys doesn't match to schema\");\r\n        this.keys = keys;\r\n        this.errors = errors;\r\n    }\r\n}\n\nvar JsonPropTypes;\r\n(function (JsonPropTypes) {\r\n    JsonPropTypes[JsonPropTypes[\"Any\"] = 0] = \"Any\";\r\n    JsonPropTypes[JsonPropTypes[\"Boolean\"] = 1] = \"Boolean\";\r\n    JsonPropTypes[JsonPropTypes[\"Number\"] = 2] = \"Number\";\r\n    JsonPropTypes[JsonPropTypes[\"String\"] = 3] = \"String\";\r\n})(JsonPropTypes || (JsonPropTypes = {}));\n\nfunction checkType(value, type) {\r\n    switch (type) {\r\n        case JsonPropTypes.Boolean:\r\n            return typeof value === \"boolean\";\r\n        case JsonPropTypes.Number:\r\n            return typeof value === \"number\";\r\n        case JsonPropTypes.String:\r\n            return typeof value === \"string\";\r\n    }\r\n    return true;\r\n}\r\nfunction throwIfTypeIsWrong(value, type) {\r\n    if (!checkType(value, type)) {\r\n        throw new TypeError(`Value must be ${JsonPropTypes[type]}`);\r\n    }\r\n}\r\nfunction isConvertible(target) {\r\n    if (target && target.prototype) {\r\n        if (target.prototype.toJSON && target.prototype.fromJSON) {\r\n            return true;\r\n        }\r\n        else {\r\n            return isConvertible(target.prototype);\r\n        }\r\n    }\r\n    else {\r\n        return !!(target && target.toJSON && target.fromJSON);\r\n    }\r\n}\n\nclass JsonSchemaStorage {\r\n    constructor() {\r\n        this.items = new Map();\r\n    }\r\n    has(target) {\r\n        return this.items.has(target) || !!this.findParentSchema(target);\r\n    }\r\n    get(target) {\r\n        const schema = this.items.get(target) || this.findParentSchema(target);\r\n        if (!schema) {\r\n            throw new Error(\"Cannot get schema for current target\");\r\n        }\r\n        return schema;\r\n    }\r\n    create(target) {\r\n        const schema = { names: {} };\r\n        const parentSchema = this.findParentSchema(target);\r\n        if (parentSchema) {\r\n            Object.assign(schema, parentSchema);\r\n            schema.names = {};\r\n            for (const name in parentSchema.names) {\r\n                schema.names[name] = Object.assign({}, parentSchema.names[name]);\r\n            }\r\n        }\r\n        schema.target = target;\r\n        return schema;\r\n    }\r\n    set(target, schema) {\r\n        this.items.set(target, schema);\r\n        return this;\r\n    }\r\n    findParentSchema(target) {\r\n        const parent = target.__proto__;\r\n        if (parent) {\r\n            const schema = this.items.get(parent);\r\n            return schema || this.findParentSchema(parent);\r\n        }\r\n        return null;\r\n    }\r\n}\n\nconst DEFAULT_SCHEMA = \"default\";\r\nconst schemaStorage = new JsonSchemaStorage();\n\nclass PatternValidation {\r\n    constructor(pattern) {\r\n        this.pattern = new RegExp(pattern);\r\n    }\r\n    validate(value) {\r\n        const pattern = new RegExp(this.pattern.source, this.pattern.flags);\r\n        if (typeof value !== \"string\") {\r\n            throw new ValidationError(\"Incoming value must be string\");\r\n        }\r\n        if (!pattern.exec(value)) {\r\n            throw new ValidationError(`Value doesn't match to pattern '${pattern.toString()}'`);\r\n        }\r\n    }\r\n}\n\nclass InclusiveValidation {\r\n    constructor(min = Number.MIN_VALUE, max = Number.MAX_VALUE) {\r\n        this.min = min;\r\n        this.max = max;\r\n    }\r\n    validate(value) {\r\n        throwIfTypeIsWrong(value, JsonPropTypes.Number);\r\n        if (!(this.min <= value && value <= this.max)) {\r\n            const min = this.min === Number.MIN_VALUE ? \"MIN\" : this.min;\r\n            const max = this.max === Number.MAX_VALUE ? \"MAX\" : this.max;\r\n            throw new ValidationError(`Value doesn't match to diapason [${min},${max}]`);\r\n        }\r\n    }\r\n}\n\nclass ExclusiveValidation {\r\n    constructor(min = Number.MIN_VALUE, max = Number.MAX_VALUE) {\r\n        this.min = min;\r\n        this.max = max;\r\n    }\r\n    validate(value) {\r\n        throwIfTypeIsWrong(value, JsonPropTypes.Number);\r\n        if (!(this.min < value && value < this.max)) {\r\n            const min = this.min === Number.MIN_VALUE ? \"MIN\" : this.min;\r\n            const max = this.max === Number.MAX_VALUE ? \"MAX\" : this.max;\r\n            throw new ValidationError(`Value doesn't match to diapason (${min},${max})`);\r\n        }\r\n    }\r\n}\n\nclass LengthValidation {\r\n    constructor(length, minLength, maxLength) {\r\n        this.length = length;\r\n        this.minLength = minLength;\r\n        this.maxLength = maxLength;\r\n    }\r\n    validate(value) {\r\n        if (this.length !== undefined) {\r\n            if (value.length !== this.length) {\r\n                throw new ValidationError(`Value length must be exactly ${this.length}.`);\r\n            }\r\n            return;\r\n        }\r\n        if (this.minLength !== undefined) {\r\n            if (value.length < this.minLength) {\r\n                throw new ValidationError(`Value length must be more than ${this.minLength}.`);\r\n            }\r\n        }\r\n        if (this.maxLength !== undefined) {\r\n            if (value.length > this.maxLength) {\r\n                throw new ValidationError(`Value length must be less than ${this.maxLength}.`);\r\n            }\r\n        }\r\n    }\r\n}\n\nclass EnumerationValidation {\r\n    constructor(enumeration) {\r\n        this.enumeration = enumeration;\r\n    }\r\n    validate(value) {\r\n        throwIfTypeIsWrong(value, JsonPropTypes.String);\r\n        if (!this.enumeration.includes(value)) {\r\n            throw new ValidationError(`Value must be one of ${this.enumeration.map((v) => `'${v}'`).join(\", \")}`);\r\n        }\r\n    }\r\n}\n\nclass JsonTransform {\r\n    static checkValues(data, schemaItem) {\r\n        const values = Array.isArray(data) ? data : [data];\r\n        for (const value of values) {\r\n            for (const validation of schemaItem.validations) {\r\n                if (validation instanceof LengthValidation && schemaItem.repeated) {\r\n                    validation.validate(data);\r\n                }\r\n                else {\r\n                    validation.validate(value);\r\n                }\r\n            }\r\n        }\r\n    }\r\n    static checkTypes(value, schemaItem) {\r\n        if (schemaItem.repeated && !Array.isArray(value)) {\r\n            throw new TypeError(\"Value must be Array\");\r\n        }\r\n        if (typeof schemaItem.type === \"number\") {\r\n            const values = Array.isArray(value) ? value : [value];\r\n            for (const v of values) {\r\n                throwIfTypeIsWrong(v, schemaItem.type);\r\n            }\r\n        }\r\n    }\r\n    static getSchemaByName(schema, name = DEFAULT_SCHEMA) {\r\n        return { ...schema.names[DEFAULT_SCHEMA], ...schema.names[name] };\r\n    }\r\n}\n\nclass JsonSerializer extends JsonTransform {\r\n    static serialize(obj, options, replacer, space) {\r\n        const json = this.toJSON(obj, options);\r\n        return JSON.stringify(json, replacer, space);\r\n    }\r\n    static toJSON(obj, options = {}) {\r\n        let res;\r\n        let targetSchema = options.targetSchema;\r\n        const schemaName = options.schemaName || DEFAULT_SCHEMA;\r\n        if (isConvertible(obj)) {\r\n            return obj.toJSON();\r\n        }\r\n        if (Array.isArray(obj)) {\r\n            res = [];\r\n            for (const item of obj) {\r\n                res.push(this.toJSON(item, options));\r\n            }\r\n        }\r\n        else if (typeof obj === \"object\") {\r\n            if (targetSchema && !schemaStorage.has(targetSchema)) {\r\n                throw new JsonError(\"Cannot get schema for `targetSchema` param\");\r\n            }\r\n            targetSchema = (targetSchema || obj.constructor);\r\n            if (schemaStorage.has(targetSchema)) {\r\n                const schema = schemaStorage.get(targetSchema);\r\n                res = {};\r\n                const namedSchema = this.getSchemaByName(schema, schemaName);\r\n                for (const key in namedSchema) {\r\n                    try {\r\n                        const item = namedSchema[key];\r\n                        const objItem = obj[key];\r\n                        let value;\r\n                        if ((item.optional && objItem === undefined)\r\n                            || (item.defaultValue !== undefined && objItem === item.defaultValue)) {\r\n                            continue;\r\n                        }\r\n                        if (!item.optional && objItem === undefined) {\r\n                            throw new SerializerError(targetSchema.name, `Property '${key}' is required.`);\r\n                        }\r\n                        if (typeof item.type === \"number\") {\r\n                            if (item.converter) {\r\n                                if (item.repeated) {\r\n                                    value = objItem.map((el) => item.converter.toJSON(el, obj));\r\n                                }\r\n                                else {\r\n                                    value = item.converter.toJSON(objItem, obj);\r\n                                }\r\n                            }\r\n                            else {\r\n                                value = objItem;\r\n                            }\r\n                        }\r\n                        else {\r\n                            if (item.repeated) {\r\n                                value = objItem.map((el) => this.toJSON(el, { schemaName }));\r\n                            }\r\n                            else {\r\n                                value = this.toJSON(objItem, { schemaName });\r\n                            }\r\n                        }\r\n                        this.checkTypes(value, item);\r\n                        this.checkValues(value, item);\r\n                        res[item.name || key] = value;\r\n                    }\r\n                    catch (e) {\r\n                        if (e instanceof SerializerError) {\r\n                            throw e;\r\n                        }\r\n                        else {\r\n                            throw new SerializerError(schema.target.name, `Property '${key}' is wrong. ${e.message}`, e);\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n            else {\r\n                res = {};\r\n                for (const key in obj) {\r\n                    res[key] = this.toJSON(obj[key], { schemaName });\r\n                }\r\n            }\r\n        }\r\n        else {\r\n            res = obj;\r\n        }\r\n        return res;\r\n    }\r\n}\n\nclass JsonParser extends JsonTransform {\r\n    static parse(data, options) {\r\n        const obj = JSON.parse(data);\r\n        return this.fromJSON(obj, options);\r\n    }\r\n    static fromJSON(target, options) {\r\n        const targetSchema = options.targetSchema;\r\n        const schemaName = options.schemaName || DEFAULT_SCHEMA;\r\n        const obj = new targetSchema();\r\n        if (isConvertible(obj)) {\r\n            return obj.fromJSON(target);\r\n        }\r\n        const schema = schemaStorage.get(targetSchema);\r\n        const namedSchema = this.getSchemaByName(schema, schemaName);\r\n        const keyErrors = {};\r\n        if (options.strictProperty && !Array.isArray(target)) {\r\n            JsonParser.checkStrictProperty(target, namedSchema, schema);\r\n        }\r\n        for (const key in namedSchema) {\r\n            try {\r\n                const item = namedSchema[key];\r\n                const name = item.name || key;\r\n                const value = target[name];\r\n                if (value === undefined && (item.optional || item.defaultValue !== undefined)) {\r\n                    continue;\r\n                }\r\n                if (!item.optional && value === undefined) {\r\n                    throw new ParserError(schema, `Property '${name}' is required.`);\r\n                }\r\n                this.checkTypes(value, item);\r\n                this.checkValues(value, item);\r\n                if (typeof (item.type) === \"number\") {\r\n                    if (item.converter) {\r\n                        if (item.repeated) {\r\n                            obj[key] = value.map((el) => item.converter.fromJSON(el, obj));\r\n                        }\r\n                        else {\r\n                            obj[key] = item.converter.fromJSON(value, obj);\r\n                        }\r\n                    }\r\n                    else {\r\n                        obj[key] = value;\r\n                    }\r\n                }\r\n                else {\r\n                    const newOptions = {\r\n                        ...options,\r\n                        targetSchema: item.type,\r\n                        schemaName,\r\n                    };\r\n                    if (item.repeated) {\r\n                        obj[key] = value.map((el) => this.fromJSON(el, newOptions));\r\n                    }\r\n                    else {\r\n                        obj[key] = this.fromJSON(value, newOptions);\r\n                    }\r\n                }\r\n            }\r\n            catch (e) {\r\n                if (!(e instanceof ParserError)) {\r\n                    e = new ParserError(schema, `Property '${key}' is wrong. ${e.message}`, e);\r\n                }\r\n                if (options.strictAllKeys) {\r\n                    keyErrors[key] = e;\r\n                }\r\n                else {\r\n                    throw e;\r\n                }\r\n            }\r\n        }\r\n        const keys = Object.keys(keyErrors);\r\n        if (keys.length) {\r\n            throw new KeyError(schema, keys, keyErrors);\r\n        }\r\n        return obj;\r\n    }\r\n    static checkStrictProperty(target, namedSchema, schema) {\r\n        const jsonProps = Object.keys(target);\r\n        const schemaProps = Object.keys(namedSchema);\r\n        const keys = [];\r\n        for (const key of jsonProps) {\r\n            if (schemaProps.indexOf(key) === -1) {\r\n                keys.push(key);\r\n            }\r\n        }\r\n        if (keys.length) {\r\n            throw new KeyError(schema, keys);\r\n        }\r\n    }\r\n}\n\nfunction getValidations(item) {\r\n    const validations = [];\r\n    if (item.pattern) {\r\n        validations.push(new PatternValidation(item.pattern));\r\n    }\r\n    if (item.type === JsonPropTypes.Number || item.type === JsonPropTypes.Any) {\r\n        if (item.minInclusive !== undefined || item.maxInclusive !== undefined) {\r\n            validations.push(new InclusiveValidation(item.minInclusive, item.maxInclusive));\r\n        }\r\n        if (item.minExclusive !== undefined || item.maxExclusive !== undefined) {\r\n            validations.push(new ExclusiveValidation(item.minExclusive, item.maxExclusive));\r\n        }\r\n        if (item.enumeration !== undefined) {\r\n            validations.push(new EnumerationValidation(item.enumeration));\r\n        }\r\n    }\r\n    if (item.type === JsonPropTypes.String || item.repeated || item.type === JsonPropTypes.Any) {\r\n        if (item.length !== undefined || item.minLength !== undefined || item.maxLength !== undefined) {\r\n            validations.push(new LengthValidation(item.length, item.minLength, item.maxLength));\r\n        }\r\n    }\r\n    return validations;\r\n}\r\nconst JsonProp = (options = {}) => (target, propertyKey) => {\r\n    const errorMessage = `Cannot set type for ${propertyKey} property of ${target.constructor.name} schema`;\r\n    let schema;\r\n    if (!schemaStorage.has(target.constructor)) {\r\n        schema = schemaStorage.create(target.constructor);\r\n        schemaStorage.set(target.constructor, schema);\r\n    }\r\n    else {\r\n        schema = schemaStorage.get(target.constructor);\r\n        if (schema.target !== target.constructor) {\r\n            schema = schemaStorage.create(target.constructor);\r\n            schemaStorage.set(target.constructor, schema);\r\n        }\r\n    }\r\n    const defaultSchema = {\r\n        type: JsonPropTypes.Any,\r\n        validations: [],\r\n    };\r\n    const copyOptions = Object.assign(defaultSchema, options);\r\n    copyOptions.validations = getValidations(copyOptions);\r\n    if (typeof copyOptions.type !== \"number\") {\r\n        if (!schemaStorage.has(copyOptions.type) && !isConvertible(copyOptions.type)) {\r\n            throw new Error(`${errorMessage}. Assigning type doesn't have schema.`);\r\n        }\r\n    }\r\n    let schemaNames;\r\n    if (Array.isArray(options.schema)) {\r\n        schemaNames = options.schema;\r\n    }\r\n    else {\r\n        schemaNames = [options.schema || DEFAULT_SCHEMA];\r\n    }\r\n    for (const schemaName of schemaNames) {\r\n        if (!schema.names[schemaName]) {\r\n            schema.names[schemaName] = {};\r\n        }\r\n        const namedSchema = schema.names[schemaName];\r\n        namedSchema[propertyKey] = copyOptions;\r\n    }\r\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@peculiar/json-schema/build/index.es.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@peculiar/webcrypto/build/webcrypto.es.js":
/*!****************************************************************!*\
  !*** ./node_modules/@peculiar/webcrypto/build/webcrypto.es.js ***!
  \****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Crypto: () => (/* binding */ Crypto),\n/* harmony export */   CryptoKey: () => (/* reexport safe */ webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.CryptoKey)\n/* harmony export */ });\n/* harmony import */ var webcrypto_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! webcrypto-core */ \"(ssr)/./node_modules/webcrypto-core/build/webcrypto-core.es.js\");\n/* harmony import */ var buffer__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! buffer */ \"buffer\");\n/* harmony import */ var buffer__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(buffer__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var crypto__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! crypto */ \"crypto\");\n/* harmony import */ var crypto__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(crypto__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var process__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! process */ \"process\");\n/* harmony import */ var process__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(process__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var tslib__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! tslib */ \"(ssr)/./node_modules/tslib/tslib.es6.mjs\");\n/* harmony import */ var _peculiar_json_schema__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @peculiar/json-schema */ \"(ssr)/./node_modules/@peculiar/json-schema/build/index.es.js\");\n/* harmony import */ var pvtsutils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! pvtsutils */ \"(ssr)/./node_modules/pvtsutils/build/index.es.js\");\n/* harmony import */ var _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @peculiar/asn1-schema */ \"(ssr)/./node_modules/@peculiar/asn1-schema/build/es2015/index.js\");\n/*!\n Copyright (c) Peculiar Ventures, LLC\n*/\n\n\n\n\n\n\n\n\n\n\n\n\nconst JsonBase64UrlConverter = {\n    fromJSON: (value) => buffer__WEBPACK_IMPORTED_MODULE_1__.Buffer.from(pvtsutils__WEBPACK_IMPORTED_MODULE_5__.Convert.FromBase64Url(value)),\n    toJSON: (value) => pvtsutils__WEBPACK_IMPORTED_MODULE_5__.Convert.ToBase64Url(value),\n};\n\nclass CryptoKey extends webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.CryptoKey {\n    constructor() {\n        super(...arguments);\n        this.data = buffer__WEBPACK_IMPORTED_MODULE_1__.Buffer.alloc(0);\n        this.algorithm = { name: \"\" };\n        this.extractable = false;\n        this.type = \"secret\";\n        this.usages = [];\n        this.kty = \"oct\";\n        this.alg = \"\";\n    }\n}\n(0,tslib__WEBPACK_IMPORTED_MODULE_7__.__decorate)([\n    (0,_peculiar_json_schema__WEBPACK_IMPORTED_MODULE_4__.JsonProp)({ name: \"ext\", type: _peculiar_json_schema__WEBPACK_IMPORTED_MODULE_4__.JsonPropTypes.Boolean, optional: true })\n], CryptoKey.prototype, \"extractable\", void 0);\n(0,tslib__WEBPACK_IMPORTED_MODULE_7__.__decorate)([\n    (0,_peculiar_json_schema__WEBPACK_IMPORTED_MODULE_4__.JsonProp)({ name: \"key_ops\", type: _peculiar_json_schema__WEBPACK_IMPORTED_MODULE_4__.JsonPropTypes.String, repeated: true, optional: true })\n], CryptoKey.prototype, \"usages\", void 0);\n(0,tslib__WEBPACK_IMPORTED_MODULE_7__.__decorate)([\n    (0,_peculiar_json_schema__WEBPACK_IMPORTED_MODULE_4__.JsonProp)({ type: _peculiar_json_schema__WEBPACK_IMPORTED_MODULE_4__.JsonPropTypes.String })\n], CryptoKey.prototype, \"kty\", void 0);\n(0,tslib__WEBPACK_IMPORTED_MODULE_7__.__decorate)([\n    (0,_peculiar_json_schema__WEBPACK_IMPORTED_MODULE_4__.JsonProp)({ type: _peculiar_json_schema__WEBPACK_IMPORTED_MODULE_4__.JsonPropTypes.String, optional: true })\n], CryptoKey.prototype, \"alg\", void 0);\n\nclass SymmetricKey extends CryptoKey {\n    constructor() {\n        super(...arguments);\n        this.kty = \"oct\";\n        this.type = \"secret\";\n    }\n}\n\nclass AsymmetricKey extends CryptoKey {\n}\n\nclass AesCryptoKey extends SymmetricKey {\n    get alg() {\n        switch (this.algorithm.name.toUpperCase()) {\n            case \"AES-CBC\":\n                return `A${this.algorithm.length}CBC`;\n            case \"AES-CTR\":\n                return `A${this.algorithm.length}CTR`;\n            case \"AES-GCM\":\n                return `A${this.algorithm.length}GCM`;\n            case \"AES-KW\":\n                return `A${this.algorithm.length}KW`;\n            case \"AES-CMAC\":\n                return `A${this.algorithm.length}CMAC`;\n            case \"AES-ECB\":\n                return `A${this.algorithm.length}ECB`;\n            default:\n                throw new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.AlgorithmError(\"Unsupported algorithm name\");\n        }\n    }\n    set alg(value) {\n    }\n}\n(0,tslib__WEBPACK_IMPORTED_MODULE_7__.__decorate)([\n    (0,_peculiar_json_schema__WEBPACK_IMPORTED_MODULE_4__.JsonProp)({ name: \"k\", converter: JsonBase64UrlConverter })\n], AesCryptoKey.prototype, \"data\", void 0);\n\nclass AesCrypto {\n    static async generateKey(algorithm, extractable, keyUsages) {\n        const key = new AesCryptoKey();\n        key.algorithm = algorithm;\n        key.extractable = extractable;\n        key.usages = keyUsages;\n        key.data = crypto__WEBPACK_IMPORTED_MODULE_2___default().randomBytes(algorithm.length >> 3);\n        return key;\n    }\n    static async exportKey(format, key) {\n        if (!(key instanceof AesCryptoKey)) {\n            throw new Error(\"key: Is not AesCryptoKey\");\n        }\n        switch (format.toLowerCase()) {\n            case \"jwk\":\n                return _peculiar_json_schema__WEBPACK_IMPORTED_MODULE_4__.JsonSerializer.toJSON(key);\n            case \"raw\":\n                return new Uint8Array(key.data).buffer;\n            default:\n                throw new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.OperationError(\"format: Must be 'jwk' or 'raw'\");\n        }\n    }\n    static async importKey(format, keyData, algorithm, extractable, keyUsages) {\n        let key;\n        switch (format.toLowerCase()) {\n            case \"jwk\":\n                key = _peculiar_json_schema__WEBPACK_IMPORTED_MODULE_4__.JsonParser.fromJSON(keyData, { targetSchema: AesCryptoKey });\n                break;\n            case \"raw\":\n                key = new AesCryptoKey();\n                key.data = buffer__WEBPACK_IMPORTED_MODULE_1__.Buffer.from(keyData);\n                break;\n            default:\n                throw new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.OperationError(\"format: Must be 'jwk' or 'raw'\");\n        }\n        key.algorithm = algorithm;\n        key.algorithm.length = key.data.length << 3;\n        key.extractable = extractable;\n        key.usages = keyUsages;\n        switch (key.algorithm.length) {\n            case 128:\n            case 192:\n            case 256:\n                break;\n            default:\n                throw new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.OperationError(\"keyData: Is wrong key length\");\n        }\n        return key;\n    }\n    static async encrypt(algorithm, key, data) {\n        switch (algorithm.name.toUpperCase()) {\n            case \"AES-CBC\":\n                return this.encryptAesCBC(algorithm, key, buffer__WEBPACK_IMPORTED_MODULE_1__.Buffer.from(data));\n            case \"AES-CTR\":\n                return this.encryptAesCTR(algorithm, key, buffer__WEBPACK_IMPORTED_MODULE_1__.Buffer.from(data));\n            case \"AES-GCM\":\n                return this.encryptAesGCM(algorithm, key, buffer__WEBPACK_IMPORTED_MODULE_1__.Buffer.from(data));\n            case \"AES-KW\":\n                return this.encryptAesKW(algorithm, key, buffer__WEBPACK_IMPORTED_MODULE_1__.Buffer.from(data));\n            case \"AES-ECB\":\n                return this.encryptAesECB(algorithm, key, buffer__WEBPACK_IMPORTED_MODULE_1__.Buffer.from(data));\n            default:\n                throw new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.OperationError(\"algorithm: Is not recognized\");\n        }\n    }\n    static async decrypt(algorithm, key, data) {\n        if (!(key instanceof AesCryptoKey)) {\n            throw new Error(\"key: Is not AesCryptoKey\");\n        }\n        switch (algorithm.name.toUpperCase()) {\n            case \"AES-CBC\":\n                return this.decryptAesCBC(algorithm, key, buffer__WEBPACK_IMPORTED_MODULE_1__.Buffer.from(data));\n            case \"AES-CTR\":\n                return this.decryptAesCTR(algorithm, key, buffer__WEBPACK_IMPORTED_MODULE_1__.Buffer.from(data));\n            case \"AES-GCM\":\n                return this.decryptAesGCM(algorithm, key, buffer__WEBPACK_IMPORTED_MODULE_1__.Buffer.from(data));\n            case \"AES-KW\":\n                return this.decryptAesKW(algorithm, key, buffer__WEBPACK_IMPORTED_MODULE_1__.Buffer.from(data));\n            case \"AES-ECB\":\n                return this.decryptAesECB(algorithm, key, buffer__WEBPACK_IMPORTED_MODULE_1__.Buffer.from(data));\n            default:\n                throw new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.OperationError(\"algorithm: Is not recognized\");\n        }\n    }\n    static async encryptAesCBC(algorithm, key, data) {\n        const cipher = crypto__WEBPACK_IMPORTED_MODULE_2___default().createCipheriv(`aes-${key.algorithm.length}-cbc`, key.data, new Uint8Array(algorithm.iv));\n        let enc = cipher.update(data);\n        enc = buffer__WEBPACK_IMPORTED_MODULE_1__.Buffer.concat([enc, cipher.final()]);\n        const res = new Uint8Array(enc).buffer;\n        return res;\n    }\n    static async decryptAesCBC(algorithm, key, data) {\n        const decipher = crypto__WEBPACK_IMPORTED_MODULE_2___default().createDecipheriv(`aes-${key.algorithm.length}-cbc`, key.data, new Uint8Array(algorithm.iv));\n        let dec = decipher.update(data);\n        dec = buffer__WEBPACK_IMPORTED_MODULE_1__.Buffer.concat([dec, decipher.final()]);\n        return new Uint8Array(dec).buffer;\n    }\n    static async encryptAesCTR(algorithm, key, data) {\n        const cipher = crypto__WEBPACK_IMPORTED_MODULE_2___default().createCipheriv(`aes-${key.algorithm.length}-ctr`, key.data, buffer__WEBPACK_IMPORTED_MODULE_1__.Buffer.from(algorithm.counter));\n        let enc = cipher.update(data);\n        enc = buffer__WEBPACK_IMPORTED_MODULE_1__.Buffer.concat([enc, cipher.final()]);\n        const res = new Uint8Array(enc).buffer;\n        return res;\n    }\n    static async decryptAesCTR(algorithm, key, data) {\n        const decipher = crypto__WEBPACK_IMPORTED_MODULE_2___default().createDecipheriv(`aes-${key.algorithm.length}-ctr`, key.data, new Uint8Array(algorithm.counter));\n        let dec = decipher.update(data);\n        dec = buffer__WEBPACK_IMPORTED_MODULE_1__.Buffer.concat([dec, decipher.final()]);\n        return new Uint8Array(dec).buffer;\n    }\n    static async encryptAesGCM(algorithm, key, data) {\n        const cipher = crypto__WEBPACK_IMPORTED_MODULE_2___default().createCipheriv(`aes-${key.algorithm.length}-gcm`, key.data, buffer__WEBPACK_IMPORTED_MODULE_1__.Buffer.from(algorithm.iv), {\n            authTagLength: (algorithm.tagLength || 128) >> 3,\n        });\n        if (algorithm.additionalData) {\n            cipher.setAAD(buffer__WEBPACK_IMPORTED_MODULE_1__.Buffer.from(algorithm.additionalData));\n        }\n        let enc = cipher.update(data);\n        enc = buffer__WEBPACK_IMPORTED_MODULE_1__.Buffer.concat([enc, cipher.final(), cipher.getAuthTag()]);\n        const res = new Uint8Array(enc).buffer;\n        return res;\n    }\n    static async decryptAesGCM(algorithm, key, data) {\n        const tagLength = (algorithm.tagLength || 128) >> 3;\n        const decipher = crypto__WEBPACK_IMPORTED_MODULE_2___default().createDecipheriv(`aes-${key.algorithm.length}-gcm`, key.data, new Uint8Array(algorithm.iv), {\n            authTagLength: tagLength,\n        });\n        const enc = data.slice(0, data.length - tagLength);\n        const tag = data.slice(data.length - tagLength);\n        if (algorithm.additionalData) {\n            decipher.setAAD(buffer__WEBPACK_IMPORTED_MODULE_1__.Buffer.from(algorithm.additionalData));\n        }\n        decipher.setAuthTag(tag);\n        let dec = decipher.update(enc);\n        dec = buffer__WEBPACK_IMPORTED_MODULE_1__.Buffer.concat([dec, decipher.final()]);\n        return new Uint8Array(dec).buffer;\n    }\n    static async encryptAesKW(algorithm, key, data) {\n        const cipher = crypto__WEBPACK_IMPORTED_MODULE_2___default().createCipheriv(`id-aes${key.algorithm.length}-wrap`, key.data, this.AES_KW_IV);\n        let enc = cipher.update(data);\n        enc = buffer__WEBPACK_IMPORTED_MODULE_1__.Buffer.concat([enc, cipher.final()]);\n        return new Uint8Array(enc).buffer;\n    }\n    static async decryptAesKW(algorithm, key, data) {\n        const decipher = crypto__WEBPACK_IMPORTED_MODULE_2___default().createDecipheriv(`id-aes${key.algorithm.length}-wrap`, key.data, this.AES_KW_IV);\n        let dec = decipher.update(data);\n        dec = buffer__WEBPACK_IMPORTED_MODULE_1__.Buffer.concat([dec, decipher.final()]);\n        return new Uint8Array(dec).buffer;\n    }\n    static async encryptAesECB(algorithm, key, data) {\n        const cipher = crypto__WEBPACK_IMPORTED_MODULE_2___default().createCipheriv(`aes-${key.algorithm.length}-ecb`, key.data, new Uint8Array(0));\n        let enc = cipher.update(data);\n        enc = buffer__WEBPACK_IMPORTED_MODULE_1__.Buffer.concat([enc, cipher.final()]);\n        const res = new Uint8Array(enc).buffer;\n        return res;\n    }\n    static async decryptAesECB(algorithm, key, data) {\n        const decipher = crypto__WEBPACK_IMPORTED_MODULE_2___default().createDecipheriv(`aes-${key.algorithm.length}-ecb`, key.data, new Uint8Array(0));\n        let dec = decipher.update(data);\n        dec = buffer__WEBPACK_IMPORTED_MODULE_1__.Buffer.concat([dec, decipher.final()]);\n        return new Uint8Array(dec).buffer;\n    }\n}\nAesCrypto.AES_KW_IV = buffer__WEBPACK_IMPORTED_MODULE_1__.Buffer.from(\"A6A6A6A6A6A6A6A6\", \"hex\");\n\nconst keyStorage = new WeakMap();\nfunction getCryptoKey(key) {\n    const res = keyStorage.get(key);\n    if (!res) {\n        throw new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.OperationError(\"Cannot get CryptoKey from secure storage\");\n    }\n    return res;\n}\nfunction setCryptoKey(value) {\n    const key = webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.CryptoKey.create(value.algorithm, value.type, value.extractable, value.usages);\n    Object.freeze(key);\n    keyStorage.set(key, value);\n    return key;\n}\n\nclass AesCbcProvider extends webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.AesCbcProvider {\n    async onGenerateKey(algorithm, extractable, keyUsages) {\n        const key = await AesCrypto.generateKey({\n            name: this.name,\n            length: algorithm.length,\n        }, extractable, keyUsages);\n        return setCryptoKey(key);\n    }\n    async onEncrypt(algorithm, key, data) {\n        return AesCrypto.encrypt(algorithm, getCryptoKey(key), new Uint8Array(data));\n    }\n    async onDecrypt(algorithm, key, data) {\n        return AesCrypto.decrypt(algorithm, getCryptoKey(key), new Uint8Array(data));\n    }\n    async onExportKey(format, key) {\n        return AesCrypto.exportKey(format, getCryptoKey(key));\n    }\n    async onImportKey(format, keyData, algorithm, extractable, keyUsages) {\n        const key = await AesCrypto.importKey(format, keyData, { name: algorithm.name }, extractable, keyUsages);\n        return setCryptoKey(key);\n    }\n    checkCryptoKey(key, keyUsage) {\n        super.checkCryptoKey(key, keyUsage);\n        if (!(getCryptoKey(key) instanceof AesCryptoKey)) {\n            throw new TypeError(\"key: Is not a AesCryptoKey\");\n        }\n    }\n}\n\nconst zero = buffer__WEBPACK_IMPORTED_MODULE_1__.Buffer.from([0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]);\nconst rb = buffer__WEBPACK_IMPORTED_MODULE_1__.Buffer.from([0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 135]);\nconst blockSize = 16;\nfunction bitShiftLeft(buffer) {\n    const shifted = buffer__WEBPACK_IMPORTED_MODULE_1__.Buffer.alloc(buffer.length);\n    const last = buffer.length - 1;\n    for (let index = 0; index < last; index++) {\n        shifted[index] = buffer[index] << 1;\n        if (buffer[index + 1] & 0x80) {\n            shifted[index] += 0x01;\n        }\n    }\n    shifted[last] = buffer[last] << 1;\n    return shifted;\n}\nfunction xor(a, b) {\n    const length = Math.min(a.length, b.length);\n    const output = buffer__WEBPACK_IMPORTED_MODULE_1__.Buffer.alloc(length);\n    for (let index = 0; index < length; index++) {\n        output[index] = a[index] ^ b[index];\n    }\n    return output;\n}\nfunction aes(key, message) {\n    const cipher = crypto__WEBPACK_IMPORTED_MODULE_2___default().createCipheriv(`aes${key.length << 3}`, key, zero);\n    const result = cipher.update(message);\n    cipher.final();\n    return result;\n}\nfunction getMessageBlock(message, blockIndex) {\n    const block = buffer__WEBPACK_IMPORTED_MODULE_1__.Buffer.alloc(blockSize);\n    const start = blockIndex * blockSize;\n    const end = start + blockSize;\n    message.copy(block, 0, start, end);\n    return block;\n}\nfunction getPaddedMessageBlock(message, blockIndex) {\n    const block = buffer__WEBPACK_IMPORTED_MODULE_1__.Buffer.alloc(blockSize);\n    const start = blockIndex * blockSize;\n    const end = message.length;\n    block.fill(0);\n    message.copy(block, 0, start, end);\n    block[end - start] = 0x80;\n    return block;\n}\nfunction generateSubkeys(key) {\n    const l = aes(key, zero);\n    let subkey1 = bitShiftLeft(l);\n    if (l[0] & 0x80) {\n        subkey1 = xor(subkey1, rb);\n    }\n    let subkey2 = bitShiftLeft(subkey1);\n    if (subkey1[0] & 0x80) {\n        subkey2 = xor(subkey2, rb);\n    }\n    return { subkey1, subkey2 };\n}\nfunction aesCmac(key, message) {\n    const subkeys = generateSubkeys(key);\n    let blockCount = Math.ceil(message.length / blockSize);\n    let lastBlockCompleteFlag;\n    let lastBlock;\n    if (blockCount === 0) {\n        blockCount = 1;\n        lastBlockCompleteFlag = false;\n    }\n    else {\n        lastBlockCompleteFlag = (message.length % blockSize === 0);\n    }\n    const lastBlockIndex = blockCount - 1;\n    if (lastBlockCompleteFlag) {\n        lastBlock = xor(getMessageBlock(message, lastBlockIndex), subkeys.subkey1);\n    }\n    else {\n        lastBlock = xor(getPaddedMessageBlock(message, lastBlockIndex), subkeys.subkey2);\n    }\n    let x = zero;\n    let y;\n    for (let index = 0; index < lastBlockIndex; index++) {\n        y = xor(x, getMessageBlock(message, index));\n        x = aes(key, y);\n    }\n    y = xor(lastBlock, x);\n    return aes(key, y);\n}\nclass AesCmacProvider extends webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.AesCmacProvider {\n    async onGenerateKey(algorithm, extractable, keyUsages) {\n        const key = await AesCrypto.generateKey({\n            name: this.name,\n            length: algorithm.length,\n        }, extractable, keyUsages);\n        return setCryptoKey(key);\n    }\n    async onSign(algorithm, key, data) {\n        const result = aesCmac(getCryptoKey(key).data, buffer__WEBPACK_IMPORTED_MODULE_1__.Buffer.from(data));\n        return new Uint8Array(result).buffer;\n    }\n    async onVerify(algorithm, key, signature, data) {\n        const signature2 = await this.sign(algorithm, key, data);\n        return buffer__WEBPACK_IMPORTED_MODULE_1__.Buffer.from(signature).compare(buffer__WEBPACK_IMPORTED_MODULE_1__.Buffer.from(signature2)) === 0;\n    }\n    async onExportKey(format, key) {\n        return AesCrypto.exportKey(format, getCryptoKey(key));\n    }\n    async onImportKey(format, keyData, algorithm, extractable, keyUsages) {\n        const res = await AesCrypto.importKey(format, keyData, { name: algorithm.name }, extractable, keyUsages);\n        return setCryptoKey(res);\n    }\n    checkCryptoKey(key, keyUsage) {\n        super.checkCryptoKey(key, keyUsage);\n        if (!(getCryptoKey(key) instanceof AesCryptoKey)) {\n            throw new TypeError(\"key: Is not a AesCryptoKey\");\n        }\n    }\n}\n\nclass AesCtrProvider extends webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.AesCtrProvider {\n    async onGenerateKey(algorithm, extractable, keyUsages) {\n        const key = await AesCrypto.generateKey({\n            name: this.name,\n            length: algorithm.length,\n        }, extractable, keyUsages);\n        return setCryptoKey(key);\n    }\n    async onEncrypt(algorithm, key, data) {\n        return AesCrypto.encrypt(algorithm, getCryptoKey(key), new Uint8Array(data));\n    }\n    async onDecrypt(algorithm, key, data) {\n        return AesCrypto.decrypt(algorithm, getCryptoKey(key), new Uint8Array(data));\n    }\n    async onExportKey(format, key) {\n        return AesCrypto.exportKey(format, getCryptoKey(key));\n    }\n    async onImportKey(format, keyData, algorithm, extractable, keyUsages) {\n        const res = await AesCrypto.importKey(format, keyData, { name: algorithm.name }, extractable, keyUsages);\n        return setCryptoKey(res);\n    }\n    checkCryptoKey(key, keyUsage) {\n        super.checkCryptoKey(key, keyUsage);\n        if (!(getCryptoKey(key) instanceof AesCryptoKey)) {\n            throw new TypeError(\"key: Is not a AesCryptoKey\");\n        }\n    }\n}\n\nclass AesGcmProvider extends webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.AesGcmProvider {\n    async onGenerateKey(algorithm, extractable, keyUsages) {\n        const key = await AesCrypto.generateKey({\n            name: this.name,\n            length: algorithm.length,\n        }, extractable, keyUsages);\n        return setCryptoKey(key);\n    }\n    async onEncrypt(algorithm, key, data) {\n        return AesCrypto.encrypt(algorithm, getCryptoKey(key), new Uint8Array(data));\n    }\n    async onDecrypt(algorithm, key, data) {\n        return AesCrypto.decrypt(algorithm, getCryptoKey(key), new Uint8Array(data));\n    }\n    async onExportKey(format, key) {\n        return AesCrypto.exportKey(format, getCryptoKey(key));\n    }\n    async onImportKey(format, keyData, algorithm, extractable, keyUsages) {\n        const res = await AesCrypto.importKey(format, keyData, { name: algorithm.name }, extractable, keyUsages);\n        return setCryptoKey(res);\n    }\n    checkCryptoKey(key, keyUsage) {\n        super.checkCryptoKey(key, keyUsage);\n        if (!(getCryptoKey(key) instanceof AesCryptoKey)) {\n            throw new TypeError(\"key: Is not a AesCryptoKey\");\n        }\n    }\n}\n\nclass AesKwProvider extends webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.AesKwProvider {\n    async onGenerateKey(algorithm, extractable, keyUsages) {\n        const res = await AesCrypto.generateKey({\n            name: this.name,\n            length: algorithm.length,\n        }, extractable, keyUsages);\n        return setCryptoKey(res);\n    }\n    async onExportKey(format, key) {\n        return AesCrypto.exportKey(format, getCryptoKey(key));\n    }\n    async onImportKey(format, keyData, algorithm, extractable, keyUsages) {\n        const res = await AesCrypto.importKey(format, keyData, { name: algorithm.name }, extractable, keyUsages);\n        return setCryptoKey(res);\n    }\n    async onEncrypt(algorithm, key, data) {\n        return AesCrypto.encrypt(algorithm, getCryptoKey(key), new Uint8Array(data));\n    }\n    async onDecrypt(algorithm, key, data) {\n        return AesCrypto.decrypt(algorithm, getCryptoKey(key), new Uint8Array(data));\n    }\n    checkCryptoKey(key, keyUsage) {\n        super.checkCryptoKey(key, keyUsage);\n        if (!(getCryptoKey(key) instanceof AesCryptoKey)) {\n            throw new TypeError(\"key: Is not a AesCryptoKey\");\n        }\n    }\n}\n\nclass AesEcbProvider extends webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.AesEcbProvider {\n    async onGenerateKey(algorithm, extractable, keyUsages) {\n        const key = await AesCrypto.generateKey({\n            name: this.name,\n            length: algorithm.length,\n        }, extractable, keyUsages);\n        return setCryptoKey(key);\n    }\n    async onEncrypt(algorithm, key, data) {\n        return AesCrypto.encrypt(algorithm, getCryptoKey(key), new Uint8Array(data));\n    }\n    async onDecrypt(algorithm, key, data) {\n        return AesCrypto.decrypt(algorithm, getCryptoKey(key), new Uint8Array(data));\n    }\n    async onExportKey(format, key) {\n        return AesCrypto.exportKey(format, getCryptoKey(key));\n    }\n    async onImportKey(format, keyData, algorithm, extractable, keyUsages) {\n        const res = await AesCrypto.importKey(format, keyData, { name: algorithm.name }, extractable, keyUsages);\n        return setCryptoKey(res);\n    }\n    checkCryptoKey(key, keyUsage) {\n        super.checkCryptoKey(key, keyUsage);\n        if (!(getCryptoKey(key) instanceof AesCryptoKey)) {\n            throw new TypeError(\"key: Is not a AesCryptoKey\");\n        }\n    }\n}\n\nclass DesCryptoKey extends SymmetricKey {\n    get alg() {\n        switch (this.algorithm.name.toUpperCase()) {\n            case \"DES-CBC\":\n                return `DES-CBC`;\n            case \"DES-EDE3-CBC\":\n                return `3DES-CBC`;\n            default:\n                throw new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.AlgorithmError(\"Unsupported algorithm name\");\n        }\n    }\n    set alg(value) {\n    }\n}\n(0,tslib__WEBPACK_IMPORTED_MODULE_7__.__decorate)([\n    (0,_peculiar_json_schema__WEBPACK_IMPORTED_MODULE_4__.JsonProp)({ name: \"k\", converter: JsonBase64UrlConverter })\n], DesCryptoKey.prototype, \"data\", void 0);\n\nclass DesCrypto {\n    static async generateKey(algorithm, extractable, keyUsages) {\n        const key = new DesCryptoKey();\n        key.algorithm = algorithm;\n        key.extractable = extractable;\n        key.usages = keyUsages;\n        key.data = crypto__WEBPACK_IMPORTED_MODULE_2___default().randomBytes(algorithm.length >> 3);\n        return key;\n    }\n    static async exportKey(format, key) {\n        switch (format.toLowerCase()) {\n            case \"jwk\":\n                return _peculiar_json_schema__WEBPACK_IMPORTED_MODULE_4__.JsonSerializer.toJSON(key);\n            case \"raw\":\n                return new Uint8Array(key.data).buffer;\n            default:\n                throw new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.OperationError(\"format: Must be 'jwk' or 'raw'\");\n        }\n    }\n    static async importKey(format, keyData, algorithm, extractable, keyUsages) {\n        let key;\n        switch (format.toLowerCase()) {\n            case \"jwk\":\n                key = _peculiar_json_schema__WEBPACK_IMPORTED_MODULE_4__.JsonParser.fromJSON(keyData, { targetSchema: DesCryptoKey });\n                break;\n            case \"raw\":\n                key = new DesCryptoKey();\n                key.data = buffer__WEBPACK_IMPORTED_MODULE_1__.Buffer.from(keyData);\n                break;\n            default:\n                throw new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.OperationError(\"format: Must be 'jwk' or 'raw'\");\n        }\n        key.algorithm = algorithm;\n        key.extractable = extractable;\n        key.usages = keyUsages;\n        return key;\n    }\n    static async encrypt(algorithm, key, data) {\n        switch (algorithm.name.toUpperCase()) {\n            case \"DES-CBC\":\n                return this.encryptDesCBC(algorithm, key, buffer__WEBPACK_IMPORTED_MODULE_1__.Buffer.from(data));\n            case \"DES-EDE3-CBC\":\n                return this.encryptDesEDE3CBC(algorithm, key, buffer__WEBPACK_IMPORTED_MODULE_1__.Buffer.from(data));\n            default:\n                throw new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.OperationError(\"algorithm: Is not recognized\");\n        }\n    }\n    static async decrypt(algorithm, key, data) {\n        if (!(key instanceof DesCryptoKey)) {\n            throw new Error(\"key: Is not DesCryptoKey\");\n        }\n        switch (algorithm.name.toUpperCase()) {\n            case \"DES-CBC\":\n                return this.decryptDesCBC(algorithm, key, buffer__WEBPACK_IMPORTED_MODULE_1__.Buffer.from(data));\n            case \"DES-EDE3-CBC\":\n                return this.decryptDesEDE3CBC(algorithm, key, buffer__WEBPACK_IMPORTED_MODULE_1__.Buffer.from(data));\n            default:\n                throw new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.OperationError(\"algorithm: Is not recognized\");\n        }\n    }\n    static async encryptDesCBC(algorithm, key, data) {\n        const cipher = crypto__WEBPACK_IMPORTED_MODULE_2___default().createCipheriv(`des-cbc`, key.data, new Uint8Array(algorithm.iv));\n        let enc = cipher.update(data);\n        enc = buffer__WEBPACK_IMPORTED_MODULE_1__.Buffer.concat([enc, cipher.final()]);\n        const res = new Uint8Array(enc).buffer;\n        return res;\n    }\n    static async decryptDesCBC(algorithm, key, data) {\n        const decipher = crypto__WEBPACK_IMPORTED_MODULE_2___default().createDecipheriv(`des-cbc`, key.data, new Uint8Array(algorithm.iv));\n        let dec = decipher.update(data);\n        dec = buffer__WEBPACK_IMPORTED_MODULE_1__.Buffer.concat([dec, decipher.final()]);\n        return new Uint8Array(dec).buffer;\n    }\n    static async encryptDesEDE3CBC(algorithm, key, data) {\n        const cipher = crypto__WEBPACK_IMPORTED_MODULE_2___default().createCipheriv(`des-ede3-cbc`, key.data, buffer__WEBPACK_IMPORTED_MODULE_1__.Buffer.from(algorithm.iv));\n        let enc = cipher.update(data);\n        enc = buffer__WEBPACK_IMPORTED_MODULE_1__.Buffer.concat([enc, cipher.final()]);\n        const res = new Uint8Array(enc).buffer;\n        return res;\n    }\n    static async decryptDesEDE3CBC(algorithm, key, data) {\n        const decipher = crypto__WEBPACK_IMPORTED_MODULE_2___default().createDecipheriv(`des-ede3-cbc`, key.data, new Uint8Array(algorithm.iv));\n        let dec = decipher.update(data);\n        dec = buffer__WEBPACK_IMPORTED_MODULE_1__.Buffer.concat([dec, decipher.final()]);\n        return new Uint8Array(dec).buffer;\n    }\n}\n\nclass DesCbcProvider extends webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.DesProvider {\n    constructor() {\n        super(...arguments);\n        this.keySizeBits = 64;\n        this.ivSize = 8;\n        this.name = \"DES-CBC\";\n    }\n    async onGenerateKey(algorithm, extractable, keyUsages) {\n        const key = await DesCrypto.generateKey({\n            name: this.name,\n            length: this.keySizeBits,\n        }, extractable, keyUsages);\n        return setCryptoKey(key);\n    }\n    async onEncrypt(algorithm, key, data) {\n        return DesCrypto.encrypt(algorithm, getCryptoKey(key), new Uint8Array(data));\n    }\n    async onDecrypt(algorithm, key, data) {\n        return DesCrypto.decrypt(algorithm, getCryptoKey(key), new Uint8Array(data));\n    }\n    async onExportKey(format, key) {\n        return DesCrypto.exportKey(format, getCryptoKey(key));\n    }\n    async onImportKey(format, keyData, algorithm, extractable, keyUsages) {\n        const key = await DesCrypto.importKey(format, keyData, { name: this.name, length: this.keySizeBits }, extractable, keyUsages);\n        if (key.data.length !== (this.keySizeBits >> 3)) {\n            throw new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.OperationError(\"keyData: Wrong key size\");\n        }\n        return setCryptoKey(key);\n    }\n    checkCryptoKey(key, keyUsage) {\n        super.checkCryptoKey(key, keyUsage);\n        if (!(getCryptoKey(key) instanceof DesCryptoKey)) {\n            throw new TypeError(\"key: Is not a DesCryptoKey\");\n        }\n    }\n}\n\nclass DesEde3CbcProvider extends webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.DesProvider {\n    constructor() {\n        super(...arguments);\n        this.keySizeBits = 192;\n        this.ivSize = 8;\n        this.name = \"DES-EDE3-CBC\";\n    }\n    async onGenerateKey(algorithm, extractable, keyUsages) {\n        const key = await DesCrypto.generateKey({\n            name: this.name,\n            length: this.keySizeBits,\n        }, extractable, keyUsages);\n        return setCryptoKey(key);\n    }\n    async onEncrypt(algorithm, key, data) {\n        return DesCrypto.encrypt(algorithm, getCryptoKey(key), new Uint8Array(data));\n    }\n    async onDecrypt(algorithm, key, data) {\n        return DesCrypto.decrypt(algorithm, getCryptoKey(key), new Uint8Array(data));\n    }\n    async onExportKey(format, key) {\n        return DesCrypto.exportKey(format, getCryptoKey(key));\n    }\n    async onImportKey(format, keyData, algorithm, extractable, keyUsages) {\n        const key = await DesCrypto.importKey(format, keyData, { name: this.name, length: this.keySizeBits }, extractable, keyUsages);\n        if (key.data.length !== (this.keySizeBits >> 3)) {\n            throw new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.OperationError(\"keyData: Wrong key size\");\n        }\n        return setCryptoKey(key);\n    }\n    checkCryptoKey(key, keyUsage) {\n        super.checkCryptoKey(key, keyUsage);\n        if (!(getCryptoKey(key) instanceof DesCryptoKey)) {\n            throw new TypeError(\"key: Is not a DesCryptoKey\");\n        }\n    }\n}\n\nfunction getJwkAlgorithm(algorithm) {\n    switch (algorithm.name.toUpperCase()) {\n        case \"RSA-OAEP\": {\n            const mdSize = /(\\d+)$/.exec(algorithm.hash.name)[1];\n            return `RSA-OAEP${mdSize !== \"1\" ? `-${mdSize}` : \"\"}`;\n        }\n        case \"RSASSA-PKCS1-V1_5\":\n            return `RS${/(\\d+)$/.exec(algorithm.hash.name)[1]}`;\n        case \"RSA-PSS\":\n            return `PS${/(\\d+)$/.exec(algorithm.hash.name)[1]}`;\n        case \"RSA-PKCS1\":\n            return `RS1`;\n        default:\n            throw new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.OperationError(\"algorithm: Is not recognized\");\n    }\n}\n\nclass RsaPrivateKey extends AsymmetricKey {\n    constructor() {\n        super(...arguments);\n        this.type = \"private\";\n    }\n    getKey() {\n        const keyInfo = _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_6__.AsnParser.parse(this.data, webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.PrivateKeyInfo);\n        return _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_6__.AsnParser.parse(keyInfo.privateKey, webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.RsaPrivateKey);\n    }\n    toJSON() {\n        const key = this.getKey();\n        const json = {\n            kty: \"RSA\",\n            alg: getJwkAlgorithm(this.algorithm),\n            key_ops: this.usages,\n            ext: this.extractable,\n        };\n        return Object.assign(json, _peculiar_json_schema__WEBPACK_IMPORTED_MODULE_4__.JsonSerializer.toJSON(key));\n    }\n    fromJSON(json) {\n        const key = _peculiar_json_schema__WEBPACK_IMPORTED_MODULE_4__.JsonParser.fromJSON(json, { targetSchema: webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.RsaPrivateKey });\n        const keyInfo = new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.PrivateKeyInfo();\n        keyInfo.privateKeyAlgorithm.algorithm = \"1.2.840.113549.1.1.1\";\n        keyInfo.privateKeyAlgorithm.parameters = null;\n        keyInfo.privateKey = _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_6__.AsnSerializer.serialize(key);\n        this.data = buffer__WEBPACK_IMPORTED_MODULE_1__.Buffer.from(_peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_6__.AsnSerializer.serialize(keyInfo));\n    }\n}\n\nclass RsaPublicKey extends AsymmetricKey {\n    constructor() {\n        super(...arguments);\n        this.type = \"public\";\n    }\n    getKey() {\n        const keyInfo = _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_6__.AsnParser.parse(this.data, webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.PublicKeyInfo);\n        return _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_6__.AsnParser.parse(keyInfo.publicKey, webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.RsaPublicKey);\n    }\n    toJSON() {\n        const key = this.getKey();\n        const json = {\n            kty: \"RSA\",\n            alg: getJwkAlgorithm(this.algorithm),\n            key_ops: this.usages,\n            ext: this.extractable,\n        };\n        return Object.assign(json, _peculiar_json_schema__WEBPACK_IMPORTED_MODULE_4__.JsonSerializer.toJSON(key));\n    }\n    fromJSON(json) {\n        const key = _peculiar_json_schema__WEBPACK_IMPORTED_MODULE_4__.JsonParser.fromJSON(json, { targetSchema: webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.RsaPublicKey });\n        const keyInfo = new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.PublicKeyInfo();\n        keyInfo.publicKeyAlgorithm.algorithm = \"1.2.840.113549.1.1.1\";\n        keyInfo.publicKeyAlgorithm.parameters = null;\n        keyInfo.publicKey = _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_6__.AsnSerializer.serialize(key);\n        this.data = buffer__WEBPACK_IMPORTED_MODULE_1__.Buffer.from(_peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_6__.AsnSerializer.serialize(keyInfo));\n    }\n}\n\nclass RsaCrypto {\n    static async generateKey(algorithm, extractable, keyUsages) {\n        const privateKey = new RsaPrivateKey();\n        privateKey.algorithm = algorithm;\n        privateKey.extractable = extractable;\n        privateKey.usages = keyUsages.filter((usage) => this.privateKeyUsages.indexOf(usage) !== -1);\n        const publicKey = new RsaPublicKey();\n        publicKey.algorithm = algorithm;\n        publicKey.extractable = true;\n        publicKey.usages = keyUsages.filter((usage) => this.publicKeyUsages.indexOf(usage) !== -1);\n        const publicExponent = buffer__WEBPACK_IMPORTED_MODULE_1__.Buffer.concat([\n            buffer__WEBPACK_IMPORTED_MODULE_1__.Buffer.alloc(4 - algorithm.publicExponent.byteLength, 0),\n            buffer__WEBPACK_IMPORTED_MODULE_1__.Buffer.from(algorithm.publicExponent),\n        ]).readInt32BE(0);\n        const keys = crypto__WEBPACK_IMPORTED_MODULE_2___default().generateKeyPairSync(\"rsa\", {\n            modulusLength: algorithm.modulusLength,\n            publicExponent,\n            publicKeyEncoding: {\n                format: \"der\",\n                type: \"spki\",\n            },\n            privateKeyEncoding: {\n                format: \"der\",\n                type: \"pkcs8\",\n            },\n        });\n        privateKey.data = keys.privateKey;\n        publicKey.data = keys.publicKey;\n        const res = {\n            privateKey,\n            publicKey,\n        };\n        return res;\n    }\n    static async exportKey(format, key) {\n        switch (format.toLowerCase()) {\n            case \"jwk\":\n                return _peculiar_json_schema__WEBPACK_IMPORTED_MODULE_4__.JsonSerializer.toJSON(key);\n            case \"pkcs8\":\n            case \"spki\":\n                return new Uint8Array(key.data).buffer;\n            default:\n                throw new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.OperationError(\"format: Must be 'jwk', 'pkcs8' or 'spki'\");\n        }\n    }\n    static async importKey(format, keyData, algorithm, extractable, keyUsages) {\n        switch (format.toLowerCase()) {\n            case \"jwk\": {\n                const jwk = keyData;\n                if (jwk.d) {\n                    const asnKey = _peculiar_json_schema__WEBPACK_IMPORTED_MODULE_4__.JsonParser.fromJSON(keyData, { targetSchema: webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.RsaPrivateKey });\n                    return this.importPrivateKey(asnKey, algorithm, extractable, keyUsages);\n                }\n                else {\n                    const asnKey = _peculiar_json_schema__WEBPACK_IMPORTED_MODULE_4__.JsonParser.fromJSON(keyData, { targetSchema: webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.RsaPublicKey });\n                    return this.importPublicKey(asnKey, algorithm, extractable, keyUsages);\n                }\n            }\n            case \"spki\": {\n                const keyInfo = _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_6__.AsnParser.parse(new Uint8Array(keyData), webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.PublicKeyInfo);\n                const asnKey = _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_6__.AsnParser.parse(keyInfo.publicKey, webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.RsaPublicKey);\n                return this.importPublicKey(asnKey, algorithm, extractable, keyUsages);\n            }\n            case \"pkcs8\": {\n                const keyInfo = _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_6__.AsnParser.parse(new Uint8Array(keyData), webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.PrivateKeyInfo);\n                const asnKey = _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_6__.AsnParser.parse(keyInfo.privateKey, webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.RsaPrivateKey);\n                return this.importPrivateKey(asnKey, algorithm, extractable, keyUsages);\n            }\n            default:\n                throw new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.OperationError(\"format: Must be 'jwk', 'pkcs8' or 'spki'\");\n        }\n    }\n    static async sign(algorithm, key, data) {\n        switch (algorithm.name.toUpperCase()) {\n            case \"RSA-PSS\":\n            case \"RSASSA-PKCS1-V1_5\":\n                return this.signRsa(algorithm, key, data);\n            default:\n                throw new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.OperationError(\"algorithm: Is not recognized\");\n        }\n    }\n    static async verify(algorithm, key, signature, data) {\n        switch (algorithm.name.toUpperCase()) {\n            case \"RSA-PSS\":\n            case \"RSASSA-PKCS1-V1_5\":\n                return this.verifySSA(algorithm, key, data, signature);\n            default:\n                throw new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.OperationError(\"algorithm: Is not recognized\");\n        }\n    }\n    static async encrypt(algorithm, key, data) {\n        switch (algorithm.name.toUpperCase()) {\n            case \"RSA-OAEP\":\n                return this.encryptOAEP(algorithm, key, data);\n            default:\n                throw new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.OperationError(\"algorithm: Is not recognized\");\n        }\n    }\n    static async decrypt(algorithm, key, data) {\n        switch (algorithm.name.toUpperCase()) {\n            case \"RSA-OAEP\":\n                return this.decryptOAEP(algorithm, key, data);\n            default:\n                throw new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.OperationError(\"algorithm: Is not recognized\");\n        }\n    }\n    static importPrivateKey(asnKey, algorithm, extractable, keyUsages) {\n        const keyInfo = new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.PrivateKeyInfo();\n        keyInfo.privateKeyAlgorithm.algorithm = \"1.2.840.113549.1.1.1\";\n        keyInfo.privateKeyAlgorithm.parameters = null;\n        keyInfo.privateKey = _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_6__.AsnSerializer.serialize(asnKey);\n        const key = new RsaPrivateKey();\n        key.data = buffer__WEBPACK_IMPORTED_MODULE_1__.Buffer.from(_peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_6__.AsnSerializer.serialize(keyInfo));\n        key.algorithm = Object.assign({}, algorithm);\n        key.algorithm.publicExponent = new Uint8Array(asnKey.publicExponent);\n        key.algorithm.modulusLength = asnKey.modulus.byteLength << 3;\n        key.extractable = extractable;\n        key.usages = keyUsages;\n        return key;\n    }\n    static importPublicKey(asnKey, algorithm, extractable, keyUsages) {\n        const keyInfo = new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.PublicKeyInfo();\n        keyInfo.publicKeyAlgorithm.algorithm = \"1.2.840.113549.1.1.1\";\n        keyInfo.publicKeyAlgorithm.parameters = null;\n        keyInfo.publicKey = _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_6__.AsnSerializer.serialize(asnKey);\n        const key = new RsaPublicKey();\n        key.data = buffer__WEBPACK_IMPORTED_MODULE_1__.Buffer.from(_peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_6__.AsnSerializer.serialize(keyInfo));\n        key.algorithm = Object.assign({}, algorithm);\n        key.algorithm.publicExponent = new Uint8Array(asnKey.publicExponent);\n        key.algorithm.modulusLength = asnKey.modulus.byteLength << 3;\n        key.extractable = extractable;\n        key.usages = keyUsages;\n        return key;\n    }\n    static getCryptoAlgorithm(alg) {\n        switch (alg.hash.name.toUpperCase()) {\n            case \"SHA-1\":\n                return \"RSA-SHA1\";\n            case \"SHA-256\":\n                return \"RSA-SHA256\";\n            case \"SHA-384\":\n                return \"RSA-SHA384\";\n            case \"SHA-512\":\n                return \"RSA-SHA512\";\n            case \"SHA3-256\":\n                return \"RSA-SHA3-256\";\n            case \"SHA3-384\":\n                return \"RSA-SHA3-384\";\n            case \"SHA3-512\":\n                return \"RSA-SHA3-512\";\n            default:\n                throw new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.OperationError(\"algorithm.hash: Is not recognized\");\n        }\n    }\n    static signRsa(algorithm, key, data) {\n        const cryptoAlg = this.getCryptoAlgorithm(key.algorithm);\n        const signer = crypto__WEBPACK_IMPORTED_MODULE_2___default().createSign(cryptoAlg);\n        signer.update(buffer__WEBPACK_IMPORTED_MODULE_1__.Buffer.from(data));\n        if (!key.pem) {\n            key.pem = `-----BEGIN PRIVATE KEY-----\\n${key.data.toString(\"base64\")}\\n-----END PRIVATE KEY-----`;\n        }\n        const options = {\n            key: key.pem,\n        };\n        if (algorithm.name.toUpperCase() === \"RSA-PSS\") {\n            options.padding = (crypto__WEBPACK_IMPORTED_MODULE_2___default().constants).RSA_PKCS1_PSS_PADDING;\n            options.saltLength = algorithm.saltLength;\n        }\n        const signature = signer.sign(options);\n        return new Uint8Array(signature).buffer;\n    }\n    static verifySSA(algorithm, key, data, signature) {\n        const cryptoAlg = this.getCryptoAlgorithm(key.algorithm);\n        const signer = crypto__WEBPACK_IMPORTED_MODULE_2___default().createVerify(cryptoAlg);\n        signer.update(buffer__WEBPACK_IMPORTED_MODULE_1__.Buffer.from(data));\n        if (!key.pem) {\n            key.pem = `-----BEGIN PUBLIC KEY-----\\n${key.data.toString(\"base64\")}\\n-----END PUBLIC KEY-----`;\n        }\n        const options = {\n            key: key.pem,\n        };\n        if (algorithm.name.toUpperCase() === \"RSA-PSS\") {\n            options.padding = (crypto__WEBPACK_IMPORTED_MODULE_2___default().constants).RSA_PKCS1_PSS_PADDING;\n            options.saltLength = algorithm.saltLength;\n        }\n        const ok = signer.verify(options, signature);\n        return ok;\n    }\n    static encryptOAEP(algorithm, key, data) {\n        const options = {\n            key: `-----BEGIN PUBLIC KEY-----\\n${key.data.toString(\"base64\")}\\n-----END PUBLIC KEY-----`,\n            padding: (crypto__WEBPACK_IMPORTED_MODULE_2___default().constants).RSA_PKCS1_OAEP_PADDING,\n        };\n        if (algorithm.label) ;\n        return new Uint8Array(crypto__WEBPACK_IMPORTED_MODULE_2___default().publicEncrypt(options, data)).buffer;\n    }\n    static decryptOAEP(algorithm, key, data) {\n        const options = {\n            key: `-----BEGIN PRIVATE KEY-----\\n${key.data.toString(\"base64\")}\\n-----END PRIVATE KEY-----`,\n            padding: (crypto__WEBPACK_IMPORTED_MODULE_2___default().constants).RSA_PKCS1_OAEP_PADDING,\n        };\n        if (algorithm.label) ;\n        return new Uint8Array(crypto__WEBPACK_IMPORTED_MODULE_2___default().privateDecrypt(options, data)).buffer;\n    }\n}\nRsaCrypto.publicKeyUsages = [\"verify\", \"encrypt\", \"wrapKey\"];\nRsaCrypto.privateKeyUsages = [\"sign\", \"decrypt\", \"unwrapKey\"];\n\nclass RsaSsaProvider extends webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.RsaSsaProvider {\n    constructor() {\n        super(...arguments);\n        this.hashAlgorithms = [\n            \"SHA-1\", \"SHA-256\", \"SHA-384\", \"SHA-512\",\n            \"shake128\", \"shake256\",\n            \"SHA3-256\", \"SHA3-384\", \"SHA3-512\"\n        ];\n    }\n    async onGenerateKey(algorithm, extractable, keyUsages) {\n        const keys = await RsaCrypto.generateKey({\n            ...algorithm,\n            name: this.name,\n        }, extractable, keyUsages);\n        return {\n            privateKey: setCryptoKey(keys.privateKey),\n            publicKey: setCryptoKey(keys.publicKey),\n        };\n    }\n    async onSign(algorithm, key, data) {\n        return RsaCrypto.sign(algorithm, getCryptoKey(key), new Uint8Array(data));\n    }\n    async onVerify(algorithm, key, signature, data) {\n        return RsaCrypto.verify(algorithm, getCryptoKey(key), new Uint8Array(signature), new Uint8Array(data));\n    }\n    async onExportKey(format, key) {\n        return RsaCrypto.exportKey(format, getCryptoKey(key));\n    }\n    async onImportKey(format, keyData, algorithm, extractable, keyUsages) {\n        const key = await RsaCrypto.importKey(format, keyData, { ...algorithm, name: this.name }, extractable, keyUsages);\n        return setCryptoKey(key);\n    }\n    checkCryptoKey(key, keyUsage) {\n        super.checkCryptoKey(key, keyUsage);\n        const internalKey = getCryptoKey(key);\n        if (!(internalKey instanceof RsaPrivateKey || internalKey instanceof RsaPublicKey)) {\n            throw new TypeError(\"key: Is not RSA CryptoKey\");\n        }\n    }\n}\n\nclass RsaPssProvider extends webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.RsaPssProvider {\n    constructor() {\n        super(...arguments);\n        this.hashAlgorithms = [\n            \"SHA-1\", \"SHA-256\", \"SHA-384\", \"SHA-512\",\n            \"shake128\", \"shake256\",\n            \"SHA3-256\", \"SHA3-384\", \"SHA3-512\"\n        ];\n    }\n    async onGenerateKey(algorithm, extractable, keyUsages) {\n        const keys = await RsaCrypto.generateKey({\n            ...algorithm,\n            name: this.name,\n        }, extractable, keyUsages);\n        return {\n            privateKey: setCryptoKey(keys.privateKey),\n            publicKey: setCryptoKey(keys.publicKey),\n        };\n    }\n    async onSign(algorithm, key, data) {\n        return RsaCrypto.sign(algorithm, getCryptoKey(key), new Uint8Array(data));\n    }\n    async onVerify(algorithm, key, signature, data) {\n        return RsaCrypto.verify(algorithm, getCryptoKey(key), new Uint8Array(signature), new Uint8Array(data));\n    }\n    async onExportKey(format, key) {\n        return RsaCrypto.exportKey(format, getCryptoKey(key));\n    }\n    async onImportKey(format, keyData, algorithm, extractable, keyUsages) {\n        const key = await RsaCrypto.importKey(format, keyData, { ...algorithm, name: this.name }, extractable, keyUsages);\n        return setCryptoKey(key);\n    }\n    checkCryptoKey(key, keyUsage) {\n        super.checkCryptoKey(key, keyUsage);\n        const internalKey = getCryptoKey(key);\n        if (!(internalKey instanceof RsaPrivateKey || internalKey instanceof RsaPublicKey)) {\n            throw new TypeError(\"key: Is not RSA CryptoKey\");\n        }\n    }\n}\n\nclass ShaCrypto {\n    static size(algorithm) {\n        switch (algorithm.name.toUpperCase()) {\n            case \"SHA-1\":\n                return 160;\n            case \"SHA-256\":\n            case \"SHA3-256\":\n                return 256;\n            case \"SHA-384\":\n            case \"SHA3-384\":\n                return 384;\n            case \"SHA-512\":\n            case \"SHA3-512\":\n                return 512;\n            default:\n                throw new Error(\"Unrecognized name\");\n        }\n    }\n    static getAlgorithmName(algorithm) {\n        switch (algorithm.name.toUpperCase()) {\n            case \"SHA-1\":\n                return \"sha1\";\n            case \"SHA-256\":\n                return \"sha256\";\n            case \"SHA-384\":\n                return \"sha384\";\n            case \"SHA-512\":\n                return \"sha512\";\n            case \"SHA3-256\":\n                return \"sha3-256\";\n            case \"SHA3-384\":\n                return \"sha3-384\";\n            case \"SHA3-512\":\n                return \"sha3-512\";\n            default:\n                throw new Error(\"Unrecognized name\");\n        }\n    }\n    static digest(algorithm, data) {\n        const hashAlg = this.getAlgorithmName(algorithm);\n        const hash = crypto__WEBPACK_IMPORTED_MODULE_2___default().createHash(hashAlg)\n            .update(buffer__WEBPACK_IMPORTED_MODULE_1__.Buffer.from(data)).digest();\n        return new Uint8Array(hash).buffer;\n    }\n}\n\nclass RsaOaepProvider extends webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.RsaOaepProvider {\n    async onGenerateKey(algorithm, extractable, keyUsages) {\n        const keys = await RsaCrypto.generateKey({\n            ...algorithm,\n            name: this.name,\n        }, extractable, keyUsages);\n        return {\n            privateKey: setCryptoKey(keys.privateKey),\n            publicKey: setCryptoKey(keys.publicKey),\n        };\n    }\n    async onEncrypt(algorithm, key, data) {\n        const internalKey = getCryptoKey(key);\n        const dataView = new Uint8Array(data);\n        const keySize = Math.ceil(internalKey.algorithm.modulusLength >> 3);\n        const hashSize = ShaCrypto.size(internalKey.algorithm.hash) >> 3;\n        const dataLength = dataView.byteLength;\n        const psLength = keySize - dataLength - 2 * hashSize - 2;\n        if (dataLength > keySize - 2 * hashSize - 2) {\n            throw new Error(\"Data too large\");\n        }\n        const message = new Uint8Array(keySize);\n        const seed = message.subarray(1, hashSize + 1);\n        const dataBlock = message.subarray(hashSize + 1);\n        dataBlock.set(dataView, hashSize + psLength + 1);\n        const labelHash = crypto__WEBPACK_IMPORTED_MODULE_2___default().createHash(internalKey.algorithm.hash.name.replace(\"-\", \"\"))\n            .update(webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.BufferSourceConverter.toUint8Array(algorithm.label || new Uint8Array(0)))\n            .digest();\n        dataBlock.set(labelHash, 0);\n        dataBlock[hashSize + psLength] = 1;\n        crypto__WEBPACK_IMPORTED_MODULE_2___default().randomFillSync(seed);\n        const dataBlockMask = this.mgf1(internalKey.algorithm.hash, seed, dataBlock.length);\n        for (let i = 0; i < dataBlock.length; i++) {\n            dataBlock[i] ^= dataBlockMask[i];\n        }\n        const seedMask = this.mgf1(internalKey.algorithm.hash, dataBlock, seed.length);\n        for (let i = 0; i < seed.length; i++) {\n            seed[i] ^= seedMask[i];\n        }\n        if (!internalKey.pem) {\n            internalKey.pem = `-----BEGIN PUBLIC KEY-----\\n${internalKey.data.toString(\"base64\")}\\n-----END PUBLIC KEY-----`;\n        }\n        const pkcs0 = crypto__WEBPACK_IMPORTED_MODULE_2___default().publicEncrypt({\n            key: internalKey.pem,\n            padding: (crypto__WEBPACK_IMPORTED_MODULE_2___default().constants).RSA_NO_PADDING,\n        }, buffer__WEBPACK_IMPORTED_MODULE_1__.Buffer.from(message));\n        return new Uint8Array(pkcs0).buffer;\n    }\n    async onDecrypt(algorithm, key, data) {\n        const internalKey = getCryptoKey(key);\n        const keySize = Math.ceil(internalKey.algorithm.modulusLength >> 3);\n        const hashSize = ShaCrypto.size(internalKey.algorithm.hash) >> 3;\n        const dataLength = data.byteLength;\n        if (dataLength !== keySize) {\n            throw new Error(\"Bad data\");\n        }\n        if (!internalKey.pem) {\n            internalKey.pem = `-----BEGIN PRIVATE KEY-----\\n${internalKey.data.toString(\"base64\")}\\n-----END PRIVATE KEY-----`;\n        }\n        let pkcs0 = crypto__WEBPACK_IMPORTED_MODULE_2___default().privateDecrypt({\n            key: internalKey.pem,\n            padding: (crypto__WEBPACK_IMPORTED_MODULE_2___default().constants).RSA_NO_PADDING,\n        }, buffer__WEBPACK_IMPORTED_MODULE_1__.Buffer.from(data));\n        const z = pkcs0[0];\n        const seed = pkcs0.subarray(1, hashSize + 1);\n        const dataBlock = pkcs0.subarray(hashSize + 1);\n        if (z !== 0) {\n            throw new Error(\"Decryption failed\");\n        }\n        const seedMask = this.mgf1(internalKey.algorithm.hash, dataBlock, seed.length);\n        for (let i = 0; i < seed.length; i++) {\n            seed[i] ^= seedMask[i];\n        }\n        const dataBlockMask = this.mgf1(internalKey.algorithm.hash, seed, dataBlock.length);\n        for (let i = 0; i < dataBlock.length; i++) {\n            dataBlock[i] ^= dataBlockMask[i];\n        }\n        const labelHash = crypto__WEBPACK_IMPORTED_MODULE_2___default().createHash(internalKey.algorithm.hash.name.replace(\"-\", \"\"))\n            .update(webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.BufferSourceConverter.toUint8Array(algorithm.label || new Uint8Array(0)))\n            .digest();\n        for (let i = 0; i < hashSize; i++) {\n            if (labelHash[i] !== dataBlock[i]) {\n                throw new Error(\"Decryption failed\");\n            }\n        }\n        let psEnd = hashSize;\n        for (; psEnd < dataBlock.length; psEnd++) {\n            const psz = dataBlock[psEnd];\n            if (psz === 1) {\n                break;\n            }\n            if (psz !== 0) {\n                throw new Error(\"Decryption failed\");\n            }\n        }\n        if (psEnd === dataBlock.length) {\n            throw new Error(\"Decryption failed\");\n        }\n        pkcs0 = dataBlock.subarray(psEnd + 1);\n        return new Uint8Array(pkcs0).buffer;\n    }\n    async onExportKey(format, key) {\n        return RsaCrypto.exportKey(format, getCryptoKey(key));\n    }\n    async onImportKey(format, keyData, algorithm, extractable, keyUsages) {\n        const key = await RsaCrypto.importKey(format, keyData, { ...algorithm, name: this.name }, extractable, keyUsages);\n        return setCryptoKey(key);\n    }\n    checkCryptoKey(key, keyUsage) {\n        super.checkCryptoKey(key, keyUsage);\n        const internalKey = getCryptoKey(key);\n        if (!(internalKey instanceof RsaPrivateKey || internalKey instanceof RsaPublicKey)) {\n            throw new TypeError(\"key: Is not RSA CryptoKey\");\n        }\n    }\n    mgf1(algorithm, seed, length = 0) {\n        const hashSize = ShaCrypto.size(algorithm) >> 3;\n        const mask = new Uint8Array(length);\n        const counter = new Uint8Array(4);\n        const chunks = Math.ceil(length / hashSize);\n        for (let i = 0; i < chunks; i++) {\n            counter[0] = i >>> 24;\n            counter[1] = (i >>> 16) & 255;\n            counter[2] = (i >>> 8) & 255;\n            counter[3] = i & 255;\n            const submask = mask.subarray(i * hashSize);\n            let chunk = crypto__WEBPACK_IMPORTED_MODULE_2___default().createHash(algorithm.name.replace(\"-\", \"\"))\n                .update(seed)\n                .update(counter)\n                .digest();\n            if (chunk.length > submask.length) {\n                chunk = chunk.subarray(0, submask.length);\n            }\n            submask.set(chunk);\n        }\n        return mask;\n    }\n}\n\nclass RsaEsProvider extends webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.ProviderCrypto {\n    constructor() {\n        super(...arguments);\n        this.name = \"RSAES-PKCS1-v1_5\";\n        this.usages = {\n            publicKey: [\"encrypt\", \"wrapKey\"],\n            privateKey: [\"decrypt\", \"unwrapKey\"],\n        };\n    }\n    async onGenerateKey(algorithm, extractable, keyUsages) {\n        const keys = await RsaCrypto.generateKey({\n            ...algorithm,\n            name: this.name,\n        }, extractable, keyUsages);\n        return {\n            privateKey: setCryptoKey(keys.privateKey),\n            publicKey: setCryptoKey(keys.publicKey),\n        };\n    }\n    checkGenerateKeyParams(algorithm) {\n        this.checkRequiredProperty(algorithm, \"publicExponent\");\n        if (!(algorithm.publicExponent && algorithm.publicExponent instanceof Uint8Array)) {\n            throw new TypeError(\"publicExponent: Missing or not a Uint8Array\");\n        }\n        const publicExponent = pvtsutils__WEBPACK_IMPORTED_MODULE_5__.Convert.ToBase64(algorithm.publicExponent);\n        if (!(publicExponent === \"Aw==\" || publicExponent === \"AQAB\")) {\n            throw new TypeError(\"publicExponent: Must be [3] or [1,0,1]\");\n        }\n        this.checkRequiredProperty(algorithm, \"modulusLength\");\n        switch (algorithm.modulusLength) {\n            case 1024:\n            case 2048:\n            case 4096:\n                break;\n            default:\n                throw new TypeError(\"modulusLength: Must be 1024, 2048, or 4096\");\n        }\n    }\n    async onEncrypt(algorithm, key, data) {\n        const options = this.toCryptoOptions(key);\n        const enc = crypto__WEBPACK_IMPORTED_MODULE_2___default().publicEncrypt(options, new Uint8Array(data));\n        return new Uint8Array(enc).buffer;\n    }\n    async onDecrypt(algorithm, key, data) {\n        const options = this.toCryptoOptions(key);\n        const dec = crypto__WEBPACK_IMPORTED_MODULE_2___default().privateDecrypt(options, new Uint8Array(data));\n        return new Uint8Array(dec).buffer;\n    }\n    async onExportKey(format, key) {\n        return RsaCrypto.exportKey(format, getCryptoKey(key));\n    }\n    async onImportKey(format, keyData, algorithm, extractable, keyUsages) {\n        const key = await RsaCrypto.importKey(format, keyData, { ...algorithm, name: this.name }, extractable, keyUsages);\n        return setCryptoKey(key);\n    }\n    checkCryptoKey(key, keyUsage) {\n        super.checkCryptoKey(key, keyUsage);\n        const internalKey = getCryptoKey(key);\n        if (!(internalKey instanceof RsaPrivateKey || internalKey instanceof RsaPublicKey)) {\n            throw new TypeError(\"key: Is not RSA CryptoKey\");\n        }\n    }\n    toCryptoOptions(key) {\n        const type = key.type.toUpperCase();\n        return {\n            key: `-----BEGIN ${type} KEY-----\\n${getCryptoKey(key).data.toString(\"base64\")}\\n-----END ${type} KEY-----`,\n            padding: (crypto__WEBPACK_IMPORTED_MODULE_2___default().constants).RSA_PKCS1_PADDING,\n        };\n    }\n}\n\nconst namedOIDs = {\n    \"1.2.840.10045.3.1.7\": \"P-256\",\n    \"P-256\": \"1.2.840.10045.3.1.7\",\n    \"1.3.132.0.34\": \"P-384\",\n    \"P-384\": \"1.3.132.0.34\",\n    \"1.3.132.0.35\": \"P-521\",\n    \"P-521\": \"1.3.132.0.35\",\n    \"1.3.132.0.10\": \"K-256\",\n    \"K-256\": \"1.3.132.0.10\",\n    \"brainpoolP160r1\": \"1.3.36.3.3.2.8.1.1.1\",\n    \"1.3.36.3.3.2.8.1.1.1\": \"brainpoolP160r1\",\n    \"brainpoolP160t1\": \"1.3.36.3.3.2.8.1.1.2\",\n    \"1.3.36.3.3.2.8.1.1.2\": \"brainpoolP160t1\",\n    \"brainpoolP192r1\": \"1.3.36.3.3.2.8.1.1.3\",\n    \"1.3.36.3.3.2.8.1.1.3\": \"brainpoolP192r1\",\n    \"brainpoolP192t1\": \"1.3.36.3.3.2.8.1.1.4\",\n    \"1.3.36.3.3.2.8.1.1.4\": \"brainpoolP192t1\",\n    \"brainpoolP224r1\": \"1.3.36.3.3.2.8.1.1.5\",\n    \"1.3.36.3.3.2.8.1.1.5\": \"brainpoolP224r1\",\n    \"brainpoolP224t1\": \"1.3.36.3.3.2.8.1.1.6\",\n    \"1.3.36.3.3.2.8.1.1.6\": \"brainpoolP224t1\",\n    \"brainpoolP256r1\": \"1.3.36.3.3.2.8.1.1.7\",\n    \"1.3.36.3.3.2.8.1.1.7\": \"brainpoolP256r1\",\n    \"brainpoolP256t1\": \"1.3.36.3.3.2.8.1.1.8\",\n    \"1.3.36.3.3.2.8.1.1.8\": \"brainpoolP256t1\",\n    \"brainpoolP320r1\": \"1.3.36.3.3.2.8.1.1.9\",\n    \"1.3.36.3.3.2.8.1.1.9\": \"brainpoolP320r1\",\n    \"brainpoolP320t1\": \"1.3.36.3.3.2.8.1.1.10\",\n    \"1.3.36.3.3.2.8.1.1.10\": \"brainpoolP320t1\",\n    \"brainpoolP384r1\": \"1.3.36.3.3.2.8.1.1.11\",\n    \"1.3.36.3.3.2.8.1.1.11\": \"brainpoolP384r1\",\n    \"brainpoolP384t1\": \"1.3.36.3.3.2.8.1.1.12\",\n    \"1.3.36.3.3.2.8.1.1.12\": \"brainpoolP384t1\",\n    \"brainpoolP512r1\": \"1.3.36.3.3.2.8.1.1.13\",\n    \"1.3.36.3.3.2.8.1.1.13\": \"brainpoolP512r1\",\n    \"brainpoolP512t1\": \"1.3.36.3.3.2.8.1.1.14\",\n    \"1.3.36.3.3.2.8.1.1.14\": \"brainpoolP512t1\",\n};\nfunction getOidByNamedCurve$1(namedCurve) {\n    const oid = namedOIDs[namedCurve];\n    if (!oid) {\n        throw new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.OperationError(`Cannot convert WebCrypto named curve '${namedCurve}' to OID`);\n    }\n    return oid;\n}\n\nclass EcPrivateKey extends AsymmetricKey {\n    constructor() {\n        super(...arguments);\n        this.type = \"private\";\n    }\n    getKey() {\n        const keyInfo = _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_6__.AsnParser.parse(this.data, webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.PrivateKeyInfo);\n        return _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_6__.AsnParser.parse(keyInfo.privateKey, webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.EcPrivateKey);\n    }\n    toJSON() {\n        const key = this.getKey();\n        const json = {\n            kty: \"EC\",\n            crv: this.algorithm.namedCurve,\n            key_ops: this.usages,\n            ext: this.extractable,\n        };\n        return Object.assign(json, _peculiar_json_schema__WEBPACK_IMPORTED_MODULE_4__.JsonSerializer.toJSON(key));\n    }\n    fromJSON(json) {\n        if (!json.crv) {\n            throw new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.OperationError(`Cannot get named curve from JWK. Property 'crv' is required`);\n        }\n        const keyInfo = new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.PrivateKeyInfo();\n        keyInfo.privateKeyAlgorithm.algorithm = \"1.2.840.10045.2.1\";\n        keyInfo.privateKeyAlgorithm.parameters = _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_6__.AsnSerializer.serialize(new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.ObjectIdentifier(getOidByNamedCurve$1(json.crv)));\n        const key = _peculiar_json_schema__WEBPACK_IMPORTED_MODULE_4__.JsonParser.fromJSON(json, { targetSchema: webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.EcPrivateKey });\n        keyInfo.privateKey = _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_6__.AsnSerializer.serialize(key);\n        this.data = buffer__WEBPACK_IMPORTED_MODULE_1__.Buffer.from(_peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_6__.AsnSerializer.serialize(keyInfo));\n        return this;\n    }\n}\n\nclass EcPublicKey extends AsymmetricKey {\n    constructor() {\n        super(...arguments);\n        this.type = \"public\";\n    }\n    getKey() {\n        const keyInfo = _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_6__.AsnParser.parse(this.data, webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.PublicKeyInfo);\n        return new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.EcPublicKey(keyInfo.publicKey);\n    }\n    toJSON() {\n        const key = this.getKey();\n        const json = {\n            kty: \"EC\",\n            crv: this.algorithm.namedCurve,\n            key_ops: this.usages,\n            ext: this.extractable,\n        };\n        return Object.assign(json, _peculiar_json_schema__WEBPACK_IMPORTED_MODULE_4__.JsonSerializer.toJSON(key));\n    }\n    fromJSON(json) {\n        if (!json.crv) {\n            throw new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.OperationError(`Cannot get named curve from JWK. Property 'crv' is required`);\n        }\n        const key = _peculiar_json_schema__WEBPACK_IMPORTED_MODULE_4__.JsonParser.fromJSON(json, { targetSchema: webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.EcPublicKey });\n        const keyInfo = new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.PublicKeyInfo();\n        keyInfo.publicKeyAlgorithm.algorithm = \"1.2.840.10045.2.1\";\n        keyInfo.publicKeyAlgorithm.parameters = _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_6__.AsnSerializer.serialize(new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.ObjectIdentifier(getOidByNamedCurve$1(json.crv)));\n        keyInfo.publicKey = _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_6__.AsnSerializer.toASN(key).valueHex;\n        this.data = buffer__WEBPACK_IMPORTED_MODULE_1__.Buffer.from(_peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_6__.AsnSerializer.serialize(keyInfo));\n        return this;\n    }\n}\n\nclass Sha1Provider extends webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.ProviderCrypto {\n    constructor() {\n        super(...arguments);\n        this.name = \"SHA-1\";\n        this.usages = [];\n    }\n    async onDigest(algorithm, data) {\n        return ShaCrypto.digest(algorithm, data);\n    }\n}\n\nclass Sha256Provider extends webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.ProviderCrypto {\n    constructor() {\n        super(...arguments);\n        this.name = \"SHA-256\";\n        this.usages = [];\n    }\n    async onDigest(algorithm, data) {\n        return ShaCrypto.digest(algorithm, data);\n    }\n}\n\nclass Sha384Provider extends webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.ProviderCrypto {\n    constructor() {\n        super(...arguments);\n        this.name = \"SHA-384\";\n        this.usages = [];\n    }\n    async onDigest(algorithm, data) {\n        return ShaCrypto.digest(algorithm, data);\n    }\n}\n\nclass Sha512Provider extends webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.ProviderCrypto {\n    constructor() {\n        super(...arguments);\n        this.name = \"SHA-512\";\n        this.usages = [];\n    }\n    async onDigest(algorithm, data) {\n        return ShaCrypto.digest(algorithm, data);\n    }\n}\n\nclass Sha3256Provider extends webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.ProviderCrypto {\n    constructor() {\n        super(...arguments);\n        this.name = \"SHA3-256\";\n        this.usages = [];\n    }\n    async onDigest(algorithm, data) {\n        return ShaCrypto.digest(algorithm, data);\n    }\n}\n\nclass Sha3384Provider extends webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.ProviderCrypto {\n    constructor() {\n        super(...arguments);\n        this.name = \"SHA3-384\";\n        this.usages = [];\n    }\n    async onDigest(algorithm, data) {\n        return ShaCrypto.digest(algorithm, data);\n    }\n}\n\nclass Sha3512Provider extends webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.ProviderCrypto {\n    constructor() {\n        super(...arguments);\n        this.name = \"SHA3-512\";\n        this.usages = [];\n    }\n    async onDigest(algorithm, data) {\n        return ShaCrypto.digest(algorithm, data);\n    }\n}\n\nclass EcCrypto {\n    static async generateKey(algorithm, extractable, keyUsages) {\n        const privateKey = new EcPrivateKey();\n        privateKey.algorithm = algorithm;\n        privateKey.extractable = extractable;\n        privateKey.usages = keyUsages.filter((usage) => this.privateKeyUsages.indexOf(usage) !== -1);\n        const publicKey = new EcPublicKey();\n        publicKey.algorithm = algorithm;\n        publicKey.extractable = true;\n        publicKey.usages = keyUsages.filter((usage) => this.publicKeyUsages.indexOf(usage) !== -1);\n        const keys = crypto__WEBPACK_IMPORTED_MODULE_2___default().generateKeyPairSync(\"ec\", {\n            namedCurve: this.getOpenSSLNamedCurve(algorithm.namedCurve),\n            publicKeyEncoding: {\n                format: \"der\",\n                type: \"spki\",\n            },\n            privateKeyEncoding: {\n                format: \"der\",\n                type: \"pkcs8\",\n            },\n        });\n        privateKey.data = keys.privateKey;\n        publicKey.data = keys.publicKey;\n        const res = {\n            privateKey,\n            publicKey,\n        };\n        return res;\n    }\n    static async sign(algorithm, key, data) {\n        const cryptoAlg = ShaCrypto.getAlgorithmName(algorithm.hash);\n        const signer = crypto__WEBPACK_IMPORTED_MODULE_2___default().createSign(cryptoAlg);\n        signer.update(buffer__WEBPACK_IMPORTED_MODULE_1__.Buffer.from(data));\n        if (!key.pem) {\n            key.pem = `-----BEGIN PRIVATE KEY-----\\n${key.data.toString(\"base64\")}\\n-----END PRIVATE KEY-----`;\n        }\n        const options = {\n            key: key.pem,\n        };\n        const signature = signer.sign(options);\n        const ecSignature = _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_6__.AsnParser.parse(signature, webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.EcDsaSignature);\n        const signatureRaw = webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.EcUtils.encodeSignature(ecSignature, webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.EcCurves.get(key.algorithm.namedCurve).size);\n        return signatureRaw.buffer;\n    }\n    static async verify(algorithm, key, signature, data) {\n        const cryptoAlg = ShaCrypto.getAlgorithmName(algorithm.hash);\n        const signer = crypto__WEBPACK_IMPORTED_MODULE_2___default().createVerify(cryptoAlg);\n        signer.update(buffer__WEBPACK_IMPORTED_MODULE_1__.Buffer.from(data));\n        if (!key.pem) {\n            key.pem = `-----BEGIN PUBLIC KEY-----\\n${key.data.toString(\"base64\")}\\n-----END PUBLIC KEY-----`;\n        }\n        const options = {\n            key: key.pem,\n        };\n        const ecSignature = new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.EcDsaSignature();\n        const namedCurve = webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.EcCurves.get(key.algorithm.namedCurve);\n        const signaturePoint = webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.EcUtils.decodeSignature(signature, namedCurve.size);\n        ecSignature.r = pvtsutils__WEBPACK_IMPORTED_MODULE_5__.BufferSourceConverter.toArrayBuffer(signaturePoint.r);\n        ecSignature.s = pvtsutils__WEBPACK_IMPORTED_MODULE_5__.BufferSourceConverter.toArrayBuffer(signaturePoint.s);\n        const ecSignatureRaw = buffer__WEBPACK_IMPORTED_MODULE_1__.Buffer.from(_peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_6__.AsnSerializer.serialize(ecSignature));\n        const ok = signer.verify(options, ecSignatureRaw);\n        return ok;\n    }\n    static async deriveBits(algorithm, baseKey, length) {\n        const cryptoAlg = this.getOpenSSLNamedCurve(baseKey.algorithm.namedCurve);\n        const ecdh = crypto__WEBPACK_IMPORTED_MODULE_2___default().createECDH(cryptoAlg);\n        const asnPrivateKey = _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_6__.AsnParser.parse(baseKey.data, webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.PrivateKeyInfo);\n        const asnEcPrivateKey = _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_6__.AsnParser.parse(asnPrivateKey.privateKey, webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.EcPrivateKey);\n        ecdh.setPrivateKey(buffer__WEBPACK_IMPORTED_MODULE_1__.Buffer.from(asnEcPrivateKey.privateKey));\n        const asnPublicKey = _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_6__.AsnParser.parse(algorithm.public.data, webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.PublicKeyInfo);\n        const bits = ecdh.computeSecret(buffer__WEBPACK_IMPORTED_MODULE_1__.Buffer.from(asnPublicKey.publicKey));\n        if (length === null) {\n            return bits;\n        }\n        return new Uint8Array(bits).buffer.slice(0, length >> 3);\n    }\n    static async exportKey(format, key) {\n        switch (format.toLowerCase()) {\n            case \"jwk\":\n                return _peculiar_json_schema__WEBPACK_IMPORTED_MODULE_4__.JsonSerializer.toJSON(key);\n            case \"pkcs8\":\n            case \"spki\":\n                return new Uint8Array(key.data).buffer;\n            case \"raw\": {\n                const publicKeyInfo = _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_6__.AsnParser.parse(key.data, webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.PublicKeyInfo);\n                return publicKeyInfo.publicKey;\n            }\n            default:\n                throw new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.OperationError(\"format: Must be 'jwk', 'raw', pkcs8' or 'spki'\");\n        }\n    }\n    static async importKey(format, keyData, algorithm, extractable, keyUsages) {\n        switch (format.toLowerCase()) {\n            case \"jwk\": {\n                const jwk = keyData;\n                if (jwk.d) {\n                    const asnKey = _peculiar_json_schema__WEBPACK_IMPORTED_MODULE_4__.JsonParser.fromJSON(keyData, { targetSchema: webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.EcPrivateKey });\n                    return this.importPrivateKey(asnKey, algorithm, extractable, keyUsages);\n                }\n                else {\n                    const asnKey = _peculiar_json_schema__WEBPACK_IMPORTED_MODULE_4__.JsonParser.fromJSON(keyData, { targetSchema: webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.EcPublicKey });\n                    return this.importPublicKey(asnKey, algorithm, extractable, keyUsages);\n                }\n            }\n            case \"raw\": {\n                const asnKey = new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.EcPublicKey(keyData);\n                return this.importPublicKey(asnKey, algorithm, extractable, keyUsages);\n            }\n            case \"spki\": {\n                const keyInfo = _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_6__.AsnParser.parse(new Uint8Array(keyData), webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.PublicKeyInfo);\n                const asnKey = new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.EcPublicKey(keyInfo.publicKey);\n                this.assertKeyParameters(keyInfo.publicKeyAlgorithm.parameters, algorithm.namedCurve);\n                return this.importPublicKey(asnKey, algorithm, extractable, keyUsages);\n            }\n            case \"pkcs8\": {\n                const keyInfo = _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_6__.AsnParser.parse(new Uint8Array(keyData), webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.PrivateKeyInfo);\n                const asnKey = _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_6__.AsnParser.parse(keyInfo.privateKey, webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.EcPrivateKey);\n                this.assertKeyParameters(keyInfo.privateKeyAlgorithm.parameters, algorithm.namedCurve);\n                return this.importPrivateKey(asnKey, algorithm, extractable, keyUsages);\n            }\n            default:\n                throw new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.OperationError(\"format: Must be 'jwk', 'raw', 'pkcs8' or 'spki'\");\n        }\n    }\n    static assertKeyParameters(parameters, namedCurve) {\n        if (!parameters) {\n            throw new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.CryptoError(\"Key info doesn't have required parameters\");\n        }\n        let namedCurveIdentifier = \"\";\n        try {\n            namedCurveIdentifier = _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_6__.AsnParser.parse(parameters, webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.ObjectIdentifier).value;\n        }\n        catch (e) {\n            throw new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.CryptoError(\"Cannot read key info parameters\");\n        }\n        if (getOidByNamedCurve$1(namedCurve) !== namedCurveIdentifier) {\n            throw new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.CryptoError(\"Key info parameter doesn't match to named curve\");\n        }\n    }\n    static async importPrivateKey(asnKey, algorithm, extractable, keyUsages) {\n        const keyInfo = new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.PrivateKeyInfo();\n        keyInfo.privateKeyAlgorithm.algorithm = \"1.2.840.10045.2.1\";\n        keyInfo.privateKeyAlgorithm.parameters = _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_6__.AsnSerializer.serialize(new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.ObjectIdentifier(getOidByNamedCurve$1(algorithm.namedCurve)));\n        keyInfo.privateKey = _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_6__.AsnSerializer.serialize(asnKey);\n        const key = new EcPrivateKey();\n        key.data = buffer__WEBPACK_IMPORTED_MODULE_1__.Buffer.from(_peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_6__.AsnSerializer.serialize(keyInfo));\n        key.algorithm = Object.assign({}, algorithm);\n        key.extractable = extractable;\n        key.usages = keyUsages;\n        return key;\n    }\n    static async importPublicKey(asnKey, algorithm, extractable, keyUsages) {\n        const keyInfo = new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.PublicKeyInfo();\n        keyInfo.publicKeyAlgorithm.algorithm = \"1.2.840.10045.2.1\";\n        const namedCurve = getOidByNamedCurve$1(algorithm.namedCurve);\n        keyInfo.publicKeyAlgorithm.parameters = _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_6__.AsnSerializer.serialize(new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.ObjectIdentifier(namedCurve));\n        keyInfo.publicKey = asnKey.value;\n        const key = new EcPublicKey();\n        key.data = buffer__WEBPACK_IMPORTED_MODULE_1__.Buffer.from(_peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_6__.AsnSerializer.serialize(keyInfo));\n        key.algorithm = Object.assign({}, algorithm);\n        key.extractable = extractable;\n        key.usages = keyUsages;\n        return key;\n    }\n    static getOpenSSLNamedCurve(curve) {\n        switch (curve.toUpperCase()) {\n            case \"P-256\":\n                return \"prime256v1\";\n            case \"K-256\":\n                return \"secp256k1\";\n            case \"P-384\":\n                return \"secp384r1\";\n            case \"P-521\":\n                return \"secp521r1\";\n            default:\n                return curve;\n        }\n    }\n}\nEcCrypto.publicKeyUsages = [\"verify\"];\nEcCrypto.privateKeyUsages = [\"sign\", \"deriveKey\", \"deriveBits\"];\n\nclass EcdsaProvider extends webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.EcdsaProvider {\n    constructor() {\n        super(...arguments);\n        this.namedCurves = webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.EcCurves.names;\n        this.hashAlgorithms = [\n            \"SHA-1\", \"SHA-256\", \"SHA-384\", \"SHA-512\",\n            \"shake128\", \"shake256\",\n            \"SHA3-256\", \"SHA3-384\", \"SHA3-512\"\n        ];\n    }\n    async onGenerateKey(algorithm, extractable, keyUsages) {\n        const keys = await EcCrypto.generateKey({\n            ...algorithm,\n            name: this.name,\n        }, extractable, keyUsages);\n        return {\n            privateKey: setCryptoKey(keys.privateKey),\n            publicKey: setCryptoKey(keys.publicKey),\n        };\n    }\n    async onSign(algorithm, key, data) {\n        return EcCrypto.sign(algorithm, getCryptoKey(key), new Uint8Array(data));\n    }\n    async onVerify(algorithm, key, signature, data) {\n        return EcCrypto.verify(algorithm, getCryptoKey(key), new Uint8Array(signature), new Uint8Array(data));\n    }\n    async onExportKey(format, key) {\n        return EcCrypto.exportKey(format, getCryptoKey(key));\n    }\n    async onImportKey(format, keyData, algorithm, extractable, keyUsages) {\n        const key = await EcCrypto.importKey(format, keyData, { ...algorithm, name: this.name }, extractable, keyUsages);\n        return setCryptoKey(key);\n    }\n    checkCryptoKey(key, keyUsage) {\n        super.checkCryptoKey(key, keyUsage);\n        const internalKey = getCryptoKey(key);\n        if (!(internalKey instanceof EcPrivateKey || internalKey instanceof EcPublicKey)) {\n            throw new TypeError(\"key: Is not EC CryptoKey\");\n        }\n    }\n}\n\nclass EcdhProvider extends webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.EcdhProvider {\n    constructor() {\n        super(...arguments);\n        this.namedCurves = webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.EcCurves.names;\n    }\n    async onGenerateKey(algorithm, extractable, keyUsages) {\n        const keys = await EcCrypto.generateKey({\n            ...algorithm,\n            name: this.name,\n        }, extractable, keyUsages);\n        return {\n            privateKey: setCryptoKey(keys.privateKey),\n            publicKey: setCryptoKey(keys.publicKey),\n        };\n    }\n    async onExportKey(format, key) {\n        return EcCrypto.exportKey(format, getCryptoKey(key));\n    }\n    async onImportKey(format, keyData, algorithm, extractable, keyUsages) {\n        const key = await EcCrypto.importKey(format, keyData, { ...algorithm, name: this.name }, extractable, keyUsages);\n        return setCryptoKey(key);\n    }\n    checkCryptoKey(key, keyUsage) {\n        super.checkCryptoKey(key, keyUsage);\n        const internalKey = getCryptoKey(key);\n        if (!(internalKey instanceof EcPrivateKey || internalKey instanceof EcPublicKey)) {\n            throw new TypeError(\"key: Is not EC CryptoKey\");\n        }\n    }\n    async onDeriveBits(algorithm, baseKey, length) {\n        const bits = await EcCrypto.deriveBits({ ...algorithm, public: getCryptoKey(algorithm.public) }, getCryptoKey(baseKey), length);\n        return bits;\n    }\n}\n\nconst edOIDs = {\n    [webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.idEd448]: \"Ed448\",\n    \"ed448\": webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.idEd448,\n    [webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.idX448]: \"X448\",\n    \"x448\": webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.idX448,\n    [webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.idEd25519]: \"Ed25519\",\n    \"ed25519\": webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.idEd25519,\n    [webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.idX25519]: \"X25519\",\n    \"x25519\": webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.idX25519,\n};\nfunction getOidByNamedCurve(namedCurve) {\n    const oid = edOIDs[namedCurve.toLowerCase()];\n    if (!oid) {\n        throw new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.OperationError(`Cannot convert WebCrypto named curve '${namedCurve}' to OID`);\n    }\n    return oid;\n}\n\nclass EdPrivateKey extends AsymmetricKey {\n    constructor() {\n        super(...arguments);\n        this.type = \"private\";\n    }\n    getKey() {\n        const keyInfo = _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_6__.AsnParser.parse(this.data, webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.PrivateKeyInfo);\n        return _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_6__.AsnParser.parse(keyInfo.privateKey, webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.CurvePrivateKey);\n    }\n    toJSON() {\n        const key = this.getKey();\n        const json = {\n            kty: \"OKP\",\n            crv: this.algorithm.namedCurve,\n            key_ops: this.usages,\n            ext: this.extractable,\n        };\n        return Object.assign(json, _peculiar_json_schema__WEBPACK_IMPORTED_MODULE_4__.JsonSerializer.toJSON(key));\n    }\n    fromJSON(json) {\n        if (!json.crv) {\n            throw new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.OperationError(`Cannot get named curve from JWK. Property 'crv' is required`);\n        }\n        const keyInfo = new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.PrivateKeyInfo();\n        keyInfo.privateKeyAlgorithm.algorithm = getOidByNamedCurve(json.crv);\n        const key = _peculiar_json_schema__WEBPACK_IMPORTED_MODULE_4__.JsonParser.fromJSON(json, { targetSchema: webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.CurvePrivateKey });\n        keyInfo.privateKey = _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_6__.AsnSerializer.serialize(key);\n        this.data = buffer__WEBPACK_IMPORTED_MODULE_1__.Buffer.from(_peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_6__.AsnSerializer.serialize(keyInfo));\n        return this;\n    }\n}\n\nclass EdPublicKey extends AsymmetricKey {\n    constructor() {\n        super(...arguments);\n        this.type = \"public\";\n    }\n    getKey() {\n        const keyInfo = _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_6__.AsnParser.parse(this.data, webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.PublicKeyInfo);\n        return keyInfo.publicKey;\n    }\n    toJSON() {\n        const key = this.getKey();\n        const json = {\n            kty: \"OKP\",\n            crv: this.algorithm.namedCurve,\n            key_ops: this.usages,\n            ext: this.extractable,\n        };\n        return Object.assign(json, {\n            x: pvtsutils__WEBPACK_IMPORTED_MODULE_5__.Convert.ToBase64Url(key)\n        });\n    }\n    fromJSON(json) {\n        if (!json.crv) {\n            throw new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.OperationError(`Cannot get named curve from JWK. Property 'crv' is required`);\n        }\n        if (!json.x) {\n            throw new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.OperationError(`Cannot get property from JWK. Property 'x' is required`);\n        }\n        const keyInfo = new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.PublicKeyInfo();\n        keyInfo.publicKeyAlgorithm.algorithm = getOidByNamedCurve(json.crv);\n        keyInfo.publicKey = pvtsutils__WEBPACK_IMPORTED_MODULE_5__.Convert.FromBase64Url(json.x);\n        this.data = buffer__WEBPACK_IMPORTED_MODULE_1__.Buffer.from(_peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_6__.AsnSerializer.serialize(keyInfo));\n        return this;\n    }\n}\n\nclass EdCrypto {\n    static async generateKey(algorithm, extractable, keyUsages) {\n        const privateKey = new EdPrivateKey();\n        privateKey.algorithm = algorithm;\n        privateKey.extractable = extractable;\n        privateKey.usages = keyUsages.filter((usage) => this.privateKeyUsages.indexOf(usage) !== -1);\n        const publicKey = new EdPublicKey();\n        publicKey.algorithm = algorithm;\n        publicKey.extractable = true;\n        publicKey.usages = keyUsages.filter((usage) => this.publicKeyUsages.indexOf(usage) !== -1);\n        const type = algorithm.namedCurve.toLowerCase();\n        const keys = crypto__WEBPACK_IMPORTED_MODULE_2___default().generateKeyPairSync(type, {\n            publicKeyEncoding: {\n                format: \"der\",\n                type: \"spki\",\n            },\n            privateKeyEncoding: {\n                format: \"der\",\n                type: \"pkcs8\",\n            },\n        });\n        privateKey.data = keys.privateKey;\n        publicKey.data = keys.publicKey;\n        const res = {\n            privateKey,\n            publicKey,\n        };\n        return res;\n    }\n    static async sign(algorithm, key, data) {\n        if (!key.pem) {\n            key.pem = `-----BEGIN PRIVATE KEY-----\\n${key.data.toString(\"base64\")}\\n-----END PRIVATE KEY-----`;\n        }\n        const options = {\n            key: key.pem,\n        };\n        const signature = crypto__WEBPACK_IMPORTED_MODULE_2___default().sign(null, buffer__WEBPACK_IMPORTED_MODULE_1__.Buffer.from(data), options);\n        return webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.BufferSourceConverter.toArrayBuffer(signature);\n    }\n    static async verify(algorithm, key, signature, data) {\n        if (!key.pem) {\n            key.pem = `-----BEGIN PUBLIC KEY-----\\n${key.data.toString(\"base64\")}\\n-----END PUBLIC KEY-----`;\n        }\n        const options = {\n            key: key.pem,\n        };\n        const ok = crypto__WEBPACK_IMPORTED_MODULE_2___default().verify(null, buffer__WEBPACK_IMPORTED_MODULE_1__.Buffer.from(data), options, buffer__WEBPACK_IMPORTED_MODULE_1__.Buffer.from(signature));\n        return ok;\n    }\n    static async deriveBits(algorithm, baseKey, length) {\n        const publicKey = crypto__WEBPACK_IMPORTED_MODULE_2___default().createPublicKey({\n            key: algorithm.public.data,\n            format: \"der\",\n            type: \"spki\",\n        });\n        const privateKey = crypto__WEBPACK_IMPORTED_MODULE_2___default().createPrivateKey({\n            key: baseKey.data,\n            format: \"der\",\n            type: \"pkcs8\",\n        });\n        const bits = crypto__WEBPACK_IMPORTED_MODULE_2___default().diffieHellman({\n            publicKey,\n            privateKey,\n        });\n        return new Uint8Array(bits).buffer.slice(0, length >> 3);\n    }\n    static async exportKey(format, key) {\n        switch (format.toLowerCase()) {\n            case \"jwk\":\n                return _peculiar_json_schema__WEBPACK_IMPORTED_MODULE_4__.JsonSerializer.toJSON(key);\n            case \"pkcs8\":\n            case \"spki\":\n                return new Uint8Array(key.data).buffer;\n            case \"raw\": {\n                const publicKeyInfo = _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_6__.AsnParser.parse(key.data, webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.PublicKeyInfo);\n                return publicKeyInfo.publicKey;\n            }\n            default:\n                throw new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.OperationError(\"format: Must be 'jwk', 'raw', pkcs8' or 'spki'\");\n        }\n    }\n    static async importKey(format, keyData, algorithm, extractable, keyUsages) {\n        switch (format.toLowerCase()) {\n            case \"jwk\": {\n                const jwk = keyData;\n                if (jwk.d) {\n                    const asnKey = _peculiar_json_schema__WEBPACK_IMPORTED_MODULE_4__.JsonParser.fromJSON(keyData, { targetSchema: webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.CurvePrivateKey });\n                    return this.importPrivateKey(asnKey, algorithm, extractable, keyUsages);\n                }\n                else {\n                    if (!jwk.x) {\n                        throw new TypeError(\"keyData: Cannot get required 'x' filed\");\n                    }\n                    return this.importPublicKey(pvtsutils__WEBPACK_IMPORTED_MODULE_5__.Convert.FromBase64Url(jwk.x), algorithm, extractable, keyUsages);\n                }\n            }\n            case \"raw\": {\n                return this.importPublicKey(keyData, algorithm, extractable, keyUsages);\n            }\n            case \"spki\": {\n                const keyInfo = _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_6__.AsnParser.parse(new Uint8Array(keyData), webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.PublicKeyInfo);\n                return this.importPublicKey(keyInfo.publicKey, algorithm, extractable, keyUsages);\n            }\n            case \"pkcs8\": {\n                const keyInfo = _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_6__.AsnParser.parse(new Uint8Array(keyData), webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.PrivateKeyInfo);\n                const asnKey = _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_6__.AsnParser.parse(keyInfo.privateKey, webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.CurvePrivateKey);\n                return this.importPrivateKey(asnKey, algorithm, extractable, keyUsages);\n            }\n            default:\n                throw new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.OperationError(\"format: Must be 'jwk', 'raw', 'pkcs8' or 'spki'\");\n        }\n    }\n    static importPrivateKey(asnKey, algorithm, extractable, keyUsages) {\n        const key = new EdPrivateKey();\n        key.fromJSON({\n            crv: algorithm.namedCurve,\n            d: pvtsutils__WEBPACK_IMPORTED_MODULE_5__.Convert.ToBase64Url(asnKey.d),\n        });\n        key.algorithm = Object.assign({}, algorithm);\n        key.extractable = extractable;\n        key.usages = keyUsages;\n        return key;\n    }\n    static async importPublicKey(asnKey, algorithm, extractable, keyUsages) {\n        const key = new EdPublicKey();\n        key.fromJSON({\n            crv: algorithm.namedCurve,\n            x: pvtsutils__WEBPACK_IMPORTED_MODULE_5__.Convert.ToBase64Url(asnKey),\n        });\n        key.algorithm = Object.assign({}, algorithm);\n        key.extractable = extractable;\n        key.usages = keyUsages;\n        return key;\n    }\n}\nEdCrypto.publicKeyUsages = [\"verify\"];\nEdCrypto.privateKeyUsages = [\"sign\", \"deriveKey\", \"deriveBits\"];\n\nclass EdDsaProvider extends webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.EdDsaProvider {\n    async onGenerateKey(algorithm, extractable, keyUsages) {\n        const keys = await EdCrypto.generateKey({\n            name: this.name,\n            namedCurve: algorithm.namedCurve.replace(/^ed/i, \"Ed\"),\n        }, extractable, keyUsages);\n        return {\n            privateKey: setCryptoKey(keys.privateKey),\n            publicKey: setCryptoKey(keys.publicKey),\n        };\n    }\n    async onSign(algorithm, key, data) {\n        return EdCrypto.sign(algorithm, getCryptoKey(key), new Uint8Array(data));\n    }\n    async onVerify(algorithm, key, signature, data) {\n        return EdCrypto.verify(algorithm, getCryptoKey(key), new Uint8Array(signature), new Uint8Array(data));\n    }\n    async onExportKey(format, key) {\n        return EdCrypto.exportKey(format, getCryptoKey(key));\n    }\n    async onImportKey(format, keyData, algorithm, extractable, keyUsages) {\n        const key = await EdCrypto.importKey(format, keyData, { ...algorithm, name: this.name }, extractable, keyUsages);\n        return setCryptoKey(key);\n    }\n}\n\nclass EcdhEsProvider extends webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.EcdhEsProvider {\n    async onGenerateKey(algorithm, extractable, keyUsages) {\n        const keys = await EdCrypto.generateKey({\n            name: this.name,\n            namedCurve: algorithm.namedCurve.toUpperCase(),\n        }, extractable, keyUsages);\n        return {\n            privateKey: setCryptoKey(keys.privateKey),\n            publicKey: setCryptoKey(keys.publicKey),\n        };\n    }\n    async onDeriveBits(algorithm, baseKey, length) {\n        const bits = await EdCrypto.deriveBits({ ...algorithm, public: getCryptoKey(algorithm.public) }, getCryptoKey(baseKey), length);\n        return bits;\n    }\n    async onExportKey(format, key) {\n        return EdCrypto.exportKey(format, getCryptoKey(key));\n    }\n    async onImportKey(format, keyData, algorithm, extractable, keyUsages) {\n        const key = await EdCrypto.importKey(format, keyData, { ...algorithm, name: this.name }, extractable, keyUsages);\n        return setCryptoKey(key);\n    }\n}\n\nclass Ed25519CryptoKey extends CryptoKey {\n    constructor(algorithm, extractable, usages, data) {\n        super();\n        this.algorithm = algorithm;\n        this.extractable = extractable;\n        this.usages = usages;\n        this.data = Buffer.from(data);\n    }\n    toJWK() {\n        return {\n            kty: \"OKP\",\n            crv: this.algorithm.name,\n            key_ops: this.usages,\n            ext: this.extractable,\n        };\n    }\n}\n\nclass Ed25519PrivateKey extends Ed25519CryptoKey {\n    constructor() {\n        super(...arguments);\n        this.type = \"private\";\n    }\n    toJWK() {\n        const pubJwk = crypto__WEBPACK_IMPORTED_MODULE_2___default().createPublicKey({\n            key: this.data,\n            format: \"pem\",\n        }).export({ format: \"jwk\" });\n        const raw = webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.PemConverter.toUint8Array(this.data.toString());\n        const pkcs8 = _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_6__.AsnConvert.parse(raw, webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.PrivateKeyInfo);\n        const d = _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_6__.AsnConvert.parse(pkcs8.privateKey, webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.EdPrivateKey).value;\n        return {\n            ...super.toJWK(),\n            ...pubJwk,\n            d: Buffer.from(new Uint8Array(d)).toString(\"base64url\"),\n        };\n    }\n}\n\nclass Ed25519PublicKey extends Ed25519CryptoKey {\n    constructor() {\n        super(...arguments);\n        this.type = \"public\";\n    }\n    toJWK() {\n        const jwk = crypto__WEBPACK_IMPORTED_MODULE_2___default().createPublicKey({\n            key: this.data,\n            format: \"pem\",\n        }).export({ format: \"jwk\" });\n        return {\n            ...super.toJWK(),\n            ...jwk,\n        };\n    }\n}\n\nclass Ed25519Crypto {\n    static async generateKey(algorithm, extractable, keyUsages) {\n        const type = algorithm.name.toLowerCase();\n        const keys = crypto__WEBPACK_IMPORTED_MODULE_2___default().generateKeyPairSync(type, {\n            publicKeyEncoding: {\n                format: \"pem\",\n                type: \"spki\",\n            },\n            privateKeyEncoding: {\n                format: \"pem\",\n                type: \"pkcs8\",\n            },\n        });\n        const keyAlg = {\n            name: type === \"ed25519\" ? \"Ed25519\" : \"X25519\",\n        };\n        const privateKeyUsages = keyUsages.filter((usage) => this.privateKeyUsages.includes(usage));\n        const publicKeyUsages = keyUsages.filter((usage) => this.publicKeyUsages.includes(usage));\n        return {\n            privateKey: new Ed25519PrivateKey(keyAlg, extractable, privateKeyUsages, keys.privateKey),\n            publicKey: new Ed25519PublicKey(keyAlg, true, publicKeyUsages, keys.publicKey),\n        };\n    }\n    static async sign(algorithm, key, data) {\n        const signature = crypto__WEBPACK_IMPORTED_MODULE_2___default().sign(null, Buffer.from(data), key.data);\n        return webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.BufferSourceConverter.toArrayBuffer(signature);\n    }\n    static async verify(algorithm, key, signature, data) {\n        return crypto__WEBPACK_IMPORTED_MODULE_2___default().verify(null, Buffer.from(data), key.data, signature);\n    }\n    static async exportKey(format, key) {\n        switch (format) {\n            case \"jwk\":\n                return key.toJWK();\n            case \"pkcs8\": {\n                return webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.PemConverter.toArrayBuffer(key.data.toString());\n            }\n            case \"spki\": {\n                return webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.PemConverter.toArrayBuffer(key.data.toString());\n            }\n            case \"raw\": {\n                const jwk = key.toJWK();\n                return pvtsutils__WEBPACK_IMPORTED_MODULE_5__.Convert.FromBase64Url(jwk.x);\n            }\n            default:\n                return Promise.reject(new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.OperationError(\"format: Must be 'jwk', 'raw', pkcs8' or 'spki'\"));\n        }\n    }\n    static async importKey(format, keyData, algorithm, extractable, keyUsages) {\n        switch (format) {\n            case \"jwk\": {\n                const jwk = keyData;\n                if (jwk.d) {\n                    const privateData = new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.EdPrivateKey();\n                    privateData.value = webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.BufferSourceConverter.toArrayBuffer(Buffer.from(jwk.d, \"base64url\"));\n                    const pkcs8 = new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.PrivateKeyInfo();\n                    pkcs8.privateKeyAlgorithm.algorithm = algorithm.name.toLowerCase() === \"ed25519\"\n                        ? webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.idEd25519\n                        : webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.idX25519;\n                    pkcs8.privateKey = _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_6__.AsnConvert.serialize(privateData);\n                    const raw = _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_6__.AsnConvert.serialize(pkcs8);\n                    const pem = webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.PemConverter.fromBufferSource(raw, \"PRIVATE KEY\");\n                    return new Ed25519PrivateKey(algorithm, extractable, keyUsages, pem);\n                }\n                else if (jwk.x) {\n                    const pubKey = crypto__WEBPACK_IMPORTED_MODULE_2___default().createPublicKey({\n                        format: \"jwk\",\n                        key: jwk,\n                    });\n                    const pem = pubKey.export({ format: \"pem\", type: \"spki\" });\n                    return new Ed25519PublicKey(algorithm, extractable, keyUsages, pem);\n                }\n                else {\n                    throw new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.OperationError(\"keyData: Cannot import JWK. 'd' or 'x' must be presented\");\n                }\n            }\n            case \"pkcs8\": {\n                const pem = webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.PemConverter.fromBufferSource(keyData, \"PRIVATE KEY\");\n                return new Ed25519PrivateKey(algorithm, extractable, keyUsages, pem);\n            }\n            case \"spki\": {\n                const pem = webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.PemConverter.fromBufferSource(keyData, \"PUBLIC KEY\");\n                return new Ed25519PublicKey(algorithm, extractable, keyUsages, pem);\n            }\n            case \"raw\": {\n                const raw = keyData;\n                const key = crypto__WEBPACK_IMPORTED_MODULE_2___default().createPublicKey({\n                    format: \"jwk\",\n                    key: {\n                        kty: \"OKP\",\n                        crv: algorithm.name.toLowerCase() === \"ed25519\" ? \"Ed25519\" : \"X25519\",\n                        x: pvtsutils__WEBPACK_IMPORTED_MODULE_5__.Convert.ToBase64Url(raw),\n                    },\n                });\n                const pem = key.export({ format: \"pem\", type: \"spki\" });\n                return new Ed25519PublicKey(algorithm, extractable, keyUsages, pem);\n            }\n            default:\n                return Promise.reject(new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.OperationError(\"format: Must be 'jwk', 'raw', pkcs8' or 'spki'\"));\n        }\n    }\n}\nEd25519Crypto.privateKeyUsages = [\"sign\", \"deriveBits\", \"deriveKey\"];\nEd25519Crypto.publicKeyUsages = [\"verify\"];\n\nclass Ed25519Provider extends webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.Ed25519Provider {\n    async onGenerateKey(algorithm, extractable, keyUsages) {\n        const keys = await Ed25519Crypto.generateKey(algorithm, extractable, keyUsages);\n        return {\n            privateKey: setCryptoKey(keys.privateKey),\n            publicKey: setCryptoKey(keys.publicKey),\n        };\n    }\n    async onSign(algorithm, key, data) {\n        const internalKey = getCryptoKey(key);\n        const signature = Ed25519Crypto.sign(algorithm, internalKey, new Uint8Array(data));\n        return signature;\n    }\n    onVerify(algorithm, key, signature, data) {\n        const internalKey = getCryptoKey(key);\n        return Ed25519Crypto.verify(algorithm, internalKey, new Uint8Array(signature), new Uint8Array(data));\n    }\n    async onExportKey(format, key) {\n        const internalKey = getCryptoKey(key);\n        return Ed25519Crypto.exportKey(format, internalKey);\n    }\n    async onImportKey(format, keyData, algorithm, extractable, keyUsages) {\n        const internalKey = await Ed25519Crypto.importKey(format, keyData, algorithm, extractable, keyUsages);\n        return setCryptoKey(internalKey);\n    }\n}\n\nclass X25519Provider extends webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.X25519Provider {\n    async onGenerateKey(algorithm, extractable, keyUsages) {\n        const keys = await Ed25519Crypto.generateKey(algorithm, extractable, keyUsages);\n        return {\n            privateKey: setCryptoKey(keys.privateKey),\n            publicKey: setCryptoKey(keys.publicKey),\n        };\n    }\n    async onDeriveBits(algorithm, baseKey, length) {\n        const internalBaseKey = getCryptoKey(baseKey);\n        const internalPublicKey = getCryptoKey(algorithm.public);\n        const publicKey = crypto__WEBPACK_IMPORTED_MODULE_2___default().createPublicKey({\n            key: internalPublicKey.data.toString(),\n            format: \"pem\",\n            type: \"spki\",\n        });\n        const privateKey = crypto__WEBPACK_IMPORTED_MODULE_2___default().createPrivateKey({\n            key: internalBaseKey.data.toString(),\n            format: \"pem\",\n            type: \"pkcs8\",\n        });\n        const bits = crypto__WEBPACK_IMPORTED_MODULE_2___default().diffieHellman({\n            publicKey,\n            privateKey,\n        });\n        return new Uint8Array(bits).buffer.slice(0, length >> 3);\n    }\n    async onExportKey(format, key) {\n        const internalKey = getCryptoKey(key);\n        return Ed25519Crypto.exportKey(format, internalKey);\n    }\n    async onImportKey(format, keyData, algorithm, extractable, keyUsages) {\n        const key = await Ed25519Crypto.importKey(format, keyData, algorithm, extractable, keyUsages);\n        return setCryptoKey(key);\n    }\n    checkCryptoKey(key, keyUsage) {\n        super.checkCryptoKey(key, keyUsage);\n        if (!(getCryptoKey(key) instanceof Ed25519CryptoKey)) {\n            throw new TypeError(\"key: Is not a Ed25519CryptoKey\");\n        }\n    }\n}\n\nclass PbkdfCryptoKey extends CryptoKey {\n}\n\nclass Pbkdf2Provider extends webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.Pbkdf2Provider {\n    async onDeriveBits(algorithm, baseKey, length) {\n        return new Promise((resolve, reject) => {\n            const salt = webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.BufferSourceConverter.toArrayBuffer(algorithm.salt);\n            const hash = algorithm.hash.name.replace(\"-\", \"\");\n            crypto__WEBPACK_IMPORTED_MODULE_2___default().pbkdf2(getCryptoKey(baseKey).data, buffer__WEBPACK_IMPORTED_MODULE_1__.Buffer.from(salt), algorithm.iterations, length >> 3, hash, (err, derivedBits) => {\n                if (err) {\n                    reject(err);\n                }\n                else {\n                    resolve(new Uint8Array(derivedBits).buffer);\n                }\n            });\n        });\n    }\n    async onImportKey(format, keyData, algorithm, extractable, keyUsages) {\n        if (format === \"raw\") {\n            const key = new PbkdfCryptoKey();\n            key.data = buffer__WEBPACK_IMPORTED_MODULE_1__.Buffer.from(keyData);\n            key.algorithm = { name: this.name };\n            key.extractable = false;\n            key.usages = keyUsages;\n            return setCryptoKey(key);\n        }\n        throw new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.OperationError(\"format: Must be 'raw'\");\n    }\n    checkCryptoKey(key, keyUsage) {\n        super.checkCryptoKey(key, keyUsage);\n        if (!(getCryptoKey(key) instanceof PbkdfCryptoKey)) {\n            throw new TypeError(\"key: Is not PBKDF CryptoKey\");\n        }\n    }\n}\n\nclass HmacCryptoKey extends CryptoKey {\n    get alg() {\n        const hash = this.algorithm.hash.name.toUpperCase();\n        return `HS${hash.replace(\"SHA-\", \"\")}`;\n    }\n    set alg(value) {\n    }\n}\n(0,tslib__WEBPACK_IMPORTED_MODULE_7__.__decorate)([\n    (0,_peculiar_json_schema__WEBPACK_IMPORTED_MODULE_4__.JsonProp)({ name: \"k\", converter: JsonBase64UrlConverter })\n], HmacCryptoKey.prototype, \"data\", void 0);\n\nclass HmacProvider extends webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.HmacProvider {\n    async onGenerateKey(algorithm, extractable, keyUsages) {\n        const length = (algorithm.length || this.getDefaultLength(algorithm.hash.name)) >> 3 << 3;\n        const key = new HmacCryptoKey();\n        key.algorithm = {\n            ...algorithm,\n            length,\n            name: this.name,\n        };\n        key.extractable = extractable;\n        key.usages = keyUsages;\n        key.data = crypto__WEBPACK_IMPORTED_MODULE_2___default().randomBytes(length >> 3);\n        return setCryptoKey(key);\n    }\n    async onSign(algorithm, key, data) {\n        const cryptoAlg = ShaCrypto.getAlgorithmName(key.algorithm.hash);\n        const hmac = crypto__WEBPACK_IMPORTED_MODULE_2___default().createHmac(cryptoAlg, getCryptoKey(key).data)\n            .update(buffer__WEBPACK_IMPORTED_MODULE_1__.Buffer.from(data)).digest();\n        return new Uint8Array(hmac).buffer;\n    }\n    async onVerify(algorithm, key, signature, data) {\n        const cryptoAlg = ShaCrypto.getAlgorithmName(key.algorithm.hash);\n        const hmac = crypto__WEBPACK_IMPORTED_MODULE_2___default().createHmac(cryptoAlg, getCryptoKey(key).data)\n            .update(buffer__WEBPACK_IMPORTED_MODULE_1__.Buffer.from(data)).digest();\n        return hmac.compare(buffer__WEBPACK_IMPORTED_MODULE_1__.Buffer.from(signature)) === 0;\n    }\n    async onImportKey(format, keyData, algorithm, extractable, keyUsages) {\n        let key;\n        switch (format.toLowerCase()) {\n            case \"jwk\":\n                key = _peculiar_json_schema__WEBPACK_IMPORTED_MODULE_4__.JsonParser.fromJSON(keyData, { targetSchema: HmacCryptoKey });\n                break;\n            case \"raw\":\n                key = new HmacCryptoKey();\n                key.data = buffer__WEBPACK_IMPORTED_MODULE_1__.Buffer.from(keyData);\n                break;\n            default:\n                throw new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.OperationError(\"format: Must be 'jwk' or 'raw'\");\n        }\n        key.algorithm = {\n            hash: { name: algorithm.hash.name },\n            name: this.name,\n            length: key.data.length << 3,\n        };\n        key.extractable = extractable;\n        key.usages = keyUsages;\n        return setCryptoKey(key);\n    }\n    async onExportKey(format, key) {\n        switch (format.toLowerCase()) {\n            case \"jwk\":\n                return _peculiar_json_schema__WEBPACK_IMPORTED_MODULE_4__.JsonSerializer.toJSON(getCryptoKey(key));\n            case \"raw\":\n                return new Uint8Array(getCryptoKey(key).data).buffer;\n            default:\n                throw new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.OperationError(\"format: Must be 'jwk' or 'raw'\");\n        }\n    }\n    checkCryptoKey(key, keyUsage) {\n        super.checkCryptoKey(key, keyUsage);\n        if (!(getCryptoKey(key) instanceof HmacCryptoKey)) {\n            throw new TypeError(\"key: Is not HMAC CryptoKey\");\n        }\n    }\n}\n\nclass HkdfCryptoKey extends CryptoKey {\n}\n\nclass HkdfProvider extends webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.HkdfProvider {\n    async onImportKey(format, keyData, algorithm, extractable, keyUsages) {\n        if (format.toLowerCase() !== \"raw\") {\n            throw new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.OperationError(\"Operation not supported\");\n        }\n        const key = new HkdfCryptoKey();\n        key.data = buffer__WEBPACK_IMPORTED_MODULE_1__.Buffer.from(keyData);\n        key.algorithm = { name: this.name };\n        key.extractable = extractable;\n        key.usages = keyUsages;\n        return setCryptoKey(key);\n    }\n    async onDeriveBits(params, baseKey, length) {\n        const hash = params.hash.name.replace(\"-\", \"\");\n        const hashLength = crypto__WEBPACK_IMPORTED_MODULE_2___default().createHash(hash).digest().length;\n        const byteLength = length / 8;\n        const info = webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.BufferSourceConverter.toUint8Array(params.info);\n        const PRK = crypto__WEBPACK_IMPORTED_MODULE_2___default().createHmac(hash, webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.BufferSourceConverter.toUint8Array(params.salt))\n            .update(webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.BufferSourceConverter.toUint8Array(getCryptoKey(baseKey).data))\n            .digest();\n        const blocks = [buffer__WEBPACK_IMPORTED_MODULE_1__.Buffer.alloc(0)];\n        const blockCount = Math.ceil(byteLength / hashLength) + 1;\n        for (let i = 1; i < blockCount; ++i) {\n            blocks.push(crypto__WEBPACK_IMPORTED_MODULE_2___default().createHmac(hash, PRK)\n                .update(buffer__WEBPACK_IMPORTED_MODULE_1__.Buffer.concat([blocks[i - 1], info, buffer__WEBPACK_IMPORTED_MODULE_1__.Buffer.from([i])]))\n                .digest());\n        }\n        return buffer__WEBPACK_IMPORTED_MODULE_1__.Buffer.concat(blocks).slice(0, byteLength);\n    }\n    checkCryptoKey(key, keyUsage) {\n        super.checkCryptoKey(key, keyUsage);\n        if (!(getCryptoKey(key) instanceof HkdfCryptoKey)) {\n            throw new TypeError(\"key: Is not HKDF CryptoKey\");\n        }\n    }\n}\n\nclass ShakeCrypto {\n    static digest(algorithm, data) {\n        const hash = crypto__WEBPACK_IMPORTED_MODULE_2___default().createHash(algorithm.name.toLowerCase(), { outputLength: algorithm.length })\n            .update(buffer__WEBPACK_IMPORTED_MODULE_1__.Buffer.from(data)).digest();\n        return new Uint8Array(hash).buffer;\n    }\n}\n\nclass Shake128Provider extends webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.Shake128Provider {\n    async onDigest(algorithm, data) {\n        return ShakeCrypto.digest(algorithm, data);\n    }\n}\n\nclass Shake256Provider extends webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.Shake256Provider {\n    async onDigest(algorithm, data) {\n        return ShakeCrypto.digest(algorithm, data);\n    }\n}\n\nclass SubtleCrypto extends webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.SubtleCrypto {\n    constructor() {\n        var _a;\n        super();\n        this.providers.set(new AesCbcProvider());\n        this.providers.set(new AesCtrProvider());\n        this.providers.set(new AesGcmProvider());\n        this.providers.set(new AesCmacProvider());\n        this.providers.set(new AesKwProvider());\n        this.providers.set(new AesEcbProvider());\n        const ciphers = crypto__WEBPACK_IMPORTED_MODULE_2___default().getCiphers();\n        if (ciphers.includes(\"des-cbc\")) {\n            this.providers.set(new DesCbcProvider());\n        }\n        this.providers.set(new DesEde3CbcProvider());\n        this.providers.set(new RsaSsaProvider());\n        this.providers.set(new RsaPssProvider());\n        this.providers.set(new RsaOaepProvider());\n        this.providers.set(new RsaEsProvider());\n        this.providers.set(new EcdsaProvider());\n        this.providers.set(new EcdhProvider());\n        this.providers.set(new Sha1Provider());\n        this.providers.set(new Sha256Provider());\n        this.providers.set(new Sha384Provider());\n        this.providers.set(new Sha512Provider());\n        this.providers.set(new Pbkdf2Provider());\n        this.providers.set(new HmacProvider());\n        this.providers.set(new HkdfProvider());\n        const nodeMajorVersion = (_a = /^v(\\d+)/.exec(process__WEBPACK_IMPORTED_MODULE_3__.version)) === null || _a === void 0 ? void 0 : _a[1];\n        if (nodeMajorVersion && parseInt(nodeMajorVersion, 10) >= 12) {\n            this.providers.set(new Shake128Provider());\n            this.providers.set(new Shake256Provider());\n        }\n        const hashes = crypto__WEBPACK_IMPORTED_MODULE_2___default().getHashes();\n        if (hashes.includes(\"sha3-256\")) {\n            this.providers.set(new Sha3256Provider());\n        }\n        if (hashes.includes(\"sha3-384\")) {\n            this.providers.set(new Sha3384Provider());\n        }\n        if (hashes.includes(\"sha3-512\")) {\n            this.providers.set(new Sha3512Provider());\n        }\n        if (nodeMajorVersion && parseInt(nodeMajorVersion, 10) >= 14) {\n            this.providers.set(new EdDsaProvider());\n            this.providers.set(new EcdhEsProvider());\n            this.providers.set(new Ed25519Provider());\n            this.providers.set(new X25519Provider());\n        }\n    }\n}\n\nclass Crypto extends webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.Crypto {\n    constructor() {\n        super(...arguments);\n        this.subtle = new SubtleCrypto();\n    }\n    getRandomValues(array) {\n        if (!ArrayBuffer.isView(array)) {\n            throw new TypeError(\"Failed to execute 'getRandomValues' on 'Crypto': parameter 1 is not of type 'ArrayBufferView'\");\n        }\n        const buffer = buffer__WEBPACK_IMPORTED_MODULE_1__.Buffer.from(array.buffer, array.byteOffset, array.byteLength);\n        crypto__WEBPACK_IMPORTED_MODULE_2___default().randomFillSync(buffer);\n        return array;\n    }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@peculiar/webcrypto/build/webcrypto.es.js\n");

/***/ })

};
;