"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_ssr_src_lib_onionAuction_ts";
exports.ids = ["_ssr_src_lib_onionAuction_ts"];
exports.modules = {

/***/ "(ssr)/./src/lib/onionAuction.ts":
/*!*********************************!*\
  !*** ./src/lib/onionAuction.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OnionAuction: () => (/* binding */ OnionAuction),\n/* harmony export */   onionAuctionConfigToCell: () => (/* binding */ onionAuctionConfigToCell)\n/* harmony export */ });\n/* harmony import */ var _ton_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @ton/core */ \"(ssr)/./node_modules/@ton/core/dist/index.js\");\n/* harmony import */ var _ton_core__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_ton_core__WEBPACK_IMPORTED_MODULE_0__);\n\nfunction onionAuctionConfigToCell(config) {\n    return (0,_ton_core__WEBPACK_IMPORTED_MODULE_0__.beginCell)().storeAddress(config.owner).storeUint(config.startTime, 64).storeUint(config.endTime, 64).storeCoins(config.softCap).storeCoins(config.hardCap).storeCoins(config.totalSupply).endCell();\n}\nclass OnionAuction {\n    constructor(address, init){\n        this.address = address;\n        this.init = init;\n    }\n    static createFromAddress(address) {\n        return new OnionAuction(address);\n    }\n    static createFromConfig(config, code, workchain = 0) {\n        const data = onionAuctionConfigToCell(config);\n        const init = {\n            code,\n            data\n        };\n        return new OnionAuction((0,_ton_core__WEBPACK_IMPORTED_MODULE_0__.contractAddress)(workchain, init), init);\n    }\n    async sendDeploy(provider, via, value) {\n        await provider.internal(via, {\n            value,\n            sendMode: _ton_core__WEBPACK_IMPORTED_MODULE_0__.SendMode.PAY_GAS_SEPARATELY,\n            body: (0,_ton_core__WEBPACK_IMPORTED_MODULE_0__.beginCell)().endCell()\n        });\n    }\n    async sendPurchase(provider, via, opts) {\n        await provider.internal(via, {\n            value: opts.value,\n            sendMode: _ton_core__WEBPACK_IMPORTED_MODULE_0__.SendMode.PAY_GAS_SEPARATELY,\n            body: (0,_ton_core__WEBPACK_IMPORTED_MODULE_0__.beginCell)().storeUint(0x1234, 32) // op code for Purchase\n            .storeUint(0, 64) // query_id\n            .storeCoins(opts.amount).storeUint(opts.currency, 8).endCell()\n        });\n    }\n    async sendRefund(provider, via, opts) {\n        await provider.internal(via, {\n            value: opts.value,\n            sendMode: _ton_core__WEBPACK_IMPORTED_MODULE_0__.SendMode.PAY_GAS_SEPARATELY,\n            body: (0,_ton_core__WEBPACK_IMPORTED_MODULE_0__.beginCell)().storeUint(0x5678, 32) // op code for Refund\n            .storeUint(0, 64) // query_id\n            .storeUint(opts.purchaseId, 32).endCell()\n        });\n    }\n    async getAuctionInfo(provider) {\n        const result = await provider.get('auction_info', []);\n        return {\n            startTime: result.stack.readNumber(),\n            endTime: result.stack.readNumber(),\n            softCap: result.stack.readBigNumber(),\n            hardCap: result.stack.readBigNumber(),\n            totalSupply: result.stack.readBigNumber(),\n            refundFeePercent: result.stack.readNumber()\n        };\n    }\n    async getCurrentRound(provider) {\n        const result = await provider.get('current_round', []);\n        return result.stack.readNumber();\n    }\n    async getCurrentPrice(provider) {\n        const result = await provider.get('current_price', []);\n        return result.stack.readBigNumber();\n    }\n    async getTotalRaised(provider) {\n        const result = await provider.get('total_raised', []);\n        return result.stack.readBigNumber();\n    }\n    async getTotalTokensSold(provider) {\n        const result = await provider.get('total_tokens_sold', []);\n        return result.stack.readBigNumber();\n    }\n    async getAuctionStatus(provider) {\n        const result = await provider.get('auction_status', []);\n        return result.stack.readNumber();\n    }\n    async getRemainingTokens(provider) {\n        const result = await provider.get('remaining_tokens', []);\n        return result.stack.readBigNumber();\n    }\n    async getUserPurchaseAddress(provider, user) {\n        const result = await provider.get('user_purchase_address', [\n            {\n                type: 'slice',\n                cell: (0,_ton_core__WEBPACK_IMPORTED_MODULE_0__.beginCell)().storeAddress(user).endCell()\n            }\n        ]);\n        return result.stack.readAddressOpt();\n    }\n    async isAuctionActive(provider) {\n        const result = await provider.get('is_auction_active', []);\n        return result.stack.readBoolean();\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/onionAuction.ts\n");

/***/ })

};
;