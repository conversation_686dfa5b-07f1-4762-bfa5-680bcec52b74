# Demo Server API Configuration
NEXT_PUBLIC_API_BASE_URL=http://localhost:3001/api

# TON Connect Configuration
NEXT_PUBLIC_TON_MANIFEST_URL=https://your-domain.com/tonconnect-manifest.json

# Smart Contract Configuration
NEXT_PUBLIC_AUCTION_CONTRACT_ADDRESS=EQCCjQ9e39-9eoBJdt_mXn2jftAWgNDhYaytVg4tFzEFmKTX

# TON API Configuration
# Get API key from @tonapibot on Telegram
NEXT_PUBLIC_TON_API_KEY=8b797132a14f8e75806ad0774e509b04088514045a8a539eb6bd747bf25d9493

# Development Configuration
NEXT_PUBLIC_ENVIRONMENT=development
