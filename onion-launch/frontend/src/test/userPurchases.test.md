# User Purchases Integration Test

## 测试目标
验证从链上读取用户购买记录和退款功能的完整流程。

## 测试前提条件

1. **钱包连接**：用户已连接 TON 钱包
2. **合约部署**：OnionAuction 合约已部署到测试网
3. **购买记录**：用户已有至少一笔购买记录
4. **环境配置**：正确配置了合约地址和 API 密钥

## 测试步骤

### 1. 购买记录读取测试

```typescript
// 1. 连接钱包后，useUserPurchases hook 应该自动触发数据获取
// 2. 检查是否正确计算用户购买合约地址
// 3. 验证是否正确检查合约部署状态
// 4. 如果合约已部署，读取购买记录数量
// 5. 遍历所有购买记录，获取详细信息
// 6. 检查每笔购买的退款状态
```

### 2. 数据显示测试

```typescript
// 1. 验证购买记录列表正确显示
// 2. 检查总购买代币数量计算
// 3. 检查总投资金额计算（USD）
// 4. 验证购买方法标识（direct vs signature_verified）
// 5. 对于签名验证购买，显示 nonce 信息
```

### 3. 退款功能测试

```typescript
// 1. 点击退款按钮
// 2. 验证交易请求正确构建
// 3. 检查 op code 是否为 0x31d5b5ac
// 4. 验证 purchase_id 参数正确传递
// 5. 确认交易发送到正确的用户购买合约地址
// 6. 等待交易确认后刷新数据
```

## 预期结果

### 成功场景
- ✅ 正确读取所有购买记录
- ✅ 准确计算汇总统计信息
- ✅ 退款交易成功发送
- ✅ 退款后状态正确更新

### 错误处理
- ✅ 钱包未连接时显示提示
- ✅ 合约未部署时显示空状态
- ✅ 网络错误时显示错误信息
- ✅ 退款失败时显示错误提示

## 关键代码路径

### 1. 用户购买合约地址计算
```typescript
// 通过 OnionAuction.getUserPurchaseAddress() 获取
// 地址是确定性计算的，基于 auction_address + user_address
```

### 2. 购买记录读取
```typescript
// 1. 获取 purchase_id_counter
// 2. 遍历 1 到 counter 的所有 ID
// 3. 调用 getPurchaseDetails(id) 获取详情
// 4. 调用 isRefunded(id) 检查退款状态
```

### 3. 退款交易构建
```typescript
// 消息结构：
// - op: 0x31d5b5ac (Refund)
// - purchase_id: uint32
// 发送到用户购买合约地址
```

## 注意事项

1. **Gas 费用**：退款交易需要 0.1 TON 的 gas 费用
2. **交易确认**：需要等待 3 秒后刷新数据
3. **错误处理**：网络问题或合约调用失败需要适当处理
4. **用户体验**：加载状态和错误提示要清晰明确

## 测试数据示例

```json
{
  "purchase": {
    "id": 1,
    "amount": 100000000000, // 100 TON in nanotons
    "currency": 0, // TON
    "tokens": 5000000000000, // 5000 ONION in nanotons
    "timestamp": 1705123456,
    "purchase_method": 1, // signature_verified
    "nonce": "12345678901234567890"
  },
  "isRefunded": false
}
```
