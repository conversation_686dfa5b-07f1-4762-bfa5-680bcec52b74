'use client'

import { useState, useEffect, useCallback } from 'react'
import { useTonWallet, useTonConnectUI } from '@tonconnect/ui-react'
import { Address, TonClient, TupleBuilder, beginCell } from '@ton/ton'
import { UserPurchase, PurchaseRecord } from '../../../wrappers/UserPurchase'

// Contract configuration
const AUCTION_CONTRACT_ADDRESS = process.env.NEXT_PUBLIC_AUCTION_CONTRACT_ADDRESS || 'EQC...'

// TON client configuration
const getTonClient = () => {
  return new TonClient({
    endpoint: 'https://testnet.toncenter.com/api/v2/jsonRPC',
    apiKey: process.env.NEXT_PUBLIC_TON_API_KEY
  })
}

export interface Purchase {
  id: number
  amount: number
  currency: 'TON' | 'USDT'
  tokens: number
  price: number
  timestamp: string
  status: 'completed' | 'pending' | 'refunded'
  canRefund: boolean
  nonce?: string
  purchase_method: 'direct' | 'signature_verified'
}

export interface UserPurchasesState {
  purchases: Purchase[]
  totalPurchased: number
  totalInvested: number
  isLoading: boolean
  error: string | null
  isRefunding: number | null
}

export function useUserPurchases() {
  const wallet = useTonWallet()
  const [tonConnectUI] = useTonConnectUI()
  const [state, setState] = useState<UserPurchasesState>({
    purchases: [],
    totalPurchased: 0,
    totalInvested: 0,
    isLoading: false,
    error: null,
    isRefunding: null
  })

  // Calculate user purchase contract address
  const getUserPurchaseAddress = useCallback(async (userAddress: string): Promise<Address | null> => {
    try {
      const client = getTonClient()

      // Import the auction contract to get the user purchase address
      const { OnionAuction } = await import('../lib/onionAuction')
      const auction = client.open(OnionAuction.createFromAddress(Address.parse(AUCTION_CONTRACT_ADDRESS)))

      const userPurchaseAddress = await auction.getUserPurchaseAddress(Address.parse(userAddress))
      return userPurchaseAddress
    } catch (error) {
      console.error('Error getting user purchase address:', error)
      return null
    }
  }, [])

  // Check if contract is deployed
  const isContractDeployed = useCallback(async (address: Address): Promise<boolean> => {
    try {
      const client = getTonClient()
      const account = await client.getContractState(address)
      return account.state === 'active'
    } catch (error) {
      console.error('Error checking contract deployment:', error)
      return false
    }
  }, [])

  // Fetch purchases from the blockchain
  const fetchPurchases = useCallback(async () => {
    if (!wallet?.account?.address) {
      setState(prev => ({
        ...prev,
        purchases: [],
        totalPurchased: 0,
        totalInvested: 0,
        isLoading: false,
        error: null
      }))
      return
    }

    setState(prev => ({ ...prev, isLoading: true, error: null }))

    try {
      const userPurchaseAddress = await getUserPurchaseAddress(wallet.account.address)
      if (!userPurchaseAddress) {
        throw new Error('Failed to get user purchase contract address')
      }

      // Check if the contract is deployed
      const isDeployed = await isContractDeployed(userPurchaseAddress)
      if (!isDeployed) {
        // Contract not deployed means no purchases yet
        setState(prev => ({
          ...prev,
          purchases: [],
          totalPurchased: 0,
          totalInvested: 0,
          isLoading: false,
          error: null
        }))
        return
      }

      const client = getTonClient()
      const userPurchase = client.open(UserPurchase.createFromAddress(userPurchaseAddress))
      // Get purchase count
      const purchaseCount = await userPurchase.getPurchaseIdCounter()
      
      if (purchaseCount === 0) {
        setState(prev => ({
          ...prev,
          purchases: [],
          totalPurchased: 0,
          totalInvested: 0,
          isLoading: false,
          error: null
        }))
        return
      }

      // Fetch all purchase records
      const purchases: Purchase[] = []
      let totalTokens = 0
      let totalInvestedUSD = 0

      for (let i = 1; i <= purchaseCount; i++) {
        try {
          const args = new TupleBuilder();
          args.writeNumber(i);

          const purchaseRecord = await client.runMethod(userPurchase.address, 'purchase_details', args.build())
          //const purchaseRecord = await userPurchase.getPurchaseDetails(i)
          const isRefunded = await userPurchase.isRefunded(i)
          
          if (purchaseRecord) {
            const amountInUnits = Number(purchaseRecord.amount) / ********** // Convert from nanotons
            const tokensInUnits = Number(purchaseRecord.tokens) / ********** // Convert from nanotons
            const price = amountInUnits / tokensInUnits // Price per token
            
            // Convert timestamp to readable format
            const timestamp = new Date(purchaseRecord.timestamp * 1000).toLocaleString('en-US', {
              year: 'numeric',
              month: '2-digit',
              day: '2-digit',
              hour: '2-digit',
              minute: '2-digit',
              timeZone: 'UTC',
              timeZoneName: 'short'
            })

            const purchase: Purchase = {
              id: purchaseRecord.id,
              amount: amountInUnits,
              currency: purchaseRecord.currency === 0 ? 'TON' : 'USDT',
              tokens: tokensInUnits,
              price: price,
              timestamp: timestamp,
              status: isRefunded ? 'refunded' : 'completed',
              canRefund: !isRefunded, // Can refund if not already refunded
              nonce: purchaseRecord.nonce.toString(),
              purchase_method: purchaseRecord.purchase_method === 0 ? 'direct' : 'signature_verified'
            }

            purchases.push(purchase)

            // Calculate totals (excluding refunded purchases)
            if (!isRefunded) {
              totalTokens += tokensInUnits
              // Convert to USD for total invested calculation
              const usdValue = purchaseRecord.currency === 0 ? amountInUnits * 5.5 : amountInUnits // Assume TON = $5.5
              totalInvestedUSD += usdValue
            }
          }
        } catch (error) {
          console.error(`Error fetching purchase ${i}:`, error)
          // Continue with other purchases
        }
      }

      setState(prev => ({
        ...prev,
        purchases: purchases.reverse(), // Show newest first
        totalPurchased: totalTokens,
        totalInvested: totalInvestedUSD,
        isLoading: false,
        error: null
      }))

    } catch (error) {
      console.error('Error fetching purchases:', error)
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to fetch purchases'
      }))
    }
  }, [wallet?.account?.address, getUserPurchaseAddress, isContractDeployed])

  // Refresh purchases
  const refreshPurchases = useCallback(() => {
    fetchPurchases()
  }, [fetchPurchases])

  // Request refund
  const requestRefund = useCallback(async (purchaseId: number) => {
    if (!wallet?.account?.address || !tonConnectUI) {
      throw new Error('Wallet not connected')
    }

    setState(prev => ({ ...prev, isRefunding: purchaseId }))

    try {
      const userPurchaseAddress = await getUserPurchaseAddress(wallet.account.address)
      if (!userPurchaseAddress) {
        throw new Error('Failed to get user purchase contract address')
      }

      // Create refund transaction
      const refundBody = beginCell()
        .storeUint(0x31d5b5ac, 32) // Refund op code
        .storeUint(purchaseId, 32) // purchase_id
        .endCell()

      const transactionRequest = {
        validUntil: Math.floor(Date.now() / 1000) + 600, // 10 minutes validity
        messages: [
          {
            address: userPurchaseAddress.toString(),
            amount: (0.1 * **********).toString(), // 0.1 TON for gas
            payload: Buffer.from(refundBody.toBoc()).toString('base64')
          }
        ]
      }

      const result = await tonConnectUI.sendTransaction(transactionRequest)
      console.log('Refund transaction sent:', result)

      // Refresh purchases after successful refund
      setTimeout(() => {
        refreshPurchases()
      }, 3000) // Wait 3 seconds for transaction to be processed

      return true
    } catch (error) {
      console.error('Refund failed:', error)
      throw error
    } finally {
      setState(prev => ({ ...prev, isRefunding: null }))
    }
  }, [wallet?.account?.address, tonConnectUI, getUserPurchaseAddress, refreshPurchases])

  // Auto-fetch purchases when wallet connects
  useEffect(() => {
    if (wallet?.account?.address) {
      fetchPurchases()
    }
  }, [wallet?.account?.address, fetchPurchases])

  return {
    ...state,
    refreshPurchases,
    requestRefund,
    isConnected: !!wallet?.account?.address
  }
}
