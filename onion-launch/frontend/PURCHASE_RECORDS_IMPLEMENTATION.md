# 购买记录链上数据实现

## 概述

已成功将购买记录列表从 mock 数据改为从链上读取真实数据，并实现了完整的退款功能。

## 主要变更

### 1. 创建了 UserPurchase 合约包装器

**文件**: `wrappers/UserPurchase.ts`

- 添加了所有必要的 getter 方法
- 实现了 refund 交易发送功能
- 定义了 PurchaseRecord 接口

**主要方法**:
```typescript
- getTotalPurchased(): 获取总购买代币数量
- getTotalPaid(): 获取总支付金额
- getPurchaseIdCounter(): 获取购买记录数量
- getPurchaseDetails(id): 获取购买记录详情
- isRefunded(id): 检查是否已退款
- sendRefund(purchaseId): 发送退款交易
```

### 2. 创建了 useUserPurchases Hook

**文件**: `src/hooks/useUserPurchases.ts`

**功能**:
- 自动计算用户购买合约地址
- 检查合约部署状态
- 读取所有购买记录
- 计算汇总统计信息
- 实现退款功能
- 错误处理和加载状态管理

**主要特性**:
```typescript
- 自动刷新：钱包连接时自动获取数据
- 实时计算：总购买量和总投资额
- 退款支持：完整的退款流程
- 错误处理：网络错误和合约调用失败处理
```

### 3. 更新了 UserPurchases 组件

**文件**: `src/components/UserPurchases.tsx`

**改进**:
- 移除了 mock 数据
- 使用真实的链上数据
- 添加了加载状态和错误提示
- 显示购买方法（direct vs signature_verified）
- 对签名验证购买显示 nonce 信息
- 实现了真实的退款功能

## 技术实现细节

### 用户购买合约地址计算

```typescript
// 通过 OnionAuction 合约的 getUserPurchaseAddress 方法
// 地址是确定性计算的，基于：
// contractAddress(initOf UserPurchase(auction_address, user_address))
```

### 购买记录读取流程

1. **获取购买数量**: 调用 `getPurchaseIdCounter()`
2. **遍历所有记录**: 从 1 到 counter 逐个获取
3. **获取详情**: 调用 `getPurchaseDetails(id)`
4. **检查退款状态**: 调用 `isRefunded(id)`
5. **格式化数据**: 转换为前端显示格式

### 退款交易结构

```typescript
// 消息体结构
{
  op: 0x31d5b5ac,        // Refund op code
  purchase_id: uint32    // 要退款的购买记录 ID
}

// 发送参数
{
  to: userPurchaseAddress,  // 用户购买合约地址
  value: 0.1 TON,          // Gas 费用
  payload: refundBody      // 退款消息体
}
```

## 数据格式

### PurchaseRecord (链上)
```typescript
{
  id: number;
  user: Address;
  amount: bigint;           // nanotons
  tokens: bigint;           // nanotons
  timestamp: number;        // Unix timestamp
  currency: number;         // 0=TON, 1=USDT
  purchase_method: number;  // 0=direct, 1=signature_verified
  nonce: bigint;           // 签名验证时的 nonce
}
```

### Purchase (前端显示)
```typescript
{
  id: number;
  amount: number;           // 转换为标准单位
  currency: 'TON' | 'USDT';
  tokens: number;           // 转换为标准单位
  price: number;            // 每代币价格
  timestamp: string;        // 格式化的时间
  status: 'completed' | 'pending' | 'refunded';
  canRefund: boolean;
  nonce?: string;
  purchase_method: 'direct' | 'signature_verified';
}
```

## 环境配置

### 必需的环境变量

```env
# 拍卖合约地址
NEXT_PUBLIC_AUCTION_CONTRACT_ADDRESS=EQC...

# TON API 密钥（从 @tonapibot 获取）
NEXT_PUBLIC_TON_API_KEY=your_api_key

# API 服务器地址
NEXT_PUBLIC_API_BASE_URL=http://localhost:3001/api
```

## 使用方法

### 1. 查看购买记录

1. 连接 TON 钱包
2. 组件自动加载购买记录
3. 显示汇总统计和详细列表

### 2. 申请退款

1. 在购买记录列表中找到要退款的记录
2. 点击"Refund"按钮
3. 确认钱包交易
4. 等待交易确认（约3秒）
5. 记录状态自动更新为"已退款"

## 错误处理

### 常见错误场景

1. **钱包未连接**: 显示连接提示
2. **合约未部署**: 显示"暂无购买记录"
3. **网络错误**: 显示错误信息和重试按钮
4. **退款失败**: 显示具体错误原因

### 调试信息

所有关键操作都有详细的 console.log 输出：
- 合约地址计算
- 购买记录获取
- 退款交易构建
- 错误信息

## 测试建议

1. **功能测试**: 使用测试网进行完整流程测试
2. **边界测试**: 测试无购买记录、网络错误等场景
3. **性能测试**: 测试大量购买记录的加载性能
4. **用户体验**: 验证加载状态和错误提示的用户友好性

## 后续优化

1. **缓存机制**: 添加本地缓存减少重复请求
2. **分页加载**: 对大量记录实现分页
3. **实时更新**: 监听区块链事件自动更新
4. **批量操作**: 支持批量退款功能
