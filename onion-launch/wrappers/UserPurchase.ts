import { Address, beginCell, Cell, Contract, contractAddress, ContractProvider, Sender, SendMode, TupleBuilder } from '@ton/core';

export type UserPurchaseConfig = {};

export function userPurchaseConfigToCell(config: UserPurchaseConfig): Cell {
    return beginCell().endCell();
}

// Purchase record structure matching the Tact contract
export interface PurchaseRecord {
    id: number;
    user: Address;
    amount: bigint;
    tokens: bigint;
    timestamp: number;
    currency: number; // 0=TON, 1=USDT
    purchase_method: number; // 0=direct, 1=signature_verified
    nonce: bigint;
}

export class UserPurchase implements Contract {
    constructor(readonly address: Address, readonly init?: { code: Cell; data: Cell }) {}

    static createFromAddress(address: Address) {
        return new UserPurchase(address);
    }

    static createFromConfig(config: UserPurchaseConfig, code: Cell, workchain = 0) {
        const data = userPurchaseConfigToCell(config);
        const init = { code, data };
        return new UserPurchase(contractAddress(workchain, init), init);
    }

    async sendDeploy(provider: ContractProvider, via: Sender, value: bigint) {
        await provider.internal(via, {
            value,
            sendMode: SendMode.PAY_GAS_SEPARATELY,
            body: beginCell().endCell(),
        });
    }

    // Send refund request
    async sendRefund(provider: ContractProvider, via: Sender, value: bigint, purchaseId: number) {
        const body = beginCell()
            .storeUint(0x31d5b5ac, 32) // Refund op code
            .storeUint(purchaseId, 32) // purchase_id
            .endCell();

        await provider.internal(via, {
            value,
            sendMode: SendMode.PAY_GAS_SEPARATELY,
            body,
        });
    }

    // Getter methods
    async getTotalPurchased(provider: ContractProvider): Promise<bigint> {
        const result = await provider.get('total_purchased', []);
        return result.stack.readBigNumber();
    }

    async getTotalPaid(provider: ContractProvider): Promise<bigint> {
        const result = await provider.get('total_paid', []);
        return result.stack.readBigNumber();
    }

    async getPurchaseIdCounter(provider: ContractProvider): Promise<number> {
        const result = await provider.get('purchase_id_counter', []);
        return result.stack.readNumber();
    }

    async getPurchaseDetails(provider: ContractProvider, purchaseId: number): Promise<PurchaseRecord | null> {
        try {
            const args = new TupleBuilder();
            // args.writeNumber(purchaseId);
            args.writeCell(beginCell().storeInt(purchaseId, 10).endCell()); // Placeholder for additional args if needed

            const result = await provider.get('purchase_details', args.build());

            if (result.stack.remaining === 0) {
                return null;
            }

            // Read the PurchaseRecord struct
            const record = result.stack.readTuple();

            return {
                id: record.readNumber(),
                user: record.readAddress(),
                amount: record.readBigNumber(),
                tokens: record.readBigNumber(),
                timestamp: record.readNumber(),
                currency: record.readNumber(),
                purchase_method: record.readNumber(),
                nonce: record.readBigNumber()
            };
        } catch (error) {
            console.error('Error reading purchase details:', error);
            return null;
        }
    }

    async isRefunded(provider: ContractProvider, purchaseId: number): Promise<boolean> {
        try {
            const args = new TupleBuilder();
            args.writeNumber(purchaseId);

            const result = await provider.get('is_refunded', args.build());
            return result.stack.readBoolean();
        } catch (error) {
            console.error('Error checking refund status:', error);
            return false;
        }
    }

    async getSignatureVerifiedPurchases(provider: ContractProvider): Promise<number> {
        try {
            const result = await provider.get('signature_verified_purchases', []);
            return result.stack.readNumber();
        } catch (error) {
            console.error('Error reading signature verified purchases:', error);
            return 0;
        }
    }
}
